<template>
  <div class="app-container report-page">
    <div class="page-header">
      <h1 class="page-title">我要爆料</h1>
      <p class="page-subtitle">举报违法违规行为，维护社会秩序</p>
    </div>

    <div class="report-form-container">
      <el-form
        ref="reportForm"
        :model="reportForm"
        :rules="reportRules"
        label-width="120px"
        class="report-form"
      >
        <el-form-item label="举报类型" prop="reportType">
          <el-select v-model="reportForm.reportType" placeholder="请选择举报类型" style="width: 100%">
            <el-option label="违法犬只饲养" value="illegal_dog"></el-option>
            <el-option label="环境污染" value="pollution"></el-option>
            <el-option label="噪音扰民" value="noise"></el-option>
            <el-option label="违章建筑" value="illegal_building"></el-option>
            <el-option label="其他违法行为" value="other"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="举报标题" prop="title">
          <el-input
            v-model="reportForm.title"
            placeholder="请输入举报标题"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="详细描述" prop="description">
          <el-input
            v-model="reportForm.description"
            type="textarea"
            :rows="6"
            placeholder="请详细描述举报内容，包括时间、地点、具体情况等"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="发生地点" prop="location">
          <el-input
            v-model="reportForm.location"
            placeholder="请输入具体地址"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="联系方式" prop="contact">
          <el-input
            v-model="reportForm.contact"
            placeholder="请输入您的联系方式（手机号或邮箱）"
            maxlength="50"
          ></el-input>
        </el-form-item>

        <el-form-item label="证据材料">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            multiple
            :limit="5"
            accept="image/*,video/*,.pdf,.doc,.docx"
          >
            <el-button size="small" type="primary">
              <i class="el-icon-upload"></i> 上传证据
            </el-button>
            <div slot="tip" class="el-upload__tip">
              支持图片、视频、PDF、Word文档，单个文件不超过10MB，最多5个文件
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="是否匿名">
          <el-switch
            v-model="reportForm.anonymous"
            active-text="匿名举报"
            inactive-text="实名举报"
          ></el-switch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitReport" :loading="submitting">
            提交举报
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">返回首页</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 举报须知 -->
    <div class="notice-container">
      <el-card>
        <div slot="header">
          <span>举报须知</span>
        </div>
        <div class="notice-content">
          <p>1. 请如实填写举报信息，提供真实有效的证据材料。</p>
          <p>2. 恶意举报、虚假举报将承担相应法律责任。</p>
          <p>3. 我们将严格保护举报人的个人信息和隐私。</p>
          <p>4. 举报受理后，我们将在5个工作日内进行核实处理。</p>
          <p>5. 如需了解处理进度，请保存好举报编号。</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'Report',
  data() {
    return {
      reportForm: {
        reportType: '',
        title: '',
        description: '',
        location: '',
        contact: '',
        anonymous: false,
        attachments: []
      },
      reportRules: {
        reportType: [
          { required: true, message: '请选择举报类型', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入举报标题', trigger: 'blur' },
          { min: 5, max: 50, message: '标题长度在 5 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入详细描述', trigger: 'blur' },
          { min: 20, max: 500, message: '描述长度在 20 到 500 个字符', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入发生地点', trigger: 'blur' }
        ],
        contact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^(1[3-9]\d{9}|[\w-]+@[\w-]+\.[\w-]+)$/, message: '请输入正确的手机号或邮箱', trigger: 'blur' }
        ]
      },
      fileList: [],
      submitting: false,
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  methods: {
    submitReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          this.submitting = true
          
          // 模拟提交
          setTimeout(() => {
            this.submitting = false
            this.$message.success('举报提交成功！举报编号：R' + Date.now())
            this.resetForm()
          }, 2000)
        }
      })
    },
    
    resetForm() {
      this.$refs.reportForm.resetFields()
      this.fileList = []
      this.reportForm.attachments = []
    },
    
    goBack() {
      this.$router.push('/')
    },
    
    handleUploadSuccess(response, file, fileList) {
      this.reportForm.attachments.push(response.data)
      this.$message.success('文件上传成功')
    },
    
    handleRemove(file, fileList) {
      this.fileList = fileList
      // 从attachments中移除对应文件
      const index = this.reportForm.attachments.findIndex(item => item.name === file.name)
      if (index > -1) {
        this.reportForm.attachments.splice(index, 1)
      }
    },
    
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    }
  }
}
</script>

<style scoped lang="scss">
.report-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 30px;

    .page-title {
      font-size: 28px;
      color: #333;
      margin-bottom: 10px;
    }

    .page-subtitle {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .report-form-container {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .notice-container {
    .notice-content {
      p {
        margin: 8px 0;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}

@media (max-width: 768px) {
  .report-page {
    padding: 10px;

    .report-form-container {
      padding: 20px;
    }

    .page-header .page-title {
      font-size: 24px;
    }
  }
}
</style>
