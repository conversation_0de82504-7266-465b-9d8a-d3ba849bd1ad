<template>
  <div class="app-container dog-management">


    <div class="service-grid">
      <!-- 动态生成服务模块 -->
      <div
        v-for="section in serviceSections"
        :key="section.name"
        class="service-section"
      >
        <h2 class="section-title">
          <svg-icon
            v-if="section.meta.icon && section.meta.icon !== '#'"
            :icon-class="section.meta.icon"
            class-name="section-icon"
          />
          {{ section.meta.title }}
        </h2>
        <div class="service-cards">
          <div
            v-for="item in section.children"
            :key="item.name"
            class="service-card"
            @click="navigateTo(item.path)"
          >
            <svg-icon
              v-if="item.meta.icon && item.meta.icon !== '#'"
              :icon-class="item.meta.icon"
              class="!w-[48px] !h-[48px] mr-4 text-[#666]"
            />

            <div class="card-content">
              <h3 class="card-title">{{ item.meta.title }}</h3>
              <p class="card-description">
                {{ getDescription(item.meta.title) }}
              </p>
            </div>
            <div class="card-arrow">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "DogIndex",
  data() {
    return {
      serviceSections: [],
      // 描述映射，为每个功能提供描述
      descriptionMap: {
        犬只登记申请: "办理新犬只的登记申请手续",
        犬只登记年审: "办理犬只登记年度审核",
        犬只登记注销: "办理犬只登记注销手续",
        犬只登记过户: "办理犬只所有权转移手续",
        犬牌补办: "办理犬牌遗失补办手续",
        犬牌补办医院: "查看可办理犬牌补办的医院",
        医院代办资质审核: "审核医院代办犬只登记的资质",
        医院代办资质年审核: "医院代办资质年度审核",
        年审提醒通知: "发送年审提醒通知",
        申请记录: "查看医院申请记录",
      },
    };
  },
  computed: {
    ...mapState({
      routes: (state) => state.permission.routes,
      sidebarRouters: (state) => state.permission.sidebarRouters,
    }),
  },
  mounted() {
    this.loadServiceSections();
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path);
    },
    loadServiceSections() {
      // 从侧边栏路由中找到犬类管理的路由配置
      const dogRoute = this.sidebarRouters.find((item) => item.name === "Dog");

      if (dogRoute && dogRoute.children) {
        // 过滤掉首页路由，只显示功能模块
        this.serviceSections = dogRoute.children
          .filter((child) => child.name !== "DogIndex" && !child.hidden)
          .map((child) => ({
            ...child,
            children: child.children
              ? child.children
                  .filter((subChild) => !subChild.hidden)
                  .map((subChild) => ({
                    ...subChild,
                    // 构建完整路径
                    path: `/dog/${child.path}/${subChild.path}`,
                  }))
              : [],
          }));
      }
    },
    getDescription(title) {
      return this.descriptionMap[title] || "提供相关服务功能";
    },
  },
};
</script>

<style scoped lang="scss">
.dog-management {
  padding: 20px;
  min-height: calc(100vh - 50px);
  background: linear-gradient(to bottom, #67c23a 100%, #85ce61 0%);

  .welcome-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;

    .welcome-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .service-grid {
    max-width: 1400px;
    margin: 0 auto;
  }

  .service-section {
    margin-bottom: 40px;

    .section-title {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .section-icon {
        font-size: 28px;
        margin-right: 12px;
      }
    }

    .service-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 20px;
    }
  }

  .service-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #85ce61, #67c23a);
    }

    .card-icon {
      margin-right: 16px;
      padding: 12px;
      background: linear-gradient(135deg, #85ce61, #67c23a);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 48px;
      min-height: 48px;

      .icon-large {
        font-size: 20px;
        color: white;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 6px 0;
      }

      .card-description {
        font-size: 13px;
        color: #666;
        margin: 0;
        line-height: 1.4;
      }
    }

    .card-arrow {
      color: #999;
      font-size: 16px;
      transition: all 0.3s ease;
      margin-left: 8px;
    }

    &:hover .card-arrow {
      color: #67c23a;
      transform: translateX(4px);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .welcome-section {
      margin-bottom: 30px;

      .welcome-title {
        font-size: 24px;
      }

      .welcome-subtitle {
        font-size: 14px;
      }
    }

    .service-section {
      margin-bottom: 30px;

      .section-title {
        font-size: 20px;

        .section-icon {
          font-size: 24px;
          margin-right: 8px;
        }
      }

      .service-cards {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    .service-card {
      padding: 16px;

      .card-icon {
        margin-right: 12px;
        padding: 10px;
        min-width: 40px;
        min-height: 40px;

        .icon-large {
          font-size: 18px;
        }
      }

      .card-content {
        .card-title {
          font-size: 15px;
        }

        .card-description {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
