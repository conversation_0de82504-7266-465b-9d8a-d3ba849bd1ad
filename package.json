{"name": "ygf-zlb", "version": "0.0.1", "description": "金华市城市运行管理服务-浙里办", "author": "数字金华", "license": "MIT", "scripts": {"dev": "rsbuild dev", "build": "vue-cli-service build", "build:prod": "vue-cli-service build", "build:stage": "rsbuild build --env-mode staging", "build:rsdoctor": "cross-env RSDOCTOR=true rsbuild build", "preview": "rsbuild preview"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.28.1", "clipboard": "2.0.8", "core-js": "3.37.1", "dayjs": "^1.11.13", "echarts": "5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "nprogress": "0.2.0", "quill": "2.0.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "vue": "2.7.16", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-router": "3.6.5", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xe-utils": "^3.7.8"}, "devDependencies": {"@rsbuild/core": "^1.3.7", "@rsbuild/plugin-babel": "^1.0.5", "@rsbuild/plugin-node-polyfill": "^1.3.0", "@rsbuild/plugin-sass": "^1.3.1", "@rsbuild/plugin-vue2": "^1.0.2", "@rsbuild/plugin-vue2-jsx": "^1.0.3", "@vue/cli-plugin-babel": "4.4.6", "@vue/cli-service": "4.4.6", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "connect": "3.6.6", "postcss": "^7.0.39", "postcss-pxtorem": "^5.1.1", "rsbuild-svg-sprite-loader": "^0.0.1", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}