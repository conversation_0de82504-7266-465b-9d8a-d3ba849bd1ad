<template>
  <div class="repair-sign-page">
    <el-card class="page-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="page-title">补卡申请</span>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="showApplyDialog = true">
          新建申请
        </el-button>
      </div>
      
      <div class="page-content">
        <div class="module-description">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>在这里您可以申请补签服务记录：</p>
            <ul>
              <li>申请补签忘记打卡的服务记录</li>
              <li>提交补卡申请和相关证明</li>
              <li>查看补卡申请审批状态</li>
              <li>管理历史补卡记录</li>
            </ul>
          </el-alert>
        </div>

        <div class="apply-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalApplies }}</div>
                <div class="stat-label">总申请数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">待审批</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.approved }}</div>
                <div class="stat-label">已通过</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.rejected }}</div>
                <div class="stat-label">已拒绝</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="apply-list">
          <el-table :data="applyList" style="width: 100%" v-loading="loading">
            <el-table-column prop="serviceName" label="服务项目" min-width="200" />
            <el-table-column prop="serviceDate" label="服务日期" width="120" />
            <el-table-column prop="reason" label="补卡原因" min-width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="applyTime" label="申请时间" width="180" />
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewApply(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 新建申请对话框 -->
    <el-dialog
      title="新建补卡申请"
      :visible.sync="showApplyDialog"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <el-form :model="applyForm" :rules="applyRules" ref="applyForm" label-width="100px">
        <el-form-item label="服务项目" prop="serviceName">
          <el-input v-model="applyForm.serviceName" placeholder="请输入服务项目名称" />
        </el-form-item>
        <el-form-item label="服务日期" prop="serviceDate">
          <el-date-picker
            v-model="applyForm.serviceDate"
            type="date"
            placeholder="选择服务日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="补卡原因" prop="reason">
          <el-input
            v-model="applyForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明补卡原因"
          />
        </el-form-item>
        <el-form-item label="证明材料">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :file-list="fileList"
            list-type="text"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showApplyDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitApply">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RepairSign',
  data() {
    return {
      loading: false,
      showApplyDialog: false,
      stats: {
        totalApplies: 8,
        pending: 2,
        approved: 5,
        rejected: 1
      },
      applyList: [
        {
          id: 1,
          serviceName: '社区环境清洁志愿服务',
          serviceDate: '2024-01-15',
          reason: '当天忘记打卡，有现场照片为证',
          status: 'approved',
          applyTime: '2024-01-16 09:30'
        },
        {
          id: 2,
          serviceName: '老年人关爱服务',
          serviceDate: '2024-01-20',
          reason: '手机故障无法打卡',
          status: 'pending',
          applyTime: '2024-01-21 14:20'
        }
      ],
      applyForm: {
        serviceName: '',
        serviceDate: '',
        reason: ''
      },
      applyRules: {
        serviceName: [
          { required: true, message: '请输入服务项目名称', trigger: 'blur' }
        ],
        serviceDate: [
          { required: true, message: '请选择服务日期', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入补卡原因', trigger: 'blur' }
        ]
      },
      fileList: []
    }
  },
  methods: {
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    getStatusType(status) {
      const typeMap = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return typeMap[status] || 'info'
    },
    getStatusText(status) {
      const textMap = {
        pending: '待审批',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return textMap[status] || '未知'
    },
    viewApply(apply) {
      this.$message.info(`查看申请详情：${apply.serviceName}`)
    },
    handleCloseDialog() {
      this.showApplyDialog = false
      this.$refs.applyForm.resetFields()
    },
    submitApply() {
      this.$refs.applyForm.validate((valid) => {
        if (valid) {
          this.$message.success('申请提交成功，请等待审批')
          this.showApplyDialog = false
          this.$refs.applyForm.resetFields()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.repair-sign-page {
  padding: 20px;

  .page-card {
    border-radius: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .page-content {
      .module-description {
        margin-bottom: 24px;

        .el-alert {
          border-radius: 8px;

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
              color: #606266;
            }
          }
        }
      }

      .apply-stats {
        margin-bottom: 24px;

        .stat-card {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          color: white;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .apply-list {
        .el-table {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .apply-stats {
      .el-col {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
