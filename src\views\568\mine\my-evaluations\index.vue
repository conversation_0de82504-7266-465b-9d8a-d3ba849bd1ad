<template>
  <div class="my-evaluations-page">
    <el-card class="page-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="page-title">我的评价</span>
        <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <div class="page-content">
        <div class="module-description">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>在这里您可以查看您的服务评价记录：</p>
            <ul>
              <li>查看服务对象对您的评价</li>
              <li>查看评价详情和评分</li>
              <li>统计评价数据和趋势</li>
              <li>回复评价和感谢反馈</li>
            </ul>
          </el-alert>
        </div>

        <div class="evaluation-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalEvaluations }}</div>
                <div class="stat-label">总评价数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.averageScore }}</div>
                <div class="stat-label">平均评分</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.excellentRate }}%</div>
                <div class="stat-label">优秀率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.thisMonth }}</div>
                <div class="stat-label">本月评价</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="evaluation-list">
          <el-table :data="evaluationList" style="width: 100%" v-loading="loading">
            <el-table-column prop="serviceName" label="服务项目" min-width="200" />
            <el-table-column prop="evaluator" label="评价人" width="120" />
            <el-table-column prop="score" label="评分" width="100">
              <template slot-scope="scope">
                <el-rate
                  v-model="scope.row.score"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </template>
            </el-table-column>
            <el-table-column prop="comment" label="评价内容" min-width="250" />
            <el-table-column prop="evaluationTime" label="评价时间" width="180" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewEvaluation(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MyEvaluations',
  data() {
    return {
      loading: false,
      stats: {
        totalEvaluations: 45,
        averageScore: 4.8,
        excellentRate: 92,
        thisMonth: 8
      },
      evaluationList: [
        {
          id: 1,
          serviceName: '社区环境清洁志愿服务',
          evaluator: '张女士',
          score: 5,
          comment: '志愿者服务态度很好，工作认真负责，社区环境得到了很大改善。',
          evaluationTime: '2024-01-15 18:30'
        },
        {
          id: 2,
          serviceName: '老年人关爱服务',
          evaluator: '李大爷',
          score: 5,
          comment: '小伙子很有耐心，帮助我解决了很多生活中的困难，非常感谢！',
          evaluationTime: '2024-01-20 19:15'
        },
        {
          id: 3,
          serviceName: '交通文明引导',
          evaluator: '王先生',
          score: 4,
          comment: '引导工作做得不错，提醒及时，态度友善。',
          evaluationTime: '2024-01-22 12:45'
        }
      ]
    }
  },
  methods: {
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    viewEvaluation(evaluation) {
      this.$message.info(`查看评价详情：${evaluation.serviceName}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.my-evaluations-page {
  padding: 20px;

  .page-card {
    border-radius: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .page-content {
      .module-description {
        margin-bottom: 24px;

        .el-alert {
          border-radius: 8px;

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
              color: #606266;
            }
          }
        }
      }

      .evaluation-stats {
        margin-bottom: 24px;

        .stat-card {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          color: white;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .evaluation-list {
        .el-table {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .evaluation-stats {
      .el-col {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
