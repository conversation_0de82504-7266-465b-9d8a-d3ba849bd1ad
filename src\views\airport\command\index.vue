<template>
  <div class="app-container">
    <!-- 顶部统计面板 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>设备总数</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.totalCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>在线设备</span>
          </div>
          <div class="text item">
            <span class="count success">{{ statistics.normalCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>离线设备</span>
          </div>
          <div class="text item">
            <span class="count warning">{{ statistics.offlineCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>故障设备</span>
          </div>
          <div class="text item">
            <span class="count danger">{{ statistics.faultCount || 0 }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧地图区域 -->
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>空港监控地图</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="refreshMap">刷新</el-button>
              <el-button size="mini" @click="fullScreen">全屏</el-button>
            </el-button-group>
          </div>
          <div id="mapContainer" style="height: 600px; width: 100%;">
            <!-- 这里将集成地图组件，如百度地图、高德地图等 -->
            <div class="map-placeholder">
              <i class="el-icon-location" style="font-size: 48px; color: #ccc;"></i>
              <p>地图组件加载中...</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧控制面板 -->
      <el-col :span="8">
        <!-- 设备控制面板 -->
        <el-card class="box-card mb20">
          <div slot="header" class="clearfix">
            <span>设备控制</span>
          </div>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane label="摄像头" name="camera">
              <div v-for="device in cameraDevices" :key="device.deviceId" class="device-item">
                <div class="device-info">
                  <span class="device-name">{{ device.deviceName }}</span>
                  <el-tag :type="getStatusType(device.status)" size="mini">
                    {{ getStatusText(device.status) }}
                  </el-tag>
                </div>
                <div class="device-actions">
                  <el-button size="mini" @click="viewVideo(device)">查看</el-button>
                  <el-button size="mini" @click="controlDevice(device)">控制</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="传感器" name="sensor">
              <div v-for="device in sensorDevices" :key="device.deviceId" class="device-item">
                <div class="device-info">
                  <span class="device-name">{{ device.deviceName }}</span>
                  <el-tag :type="getStatusType(device.status)" size="mini">
                    {{ getStatusText(device.status) }}
                  </el-tag>
                </div>
                <div class="device-actions">
                  <el-button size="mini" @click="viewData(device)">数据</el-button>
                  <el-button size="mini" @click="controlDevice(device)">控制</el-button>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="对讲机" name="radio">
              <div v-for="device in radioDevices" :key="device.deviceId" class="device-item">
                <div class="device-info">
                  <span class="device-name">{{ device.deviceName }}</span>
                  <el-tag :type="getStatusType(device.status)" size="mini">
                    {{ getStatusText(device.status) }}
                  </el-tag>
                </div>
                <div class="device-actions">
                  <el-button size="mini" @click="callDevice(device)">呼叫</el-button>
                  <el-button size="mini" @click="controlDevice(device)">控制</el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 快速调度面板 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>快速调度</span>
          </div>
          <el-form :model="dispatchForm" size="small">
            <el-form-item label="调度类型">
              <el-select v-model="dispatchForm.dispatchType" placeholder="请选择">
                <el-option label="指令调度" value="instruction" />
                <el-option label="通讯调度" value="communication" />
              </el-select>
            </el-form-item>
            <el-form-item label="优先级">
              <el-select v-model="dispatchForm.priorityLevel" placeholder="请选择">
                <el-option label="紧急" value="1" />
                <el-option label="重要" value="2" />
                <el-option label="一般" value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="调度内容">
              <el-input v-model="dispatchForm.content" type="textarea" rows="3" placeholder="请输入调度内容" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="sendDispatch">发送调度</el-button>
              <el-button size="small" @click="resetDispatch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- 视频查看对话框 -->
    <el-dialog title="设备视频" :visible.sync="videoDialogVisible" width="800px">
      <div class="video-container">
        <video v-if="currentDevice.streamUrl" :src="currentDevice.streamUrl" controls style="width: 100%; height: 400px;">
          您的浏览器不支持视频播放
        </video>
        <div v-else class="video-placeholder">
          <i class="el-icon-video-camera" style="font-size: 48px; color: #ccc;"></i>
          <p>暂无视频流</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDeviceStatistics, getDevicesByType } from "@/api/airport/device";

export default {
  name: "AirportCommand",
  data() {
    return {
      // 统计数据
      statistics: {},
      // 当前激活的设备类型标签
      activeTab: 'camera',
      // 设备列表
      cameraDevices: [],
      sensorDevices: [],
      radioDevices: [],
      // 调度表单
      dispatchForm: {
        dispatchType: '',
        priorityLevel: '2',
        content: ''
      },
      // 视频对话框
      videoDialogVisible: false,
      currentDevice: {}
    };
  },
  created() {
    this.loadStatistics();
    this.loadDevices();
  },
  methods: {
    // 加载统计数据
    loadStatistics() {
      getDeviceStatistics().then(response => {
        this.statistics = response.data;
      });
    },
    // 加载设备列表
    loadDevices() {
      // 加载摄像头设备
      getDevicesByType('camera').then(response => {
        this.cameraDevices = response.data;
      });
      // 加载传感器设备
      getDevicesByType('sensor').then(response => {
        this.sensorDevices = response.data;
      });
      // 加载对讲机设备
      getDevicesByType('radio').then(response => {
        this.radioDevices = response.data;
      });
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',    // 停用
        '1': 'success', // 正常
        '2': 'danger'   // 故障
      };
      return statusMap[status] || 'info';
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '停用',
        '1': '正常',
        '2': '故障'
      };
      return statusMap[status] || '未知';
    },
    // 查看视频
    viewVideo(device) {
      this.currentDevice = device;
      this.videoDialogVisible = true;
    },
    // 查看传感器数据
    viewData(device) {
      this.$message.info('查看设备数据：' + device.deviceName);
      // 这里可以打开数据查看对话框
    },
    // 呼叫对讲机
    callDevice(device) {
      this.$message.info('呼叫设备：' + device.deviceName);
      // 这里可以实现对讲机呼叫功能
    },
    // 控制设备
    controlDevice(device) {
      this.$message.info('控制设备：' + device.deviceName);
      // 这里可以打开设备控制面板
    },
    // 发送调度
    sendDispatch() {
      if (!this.dispatchForm.dispatchType || !this.dispatchForm.content) {
        this.$message.warning('请填写完整的调度信息');
        return;
      }
      this.$message.success('调度指令发送成功');
      this.resetDispatch();
    },
    // 重置调度表单
    resetDispatch() {
      this.dispatchForm = {
        dispatchType: '',
        priorityLevel: '2',
        content: ''
      };
    },
    // 刷新地图
    refreshMap() {
      this.$message.info('刷新地图');
      // 这里可以实现地图刷新逻辑
    },
    // 全屏显示
    fullScreen() {
      this.$message.info('全屏显示');
      // 这里可以实现全屏逻辑
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.count {
  font-size: 24px;
  font-weight: bold;
}

.count.success {
  color: #67C23A;
}

.count.warning {
  color: #E6A23C;
}

.count.danger {
  color: #F56C6C;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  flex: 1;
}

.device-name {
  margin-right: 8px;
}

.device-actions {
  flex-shrink: 0;
}

.video-container {
  text-align: center;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
}
</style>
