<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="filters" size="small">
      <!-- 申请类型 1.医院申请 2.执法局申请（取userid） 3.执法窗口申请 -->
      <!-- <el-form-item label="申请类型">
        <el-select v-model="filters.type" clearable placeholder="请选择">
          <el-option label="医院申请" value="1"></el-option>
          <el-option label="执法局申请" value="2"></el-option>
          <el-option label="执法窗口申请" value="3"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="医院名称">
        <el-input v-model="filters.qualifiName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="申请数量">
        <el-input v-model="filters.brandNum" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="filters.status" clearable placeholder="请选择">
          <el-option label="待审核" value="1"></el-option>
          <el-option label="已通过" value="2"></el-option>
          <el-option label="已驳回" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" icon="el-icon-search" @click="getPageList">
            查询
          </el-button>
          <el-button icon="el-icon-refresh-right" @click="resetFilters">
            重置
          </el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
      >
        新增
      </el-button>
      <!-- <el-button
        type="danger"
        icon="el-icon-delete"
        size="small"
        @click="handleBatchDelete"
      >
        删除
      </el-button>
      <span>已选 {{ selectedRows.length }} 条记录</span> -->
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column prop="qualifiName" label="医院名称"></el-table-column>
      <el-table-column
        prop="district"
        label="所属地区执法局名称"
      ></el-table-column>
      <el-table-column prop="brandNum" label="申请犬牌数量"></el-table-column>
      <el-table-column prop="userId" label="申请人"></el-table-column>
      <el-table-column prop="tel" label="联系方式"></el-table-column>
      <el-table-column prop="status" label="申请状态">
        <template slot-scope="scope">
          {{ tagStatusOption[scope.row.status] || '' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        label="申请时间"
        align="center"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="notUsedNum"
        label="剩余犬牌数"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button
            v-if="scope.row.status == 1"
            type="text"
            @click="handleAudit(scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="getPageList"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleDialogClose"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医院名称" prop="qualifiName">
              <el-input
                v-model="form.qualifiName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="所属地区执法局" prop="district">
              <el-input v-model="form.district" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col> -->
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人" prop="userRealName">
              <el-input
                v-model="form.userRealName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="tel">
              <el-input v-model="form.tel" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请数量" prop="brandNum">
              <el-input-number
                v-model="form.brandNum"
                :min="1"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择">
                <el-option label="待审核" :value="1"></el-option>
                <el-option label="已通过" :value="2"></el-option>
                <el-option label="已驳回" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="所属地区" prop="county">
              <div class="area-select">
                <span>浙江省/金华市/</span>
                <el-select v-model="form.county" placeholder="请选择">
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详细地址" prop="qualifiAddress">
              <el-input
                v-model="form.qualifiAddress"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <template v-if="dialogTitle !== '查看申请'">
          <el-button @click="handleDialogClose">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
        <template v-else>
          <el-button @click="handleDialogClose">关 闭</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { doGet, doPost } from '@/api/dog/index'
import { updateBrandStatus, saveOrUpdateBrand } from '@/api/dog/hospital'

export default {
  name: 'DogBrandApply',
  data() {
    return {
      // 搜索条件
      filters: {
        qualifiName: undefined,
        brandNum: undefined,
        status: undefined,
        type: '1',
      },
      // 表格数据
      loading: false,
      tableData: [],
      selectedRows: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 弹窗
      dialogVisible: false,
      dialogTitle: '',
      // 表单
      form: {
        qualifiName: '',
        district: '',
        userRealName: '',
        tel: '',
        brandNum: 1,
        status: '',
        area: '',
        qualifiAddress: '',
      },
      // 表单校验规则
      rules: {
        qualifiName: [
          { required: true, message: '请输入医院名称', trigger: 'blur' },
        ],
        district: [
          { required: true, message: '请输入所属执法局', trigger: 'blur' },
        ],
        userRealName: [
          { required: true, message: '请输入申请人', trigger: 'blur' },
        ],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        brandNum: [
          { required: true, message: '请输入申请数量', trigger: 'blur' },
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        area: [
          { required: true, message: '请选择所属地区', trigger: 'change' },
        ],
        qualifiAddress: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
        ],
      },
      // 区域选项
      areaOptions: [
        { label: '婺城区', value: '330702' },
        { label: '金东区', value: '330703' },
        { label: '武义县', value: '330723' },
        { label: '浦江县', value: '330726' },
        { label: '磐安县', value: '330727' },
        { label: '兰溪市', value: '330781' },
        { label: '义乌市', value: '330782' },
        { label: '东阳市', value: '330783' },
        { label: '永康市', value: '330784' },
      ],
      // 审核状态 犬牌领用
      // 状态 1.待审核 2.拒绝  3.待提交制作 30.已提交制作 4.制作通过 5.制作驳回 6.制作中 7.运输中 8.待领取 9.已领取 10.已完结
      tagStatusOption: {
        1: '待审核',
        2: '拒绝',
        3: '待提交制作',
        30: '已提交制作',
        4: '制作通过',
        5: '制作驳回',
        6: '制作中',
        7: '运输中',
        8: '待领取',
        9: '已领取',
        10: '已完结',
      },
      deptList: [],
    }
  },
  created() {
    this.getPageList()
    this.getdeptList()
  },
  methods: {
    getdeptList() {
      doPost('/dept/getAllList').then((res) => {
        console.log(res)
        this.deptList = res.filter((item) => item.level === 2)
      })
    },
    formatCounty(row, column) {
      console.log(row)
      for (const i in this.areaOptions) {
        if (row.county === this.areaOptions[i].value) {
          return '浙江省/金华市/' + this.areaOptions[i].label
        }
      }
    },
    // 获取列表数据
    async getPageList() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          ...this.filters,
        }
        const res = await doGet('/brandApply/getPageList', params)
        this.tableData = res.list
        this.total = res.total
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },

    // 重置搜索条件
    resetFilters() {
      this.filters = {
        qualifiName: undefined,
        brandNum: undefined,
        status: undefined,
        type: '1',
      }
      this.getPageList()
    },

    // 表格选择
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增申请'
      this.dialogVisible = true
      this.form = {
        qualifiName: '',
        district: '',
        userRealName: '',
        tel: '',
        brandNum: 1,
        status: '',
        area: '',
        qualifiAddress: '',
      }
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑申请'
      this.dialogVisible = true
      doPost('/brandApply/getById', { id: row.id }).then((res) => {
        this.form = { ...res }
      })
    },

    // 查看
    handleView(row) {
      this.dialogTitle = '查看申请'
      this.dialogVisible = true
      doPost('/brandApply/getById', { id: row.id }).then((res) => {
        this.form = { ...res }
        // 设置表单为只读
        this.$nextTick(() => {
          if (this.$refs.form) {
            const formItems =
              this.$refs.form.$el.querySelectorAll('.el-form-item')
            formItems.forEach((item) => {
              const input = item.querySelector('.el-input__inner')
              if (input) {
                input.setAttribute('disabled', true)
              }
              const select = item.querySelector('.el-select')
              if (select) {
                select.setAttribute('disabled', true)
              }
              const inputNumber = item.querySelector('.el-input-number')
              if (inputNumber) {
                inputNumber.setAttribute('disabled', true)
              }
            })
          }
        })
      })
    },

    // 审核
    async handleAudit(row) {
      try {
        await this.$confirm('确认审核该记录?', '提示', {
          distinguishCancelAndClose: true,
          cancelButtonText: '拒绝',
          confirmButtonText: '通过',
          type: 'warning',
        }).then(() => {
          updateBrandStatus({ id: row.id, status: 10 }).then(() => {
            this.$message.success('审核通过')
            this.getPageList()
          })
        })
      } catch (action) {
        if (action === 'cancel') {
          updateBrandStatus({ id: row.id, status: 2 }).then(() => {
            this.$message.warning('拒绝通过')
            this.getPageList()
          })
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        return this.$message.warning('请选择要删除的记录')
      }
      try {
        await this.$confirm(
          `确认删除选中的 ${this.selectedRows.length} 条记录?`,
          '提示',
          {
            type: 'warning',
          }
        )
        const ids = this.selectedRows.map((row) => row.id)
        await this.$api.dogBrand.batchDeleteApply(ids)
        this.$message.success('删除成功')
        this.getPageList()
      } catch (error) {
        console.error(error)
      }
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPageList()
    },

    // 关闭弹窗
    handleDialogClose() {
      this.$refs.form?.resetFields()
      this.dialogVisible = false
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        if (this.form.id) {
          await saveOrUpdateBrand(this.form)
        } else {
          await saveOrUpdateBrand(this.form)
        }
        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getPageList()
      } catch (error) {
        console.error(error)
      }
    },
  },
}
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
}

.area-select {
  display: flex;
  align-items: center;
}

.area-select span {
  margin-right: 10px;
}
</style>
