<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="568志愿者招募"> </el-page-header>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <!-- 志愿服务介绍卡片 -->
      <el-card
        class="info-card"
        shadow="none"
        :body-style="{
          padding: 0,
        }"
      >
        <div class="info-content">
          <div class="info-item">
            <h4 class="info-title">568简介</h4>
            <p class="info-text">
              568（我来帮）志愿服务队隶属于金华市综合行政执法局站前管理中心，推行"365天志愿服务不落幕"，建设1个志愿站，位于铁路金华站出站口，是金华市志愿服务站示范站点之一。服务队为铁路金华站来往旅客提供家服务、家温暖、家安全。2021年推出"568助老助残直通车"志愿服务项目，为行动不便残疾人、老年人、提供点到点精准服务。
            </p>
          </div>

          <div class="info-item">
            <h4 class="info-title">招募要求</h4>
            <p class="info-text">
              具备相应民事行为能力（未成年人需有监护人或家长陪同）和志愿服务能力，且应身心健康，建议60周岁以下，无不良行为和信用记录，热爱公益事业，遵守志愿者行为规范，熟悉金华火车站的热心市民。
            </p>
          </div>

          <div class="info-item">
            <h4 class="info-title">服务内容</h4>
            <p class="info-text">
              铁路金华站站前区域的咨询问询、交通引导、文明劝导、疫情防控、便民服务等10+X服务。
            </p>
          </div>

          <div class="info-item">
            <h4 class="info-title">服务时间段</h4>
            <p class="info-text">全天分三个时间段，</p>
            <p class="info-text">
              夏令时：上午8：30-11：30，中午11：30-14：30，下午14:30-17：30；
            </p>
            <p class="info-text">
              冬令时：上午8：30-11：30，中午11：30-14：00，下午14:00-17：00。
            </p>
          </div>
        </div>
      </el-card>

      <!-- 状态提示区域 -->
      <div class="status-section">
        <el-alert
          v-if="beVol"
          title="温馨提示：您已成为568志愿者一员"
          type="success"
          :closable="false"
          show-icon
          class="status-alert"
        >
        </el-alert>

        <el-alert
          v-else-if="submiting"
          title="您已提交申请，可到'我的-审批事项'中查看进度"
          type="info"
          :closable="false"
          show-icon
          class="status-alert"
        >
        </el-alert>

        <!-- 操作按钮区域 -->
        <div v-else class="action-section">
          <el-button @click="goBack"> 我再想想 </el-button>

          <el-button type="primary" @click="$router.push('/568/join')">
            同意加入
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listApprove } from "@/api/568/mine";

export default {
  name: "JoinUs",
  components: {},
  props: {},
  data() {
    return {
      beVol: false,
      submiting: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.showSubmit();
  },
  mounted() {},
  methods: {
    // 返回上一页
    goBack() {
      this.$router.back();
    },

    async showSubmit() {
      if (this.vuex_user_role != 0) {
        // this.beVol = true;
        // this.submiting = false;
        return;
      }

      // 如果申请成为志愿者过程中，跳进度页面
      const result = await listApprove({
        volUserId: this.vuex_user_vol_id,
        businessType: 1,
        status: 2,
      }).catch(() => {
        // 错误处理
        this.$toast.fail({
          message: "网络异常，请重试！",
        });
      });
      if (result.rows.length != 0) {
        this.beVol = false;
        this.submiting = true;
        return;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  max-width: 1200px;
  margin: 20px auto 0;
  min-height: calc(100vh - 50px);
}

// 页面头部样式
::v-deep .el-page-header {
  padding: 0;
  margin-bottom: 30px;

  .el-page-header__content {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .el-page-header__left {
    .el-page-header__back {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 内容容器
.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

// 信息卡片
.info-card {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  border: none !important;

  .card-header {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    color: white;
    padding: 0;

    .card-title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .info-content {
    padding: 20px;

    .info-item {
      margin-bottom: 25px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 10px;
        padding-left: 12px;
        border-left: 4px solid #409eff;
      }

      .info-text {
        font-size: 14px;
        line-height: 1.8;
        color: #606266;
        text-align: justify;
        text-indent: 2em;
        margin: 0;
      }
    }
  }
}

// 状态提示区域
.status-section {
  .status-alert {
    margin-bottom: 30px;
    border-radius: 8px;

    ::v-deep .el-alert__title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 操作按钮区域
  .action-section {
    text-align: center;
    padding: 40px 0;

    .action-btn {
      width: 100%;
      height: 50px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 25px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 15px;
  }

  .content-container {
    max-width: 100%;
  }

  .info-card {
    .info-content {
      padding: 15px;

      .info-item {
        margin-bottom: 20px;

        .info-title {
          font-size: 15px;
        }

        .info-text {
          font-size: 13px;
          line-height: 1.6;
        }
      }
    }
  }

  .action-section {
    .el-col {
      margin-bottom: 15px;
    }
  }

  // 移动端 PageHeader 样式
  ::v-deep .el-page-header {
    .el-page-header__content {
      font-size: 20px;
    }
  }
}

// 卡片悬停效果
.info-card:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

// 按钮悬停效果
.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}
</style>
