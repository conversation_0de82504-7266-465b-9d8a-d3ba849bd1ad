<template>
  <div class="review-apply-page">
    <el-card class="page-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="page-title">审批事项</span>
        <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <div class="page-content">
        <div class="module-description">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>在这里您可以处理待审批事项：</p>
            <ul>
              <li>查看需要您审批的申请事项</li>
              <li>审批志愿者补卡申请</li>
              <li>审批服务项目申请</li>
              <li>查看审批历史记录</li>
            </ul>
          </el-alert>
        </div>

        <div class="review-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalReviews }}</div>
                <div class="stat-label">总审批数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">待审批</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.approved }}</div>
                <div class="stat-label">已通过</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.rejected }}</div>
                <div class="stat-label">已拒绝</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="filter-section">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="申请类型">
              <el-select v-model="filterForm.type" placeholder="请选择" clearable>
                <el-option label="补卡申请" value="repair" />
                <el-option label="服务申请" value="service" />
                <el-option label="其他申请" value="other" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="filterForm.status" placeholder="请选择" clearable>
                <el-option label="待审批" value="pending" />
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">查询</el-button>
              <el-button @click="resetFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="review-list">
          <el-table :data="reviewList" style="width: 100%" v-loading="loading">
            <el-table-column prop="applicant" label="申请人" width="120" />
            <el-table-column prop="type" label="申请类型" width="120">
              <template slot-scope="scope">
                <el-tag :type="getTypeColor(scope.row.type)">
                  {{ getTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="申请标题" min-width="200" />
            <el-table-column prop="content" label="申请内容" min-width="250" />
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="applyTime" label="申请时间" width="180" />
            <el-table-column label="操作" width="200">
              <template slot-scope="scope">
                <el-button 
                  type="text" 
                  size="small" 
                  @click="viewDetail(scope.row)"
                >
                  查看详情
                </el-button>
                <el-button 
                  v-if="scope.row.status === 'pending'"
                  type="text" 
                  size="small" 
                  @click="approveApply(scope.row)"
                >
                  通过
                </el-button>
                <el-button 
                  v-if="scope.row.status === 'pending'"
                  type="text" 
                  size="small" 
                  @click="rejectApply(scope.row)"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ReviewApply',
  data() {
    return {
      loading: false,
      stats: {
        totalReviews: 25,
        pending: 5,
        approved: 18,
        rejected: 2
      },
      filterForm: {
        type: '',
        status: ''
      },
      reviewList: [
        {
          id: 1,
          applicant: '张三',
          type: 'repair',
          title: '社区清洁服务补卡申请',
          content: '因手机故障未能及时打卡，申请补签1月15日的社区清洁服务记录',
          status: 'pending',
          applyTime: '2024-01-16 09:30'
        },
        {
          id: 2,
          applicant: '李四',
          type: 'service',
          title: '新增助老服务项目申请',
          content: '申请新增针对独居老人的定期探访服务项目',
          status: 'pending',
          applyTime: '2024-01-18 14:20'
        },
        {
          id: 3,
          applicant: '王五',
          type: 'repair',
          title: '交通引导服务补卡申请',
          content: '因突发事件提前离开，申请补签1月20日的交通引导服务记录',
          status: 'approved',
          applyTime: '2024-01-21 10:15'
        }
      ]
    }
  },
  methods: {
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    getTypeColor(type) {
      const colorMap = {
        repair: 'warning',
        service: 'primary',
        other: 'info'
      }
      return colorMap[type] || 'info'
    },
    getTypeText(type) {
      const textMap = {
        repair: '补卡申请',
        service: '服务申请',
        other: '其他申请'
      }
      return textMap[type] || '未知'
    },
    getStatusType(status) {
      const typeMap = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return typeMap[status] || 'info'
    },
    getStatusText(status) {
      const textMap = {
        pending: '待审批',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return textMap[status] || '未知'
    },
    handleFilter() {
      this.$message.info('执行筛选操作')
    },
    resetFilter() {
      this.filterForm = {
        type: '',
        status: ''
      }
      this.$message.info('筛选条件已重置')
    },
    viewDetail(item) {
      this.$message.info(`查看详情：${item.title}`)
    },
    approveApply(item) {
      this.$confirm('确认通过此申请？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        item.status = 'approved'
        this.$message.success('审批通过')
      })
    },
    rejectApply(item) {
      this.$confirm('确认拒绝此申请？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        item.status = 'rejected'
        this.$message.success('审批拒绝')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.review-apply-page {
  padding: 20px;

  .page-card {
    border-radius: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .page-content {
      .module-description {
        margin-bottom: 24px;

        .el-alert {
          border-radius: 8px;

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
              color: #606266;
            }
          }
        }
      }

      .review-stats {
        margin-bottom: 24px;

        .stat-card {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          color: white;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .filter-section {
        margin-bottom: 20px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;

        .filter-form {
          margin: 0;
        }
      }

      .review-list {
        .el-table {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .review-stats {
      .el-col {
        margin-bottom: 12px;
      }
    }

    .filter-section {
      .filter-form {
        .el-form-item {
          display: block;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>
