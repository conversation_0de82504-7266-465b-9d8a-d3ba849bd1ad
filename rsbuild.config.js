import { defineConfig, loadEnv } from "@rsbuild/core";
import { pluginBabel } from "@rsbuild/plugin-babel";
import { pluginVue2 } from "@rsbuild/plugin-vue2";
import { pluginVue2Jsx } from "@rsbuild/plugin-vue2-jsx";
import { pluginSass } from "@rsbuild/plugin-sass";
import { pluginNodePolyfill } from "@rsbuild/plugin-node-polyfill";
import { pluginSvgSpriteLoader } from "rsbuild-svg-sprite-loader";
import CompressionWebpackPlugin from "compression-webpack-plugin";

import path from "path";

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = "金华市城市运行管理服务-浙里办";
const port = process.env.port || process.env.npm_config_port || 8180;

const { publicVars } = loadEnv({ prefixes: ["VUE_APP_"] });

const target1 = `http://************`; //线上
// const target1 = 'http://172.16.10.98:9000' //俊伟
// const target1 = 'http://172.16.10.107:9000' //盛名
// const target1 = 'http://172.16.10.167:9000'

export default defineConfig({
  dev: {
    lazyCompilation: true,
  },
  plugins: [
    pluginBabel(),
    pluginVue2(),
    pluginVue2Jsx(),
    pluginSass(),
    pluginNodePolyfill(),
    pluginSvgSpriteLoader({
      path: path.join(__dirname, "./src/assets/icons/svg"),
      symbolId: "icon-[name]",
    }),
  ],
  source: {
    define: publicVars,
    alias: {
      "@": resolve("src"),
    },
    // 指定入口文件
    entry: {
      index: "./src/main.js",
    },
  },
  output: {
    // 根据环境设置不同的publicPath
    assetPrefix: process.env.VUE_APP_YGF_BASE_URL,
  },

  server: {
    // host: "0.0.0.0",
    port: port,
    open: true,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: target1,
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: process.env.VUE_APP_BASE_API,
        },
      },
      "/file/uploadPath": {
        target: target1,
        changeOrigin: true,
        pathRewrite: {
          ["^/file/uploadPath"]: "/file/uploadPath",
        },
      },
      // [process.env.VUE_APP_BASE_API + '/sysUploadFile/downloadLocalFile']: {
      //   target: target1,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     ['^' +
      //     process.env.VUE_APP_BASE_API +
      //     '/sysUploadFile/downloadLocalFile']:
      //       'http://************/prod-api/sysUploadFile/downloadLocalFile',
      //   },
      // },
    },
    disableHostCheck: true,
  },
  performance: {
    removeConsole: process.env.NODE_ENV === "production",
    removeMomentLocale: true,
    chunkSplit: {
      strategy: "split-by-size",
      minSize: 30000,
      maxSize: 90000,
    },
  },
  moduleFederation: {},
  security: {
    nonce: "",
  },
  html: {
    template: "./public/index.html",
    title: "金华市城市运行管理服务-浙里办",
  },

  tools: {
    rspack: {
      experiments: {
        incremental: process.env.NODE_ENV === "development",
      },
      ignoreWarnings: [/ModuleWarning: Deprecation Warning/],
      plugins: [
        new CompressionWebpackPlugin({
          test: /\.js$|\.html$|\.css$/u,
          threshold: 4096, // 超过 4kb 压缩
        }),
      ],
    },
  },
});
