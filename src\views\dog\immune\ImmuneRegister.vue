<template>
  <div class="app-container">
    <!--工具条-->
    <el-col :span="24" class="form-line" style="padding-bottom: 0px">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬牌号">
          <el-input
            v-model="filters.petNum"
            placeholder="请输入犬牌号"
          ></el-input>
        </el-form-item>
        <el-form-item label="身份证号">
          <el-input
            v-model="filters.petIdCard"
            placeholder="请输入身份证信息"
          ></el-input>
        </el-form-item>

        <el-form-item label="犬牌状态">
          <el-select
            v-model="filters.applyStatus"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in applyStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input
            v-model="filters.tel"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>

        <div style="float: right; display: flex">
          <el-button
            type="primary"
            size="small"
            plain
            style="margin-right: 10px"
            @click="changeSearchType"
          >
            筛选
            <i v-if="searchType2 == 1" class="el-icon-arrow-down el-icon"></i>
            <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
          </el-button>

          <div>
            <el-button-group>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-search"
                @click="handleSearch"
              >
                查询
              </el-button>
              <el-button
                size="small"
                icon="el-icon-refresh-right"
                @click="resetParameter"
              >
                重置
              </el-button>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addForm"
              >
                新增
              </el-button>
            </el-button-group>
          </div>
        </div>
      </el-form>
    </el-col>
    <el-col v-show="searchType2 == 2" :span="24" class="form-line">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="免疫状态">
          <el-select
            v-model="filters.status"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in statuss"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="饲主名称">
          <el-input
            v-model="filters.ownerName"
            placeholder="请输入饲主名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="宠物名称">
          <el-input
            v-model="filters.petName"
            placeholder="请输入宠物名称"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="user.userType != '3'" label="所在地区">
          <el-select
            v-model="filters.petDept"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in areaOptions"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="注射日期">
          <el-date-picker
            v-model="filters.time"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </el-col>

    <!--列表-->

    <el-table
      v-loading="loading"
      :data="data"
      highlight-current-row
      style="width: 100%"
      stripe
    >
      <el-table-column
        prop="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="petNum"
        label="犬牌号"
        width="140"
        sortable
      ></el-table-column>
      <el-table-column
        prop="petIdCard"
        width="185"
        label="身份证"
        sortable
      ></el-table-column>
      <el-table-column
        prop="ownerName"
        label="饲主姓名"
        sortable
      ></el-table-column>
      <el-table-column
        prop="tel"
        label="联系电话"
        width="120"
        sortable
      ></el-table-column>
      <el-table-column
        prop="petName"
        label="宠物名称"
        sortable
      ></el-table-column>
      <el-table-column
        prop="petDept"
        label="所在地区"
        :formatter="formatDept"
        sortable
      ></el-table-column>
      <el-table-column label="免疫医院" sortable>
        <template slot-scope="scope">
          {{
            scope.row.immuneRegister == null
              ? ''
              : scope.row.immuneRegister.hospital
          }}
        </template>
      </el-table-column>
      <el-table-column label="注射日期" sortable>
        <template slot-scope="scope">
          {{
            scope.row.immuneRegister == null
              ? ''
              : scope.row.immuneRegister.injectionDate
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="endDate"
        label="有效期至"
        sortable
      ></el-table-column>
      <el-table-column label="免疫状态" sortable align="center" width="140">
        <template slot-scope="scope">
          <el-tag :type="getImmuneStatusType(scope.row)">
            {{ formatStatus(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="犬牌状态" sortable align="center" width="140">
        <template slot-scope="scope">
          <el-tag :type="getNumStatusType(scope.row)">
            {{ formatNumStatus(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <!--                    <el-table-column label="办理方式" :formatter="formatAboutMake" sortable>-->
      <!--                    </el-table-column>-->
      <el-table-column align="center" label="操作" fixed="right" width="150">
        <template slot-scope="scope">
          <el-button
            class="btn-text-pd0"
            type="text"
            @click="handleEdit(1, scope.row.id, scope.row.petIdCard)"
          >
            查看
          </el-button>
          <el-button
            v-if="scope.row.applyStatus == '1' || scope.row.applyStatus == '4'"
            class="btn-text-pd0"
            type="text"
            @click="handleEdit(2, scope.row.id, scope.row.petIdCard)"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.applyStatus != '2'"
            class="btn-tex t-pd0"
            type="text"
            @click="handleImmnue(scope.row.id)"
          >
            免疫
          </el-button>
          <!-- v-hasPermi="['dog:register:apply']" -->
          <el-button
            v-if="
              scope.row.immuneId &&
              scope.row.petCount == '0' &&
              scope.row.applyStatus != '3'
            "
            class="btn-text-pd0"
            type="text"
            @click="
              handleApply(scope.row.id, scope.row.immuneId, scope.row.petNum)
            "
          >
            犬牌申请
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--工具条-->
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      :title="title"
      :visible.sync="formVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="宠物信息" name="first">
          <el-form
            ref="form"
            :model="form"
            label-width="150px"
            :rules="formRules"
          >
            <div style="padding-top: 10px">
              <!-- 饲主信息 -->
              <div class="section-title">饲主信息</div>
              <el-divider></el-divider>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="饲主姓名" prop="ownerName">
                    <el-input
                      v-model="form.ownerName"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入饲主姓名"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证" prop="petIdCard">
                    <el-input
                      v-model="form.petIdCard"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入饲主身份证号"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="户籍地址" prop="ownerAddress">
                    <el-input
                      v-model="form.ownerAddress"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入户籍地址"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="tel">
                    <el-input
                      v-model="form.tel"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入饲主联系电话"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="身份证照片（正面）" prop="posiCard">
                    <el-upload
                      :headers="headers"
                      class="avatar-uploader"
                      accept="image/jpeg,image/png"
                      :show-file-list="false"
                      :disabled="disabled"
                      :action="uploadUrl"
                      :on-success="
                        (response, file) => {
                          return handleSuccess(response, file, 'posiCard')
                        }
                      "
                      name="multipartFile"
                      :before-upload="beforeAvatarUpload"
                    >
                      <el-image
                        v-if="posiCardPath"
                        :src="form.posiCard"
                        class="avatar"
                        :preview-src-list="[form.posiCard]"
                      />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证照片（反面）" prop="sideCard">
                    <el-upload
                      :headers="headers"
                      class="avatar-uploader"
                      accept="image/jpeg,image/png"
                      :show-file-list="false"
                      :disabled="disabled"
                      :action="uploadUrl"
                      :on-success="
                        (response, file) => {
                          return handleSuccess(response, file, 'sideCard')
                        }
                      "
                      name="multipartFile"
                      :before-upload="beforeAvatarUpload"
                    >
                      <el-image
                        v-if="sideCardPath"
                        :src="form.sideCard"
                        class="avatar"
                        :preview-src-list="[form.sideCard]"
                      />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="户口本照片" prop="residence">
                    <el-upload
                      :headers="headers"
                      accept="image/jpeg,image/png"
                      :class="[!readonly ? '' : 'hidden-Btn']"
                      :disabled="readonly"
                      list-type="picture-card"
                      :action="uploadUrl"
                      :on-change="handleResidenceChange"
                      :file-list="residenceImgList"
                      name="multipartFile"
                      :multiple="true"
                      :before-upload="beforeAvatarUpload"
                    >
                      <i slot="default" class="el-icon-plus"></i>
                      <div slot="file" slot-scope="{ file }">
                        <el-image
                          class="el-upload-list__item-thumbnail"
                          :src="file.url"
                          style="
                            border-radius: 6px;
                            width: 148px;
                            height: 148px;
                          "
                          :preview-src-list="[file.url]"
                        />
                        <span
                          v-if="!disabled"
                          class="el-upload-list__item-actions"
                        >
                          <span
                            class="el-upload-list__item-delete"
                            @click="handleResidenceRemove(file)"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="房产证/租赁合同照片" prop="property">
                    <el-upload
                      :headers="headers"
                      accept="image/jpeg,image/png"
                      :class="[!readonly ? '' : 'hidden-Btn']"
                      :disabled="readonly"
                      list-type="picture-card"
                      :action="uploadUrl"
                      :on-change="handlePropertyChange"
                      :file-list="propertyImgList"
                      name="multipartFile"
                      :multiple="true"
                      :before-upload="beforeAvatarUpload"
                    >
                      <i slot="default" class="el-icon-plus"></i>
                      <div slot="file" slot-scope="{ file }">
                        <el-image
                          class="el-upload-list__item-thumbnail"
                          :src="file.url"
                          style="
                            border-radius: 6px;
                            width: 148px;
                            height: 148px;
                          "
                          :preview-src-list="[file.url]"
                        />
                        <span
                          v-if="!disabled"
                          class="el-upload-list__item-actions"
                        >
                          <span
                            class="el-upload-list__item-delete"
                            @click="handlePropertyRemove(file)"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 宠物信息 -->
              <div class="section-title">宠物信息</div>
              <el-divider></el-divider>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="宠物照片（正面）" prop="petImgZ">
                    <el-upload
                      :headers="headers"
                      class="avatar-uploader"
                      :show-file-list="false"
                      accept="image/jpeg,image/png"
                      :disabled="disabled"
                      :action="uploadUrl"
                      :on-success="
                        (response, file) => {
                          return handleSuccess(response, file, 'petImgZ')
                        }
                      "
                      name="multipartFile"
                      :before-upload="beforeAvatarUpload"
                    >
                      <el-image
                        v-if="petImgZPath"
                        :src="form.petImgZ"
                        class="avatar"
                        :preview-src-list="[form.petImgZ]"
                      />
                      <div v-else>
                        <img
                          class="avatar"
                          src="../../../assets/dog/img-dog1.png"
                          alt=""
                        />
                        <img
                          style="position: absolute; top: 0; left: 0"
                          class="avatar"
                          src="../../../assets/dog/img-dog3.png"
                          alt=""
                        />
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物照片（侧面）" prop="petImgF">
                    <el-upload
                      :headers="headers"
                      class="avatar-uploader"
                      :show-file-list="false"
                      accept="image/jpeg,image/png"
                      :disabled="disabled"
                      :action="uploadUrl"
                      :on-success="
                        (response, file) => {
                          return handleSuccess(response, file, 'petImgF')
                        }
                      "
                      name="multipartFile"
                      :before-upload="beforeAvatarUpload"
                    >
                      <el-image
                        v-if="petImgFPath"
                        :src="form.petImgF"
                        class="avatar"
                        :preview-src-list="[form.petImgF]"
                      />
                      <div v-else>
                        <img
                          class="avatar"
                          src="../../../assets/dog/img-dog2.png"
                          alt=""
                        />
                        <img
                          style="position: absolute; top: 0; left: 0"
                          class="avatar"
                          src="../../../assets/dog/img-dog3.png"
                          alt=""
                        />
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="宠物名字" prop="petName">
                    <el-input
                      v-model="form.petName"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入宠物名称"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物性别" prop="petSex">
                    <el-select
                      v-model="form.petSex"
                      :disabled="disabled"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petSexData"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物类型" prop="petType">
                    <el-select
                      v-model="form.petType"
                      :disabled="disabled"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="changeVariOneChange($event, true)"
                    >
                      <el-option
                        v-for="item in petVariOneList"
                        :key="item.dictKey"
                        :label="item.name"
                        :value="item.dictKey"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物品种" prop="petVarieties">
                    <el-input
                      v-if="form.petType == '3'"
                      v-model="form.petVarieties"
                      type="input"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入品种名称"
                    ></el-input>
                    <el-select
                      v-else
                      v-model="form.petVarieties"
                      :disabled="disabled"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petVarietiesArray"
                        :key="item.dictKey"
                        :label="item.name"
                        :value="item.dictKey"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="form.petVarieties === '107'" :span="12">
                  <el-form-item label="其他品种" prop="otherVarieties">
                    <el-input
                      v-model="form.otherVarieties"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入其他品种名称"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物毛色" prop="petHair">
                    <el-select
                      v-model="form.petHair"
                      :disabled="disabled"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petHairData"
                        :key="item.dictKey"
                        :label="item.name"
                        :value="item.dictKey"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物年龄" prop="petAge">
                    <el-input
                      v-model="form.petAge"
                      type="number"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入宠物年龄"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="饲养日期" prop="raiseDate">
                    <el-date-picker
                      v-model="form.raiseDate"
                      style="width: 100%"
                      :readonly="disabled"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="养宠地址" prop="petDept">
                    <div style="display: flex; align-items: center">
                      <div style="width: 110px">浙江省/金华市/</div>
                      <el-cascader
                        :key="modalKey"
                        v-model="area"
                        style="flex: 1"
                        :disabled="disabled"
                        :options="areaOptions"
                        :props="cateProps"
                        clearable
                        @change="handlePetDept"
                      ></el-cascader>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="" prop="petAddress">
                    <el-input
                      v-model="form.petAddress"
                      type="textarea"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入详细地址"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="免疫证" prop="immuneCard">
                    <el-input
                      v-model="form.immuneCard"
                      readonly
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="注射医院" prop="hospitalId">
                    <el-select
                      v-model="form.hospitalId"
                      :disabled="
                        readonly || user.userType == '2' || type == '2'
                      "
                      placeholder="请选择"
                      style="width: 100%"
                      filterable
                      @change="hospitalChange"
                    >
                      <el-option
                        v-for="item in hospitalList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="注射医生" prop="doctor">
                    <el-input
                      v-model="form.doctor"
                      :readonly="readonly || type == '2'"
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="疫苗品牌" prop="vaccineBrand">
                    <el-select
                      v-model="form.vaccineBrand"
                      :disabled="readonly || type == '2'"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="vaccineBrandChange"
                    >
                      <el-option
                        v-for="item in vaccineBrandArray"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="注射日期" prop="injectionDate">
                    <el-date-picker
                      v-model="form.injectionDate"
                      style="width: 100%"
                      :readonly="readonly || type == '2'"
                      value-format="yyyy-MM-dd"
                      type="date"
                      :picker-options="pickerOptions"
                      placeholder="选择日期"
                      @change="dateChange"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="到期日期" prop="injectionEnddate">
                    <el-date-picker
                      v-model="form.injectionEnddate"
                      style="width: 100%"
                      :readonly="readonly || type == '2'"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="疫苗批次" prop="vaccineBatch">
                    <el-input
                      v-model="form.vaccineBatch"
                      :readonly="readonly"
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col v-if="readonly && form.expresType != '0'" :span="12">
                  <el-form-item label="领取方式" prop="expresType">
                    <el-radio
                      v-model="form.expresType"
                      label="1"
                      :disabled="readonly"
                    >
                      自取
                    </el-radio>
                    <!--                                    <el-radio v-model="form.expresType" label="2" :disabled="readonly">快递</el-radio>-->
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col v-if="readonly && form.expresType != '0'" :span="24">
                  <el-form-item label="领取地址" prop="expresAddress">
                    <el-input
                      v-model="form.expresAddress"
                      type="textarea"
                      :readonly="readonly"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <div v-if="readonly">
                <!--                            <el-row :gutter="20">-->
                <!--                                <el-col :span="24">-->
                <!--                                    <el-form-item label="状态">-->
                <!--                                        <el-input type="text" readonly :model="formatStatus(form)"></el-input>-->
                <!--                                    </el-form-item>-->
                <!--                                </el-col>-->
                <!--                            </el-row>-->
                <el-row v-if="form.immuneId" :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="审核意见" prop="immuneRegister.reason">
                      <el-input
                        v-model="form.immuneRegister.reason"
                        type="textarea"
                        auto-complete="off"
                        :readonly="readonly"
                        placeholder="请输入审核意见"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="type == '1'" label="操作记录" name="two">
          <el-table
            v-loading="loading"
            border
            :data="recordList"
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column prop="createName" label="操作人"></el-table-column>
            <el-table-column
              prop="createDate"
              label="操作时间"
            ></el-table-column>
            <el-table-column prop="petNum" label="犬牌号"></el-table-column>
            <el-table-column
              label="类型"
              :formatter="formatNode"
            ></el-table-column>
            <el-table-column
              label="状态"
              :formatter="formatRecordStatus"
            ></el-table-column>
            <el-table-column
              prop="remark"
              label="原因/审批意见"
            ></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancelForm">取消</el-button>
        <el-button
          v-if="type == '2' || type == '3'"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="formSubmit('1')"
        >
          生成免疫证
        </el-button>
      </div>
    </el-dialog>

    <ImmuneRecord
      :outer-visible="immnueVisible"
      :pet-id="petId"
      @callBack="callBack"
    ></ImmuneRecord>

    <el-dialog
      title="犬牌申请"
      :visible.sync="ApplyVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-form
        ref="refForm"
        :model="form"
        label-width="150px"
        :rules="formApplyRules"
      >
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="犬牌号" prop="petNum">
                <el-select
                  v-model="form.petNum"
                  filterable
                  placeholder="请选择"
                  :disabled="true"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="item in petNumArray"
                    :key="item.brandNum"
                    :label="item.brandNum"
                    :value="item.brandNum"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!--                            <el-col :span="12">-->
            <!--                                <el-form-item label="领取方式" prop="expresType">-->
            <!--                                    <el-radio v-model="form.expresType" label="1">自取</el-radio>-->
            <!--                                    <el-radio v-model="form.expresType" label="2">快递</el-radio>-->
            <!--                                </el-form-item>-->
            <!--                            </el-col>-->
          </el-row>
          <!--                        <el-row :gutter="20">-->
          <!--                            <el-col :span="24" v-if="form.expresType=='2'">-->
          <!--                                <el-form-item label="领取地址" prop="expresAddress">-->
          <!--                                    <el-input type="textarea" v-model="form.expresAddress"></el-input>-->
          <!--                                </el-form-item>-->
          <!--                            </el-col>-->
          <!--                        </el-row>-->
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="cancelForm">取消</el-button>
        <el-button
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="ApplySubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <!-- 移除了自定义图片预览，使用el-image组件内置的预览功能 -->
  </div>
</template>

<script>
/* eslint-disable */
import { base, doPost, getDownLoadUrl } from '@/api/dog/index'
import { validatorPhone, validatorIdCard } from '@/utils/index'
import ImmuneRecord from './ImmuneRecord.vue'
import { getToken } from '@/utils/auth'

export default {
  components: { ImmuneRecord },
  data() {
    const residenceValidate = (rule, value, callback) => {
      if (this.residenceImgList.length == 0) {
        callback(new Error('请选择户口本照片！'))
      } else {
        callback()
      }
    }
    const propertyValidate = (rule, value, callback) => {
      if (this.propertyImgList.length == 0) {
        callback(new Error('请选择房产证/租赁合同照片！'))
      } else {
        callback()
      }
    }
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      searchType2: '1',
      petId: '',
      filters: {
        pageNum: '',
        pageSize: '',
        petIdCard: '',
        immuneStatus: '',
        aboutMake: '',
        hospitalId: '',
        petDept: '',
        applyStatus: '',
      },
      form: {
        id: '',
        ownerName: '', //饲主姓名
        petIdCard: '', //身份证号
        ownerAddress: '', //户籍地址
        tel: '', //饲主联系电话
        petNum: '', //犬牌编号
        petName: '', //宠物名
        petType: '', //宠物类别(对应字典表pet_type)
        petSex: '', //宠物性别
        petVarieties: '', //品种(对应字典表varieties_type)
        petVarietiesOne: '', //品种（大类）
        petHair: '', //毛色(对应字典表hair_type)
        petAge: '', //年龄
        raiseDate: '', //饲养日期
        petAddress: '', //详细地址
        petDept: '', //所在地区
        street: '', //所在地区街道
        isAgency: '', //是否代办：1是，2否
        agencyCom: '', //代办单位
        status: '', //审批状态：1待审批，2已通过，3已注销，4走失注销
        aboutMake: '', //办理方式
        hospitalId: '', //注射医院ID
        petId: '', //宠物ID
        hospital: '', //注射医生
        doctor: '', //注射医生
        vaccineBrand: '', //疫苗品牌
        vaccineBrandOld: '', //原来的疫苗品牌
        vaccineBatch: '', //疫苗批次
        injectionDate: '', //注射日期
        injectionEnddate: '', //到期日期
        aboutDate: '', //预约时间
        expresType: '0', //领取方式
        expresAddress: '', //领取地址
        petImg: '',
        residence: '',
        property: '',
        petImgZ: '',
        petImgF: '',
        posiCard: '',
        sideCard: '',
        immuneCard: '',
        otherVarieties: '', // 其他品种名称
        immuneRegister: {
          id: '',
          hospitalId: '', //注射医院ID
          petId: '', //宠物ID
          hospital: '', //注射医生
          doctor: '', //注射医生
          vaccineBrand: '', //疫苗品牌
          vaccineBrandOld: '', //原来的疫苗品牌
          vaccineBatch: '', //疫苗批次
          injectionDate: '', //注射日期
          status: '1',
          readonly: '',
        },
      },
      formRules: {
        petImgZ: [
          {
            required: true,
            message: '请选择宠物照片（正面）',
            trigger: 'blur',
          },
        ],
        petImgF: [
          {
            required: true,
            message: '请选择宠物照片（侧面）',
            trigger: 'blur',
          },
        ],
        sideCard: [
          {
            required: true,
            message: '请选择身份证照片（背面）',
            trigger: 'blur',
          },
        ],
        posiCard: [
          {
            required: true,
            message: '请选择身份证照片（正面）',
            trigger: 'blur',
          },
        ],
        petName: [
          { required: true, message: '请输入宠物名称', trigger: 'blur' },
        ],
        petSex: [
          { required: true, message: '请选择宠物性别', trigger: 'blur' },
        ],
        petVarietiesOne: [
          { required: true, message: '请选择宠物类型', trigger: 'blur' },
        ],
        petVarieties: [
          { required: true, message: '请选择宠物品种', trigger: 'blur' },
        ],
        petHair: [
          { required: true, message: '请选择宠物毛色', trigger: 'blur' },
        ],
        petAge: [
          { required: true, message: '请输入宠物年龄', trigger: 'blur' },
        ],
        raiseDate: [
          { required: true, message: '请选择饲养日期', trigger: 'blur' },
        ],
        immuneStatus: [
          { required: true, message: '请输入选择审核状态!', trigger: 'blur' },
        ],
        ownerName: [
          { required: true, message: '请输入饲主姓名!', trigger: 'blur' },
        ],
        petIdCard: [
          { required: true, message: '请输入身份证号!', trigger: 'blur' },
        ],
        tel: [{ required: true, message: '请输入联系电话!', trigger: 'blur' }],
        petDept: [
          { required: true, message: '请输入养宠地址!', trigger: 'blur' },
        ],
        hospitalId: [
          { required: true, message: '请选择注射医院!', trigger: 'blur' },
        ],
        doctor: [
          { required: true, message: '请输入注射医生!', trigger: 'blur' },
        ],
        vaccineBrand: [
          { required: true, message: '请选择疫苗品牌!', trigger: 'blur' },
        ],
        injectionDate: [
          { required: true, message: '请选择注射日期!', trigger: 'blur' },
        ],
        injectionEnddate: [
          { required: true, message: '请选择到期日期!', trigger: 'blur' },
        ],
        residence: [
          { required: true, validator: residenceValidate, trigger: 'blur' },
        ],
        property: [
          { required: true, validator: propertyValidate, trigger: 'blur' },
        ],
        otherVarieties: [
          { required: true, message: '请输入其他品种名称', trigger: 'blur' },
        ],
      },
      formApplyRules: {
        petNum: [{ required: true, message: '请选择犬牌号!', trigger: 'blur' }],
      },
      // user: {},
      type: '', // 区分
      loading: false,
      data: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      title: '',
      modalKey: 0,
      immnueVisible: false,
      ApplyVisible: false,
      formVisible: false,
      approvalForm: false,
      disabled: false,
      readonly: false,
      formLoading: false,
      numFlag: false,
      headers: { Authorization: 'Bearer ' + getToken() },
      cateProps: {
        label: 'deptName',
        children: 'children',
        value: 'id',
      },
      residenceName: '',
      residencePath: '',
      propertyName: '',
      propertyPath: '',
      petImgZName: '',
      petImgZPath: '',
      petImgFName: '',
      petImgFPath: '',
      posiCardName: '',
      posiCardPath: '',
      sideCardName: '',
      sideCardPath: '',
      area: [],
      areaOptions: [], // 地区下拉
      petTypes: [], // 宠物类别
      petVarietiesArray: [], // 宠物品种
      petVariAllList: [], // 宠物品种（总）
      petVariOneList: [], // 宠物品种（大类）
      vaccineBrandArray: [], // 疫苗品牌
      hospitalList: [], //注射医院
      petNumArray: [], // 犬牌下拉
      petHairData: [],
      statuss: [
        { label: '未免疫', value: '0' },
        { label: '已免疫', value: '1' },
        { label: '免疫过期', value: '2' },
        { label: '免疫即将过期', value: '3' },
      ],
      applyStatusList: [
        { label: '未上牌', value: '1' },
        { label: '待审核', value: '2' },
        { label: '未通过', value: '3' },
        { label: '已通过（未上牌）', value: '4' },
        { label: '已上牌（已激活）', value: '5' },
        { label: '已上牌（未激活）', value: '6' },
      ],
      makeList: [
        { label: '未预约', value: '0' },
        { label: '去医院免疫', value: '1' },
        { label: '上门免疫', value: '2' },
      ],
      petSexData: [
        { value: 1, label: '雄' },
        { value: 2, label: '雌' },
      ],
      nodeList: [
        { dictKey: 1, name: '犬牌申请' },
        { dictKey: 2, name: '办证审核' },
        { dictKey: 6, name: '犬证激活' },
      ],
      recordStatusList: [
        { dictKey: 1, name: '已提交' },
        { dictKey: 2, name: '已通过' },
        { dictKey: 3, name: '未通过' },
        { dictKey: 4, name: '已激活' },
      ],
      activeName: 'first',
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      fileList: [],
      propertyImgList: [], //租房合同照片集合
      residenceImgList: [], //户口本照片集合
      oldImgList: [], //老系统照片
      uploadFiles: [],
      recordList: [],
    }
  },
  computed: {
    user() {
      return JSON.parse(sessionStorage.getItem('user')) || {}
    },
  },
  watch: {
    'form.tel': {
      handler(newValue) {
        // 去除所有空格
        this.form.tel = newValue.replace(/\s/g, '')
      },
      immediate: true,
    },
  },
  methods: {
    //筛选展开
    changeSearchType() {
      this.searchType2 = this.searchType2 == 1 ? 2 : 1
    },
    resetParameter() {
      this.filters = {
        hospitalId:  '',
        pageNum: '',
        pageSize: '',
        petIdCard: '',
        immuneStatus: '',
        aboutMake: '',
        petDept: '',
        applyStatus: '',
      }
      if (this.user.userType === 2) {
        this.$set(this.filters, 'hospitalId', this.user.userQualifi?.id || '') //医院ID
      }
      if (this.user.userType === 3) {
        this.$set(
          this.filters,
          'petDept',
          this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
            ? ''
            : this.user.deptId
        )
      }
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPgaeList()
    },
    handleSearch(){
      this.currentPage = 1
      this.getPgaeList()
    },

    // 获取列表（查询条件，欠缺调整）
    getPgaeList: function () {
      this.loading = true
      let para = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        petIdCard: this.filters.petIdCard,
        immuneStatus: this.filters.status,
        aboutMake: this.filters.aboutMake,
        hospitalId: this.filters.hospitalId,
        petDept: this.filters.petDept,
        tel: this.filters.tel,
        petName: this.filters.petName,
        ownerName: this.filters.ownerName,
        petNum: this.filters.petNum,
        applyStatus: this.filters.applyStatus,
        startTime: this.filters.time ? this.filters.time[0] : '',
        endTime: this.filters.time ? this.filters.time[1] : '',
      }
      doPost('/petCertificates/queryPageList', para)
        .then((res) => {
          this.data = res.list
          if (this.data !== null && this.data.length > 0) {
            for (var a in this.data) {
              this.data[a].index =
                (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
            }
          }
          this.total = res.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    //房产证/租赁合同照片多上传
    handlePropertyChange(file, fileList) {
      const a = fileList.length - 1
      if (fileList[a].status === 'success') {
        this.propertyImgList.push({
          fileUrl: fileList[a].response.data,
          fileName: fileList[a].name,
          modelType: 'property',
          uid: file.uid,
          url: getDownLoadUrl(fileList[a].response.data),
        })
      }
    },
    //删除房产证/租赁合同照片
    handlePropertyRemove(file, fileList) {
      this.propertyImgList.forEach((item, index) => {
        if (item.uid == file.uid) {
          this.propertyImgList.splice(index, 1)
        }
      })
    },
    //户口本照片多上传
    handleResidenceChange(file, fileList) {
      const a = fileList.length - 1
      if (fileList[a].status === 'success') {
        this.residenceImgList.push({
          fileUrl: fileList[a].response.data,
          fileName: fileList[a].name,
          modelType: 'residence',
          uid: file.uid,
          url: getDownLoadUrl(fileList[a].response.data),
        })
      }
    },
    //删除户口本照片
    handleResidenceRemove(file, fileList) {
      this.residenceImgList.forEach((item, index) => {
        if (item.uid == file.uid) {
          this.residenceImgList.splice(index, 1)
        }
      })
    },
    handleSuccess(res, file, strUrl) {
      console.log(res, file, strUrl)
      if (strUrl == 'residence') {
        this.form.residence = getDownLoadUrl(res.data)
        this.residenceName = file.name
        this.residencePath = res.data
      } else if (strUrl == 'property') {
        this.form.property = getDownLoadUrl(res.data)
        this.propertyName = file.name
        this.propertyPath = res.data
      } else if (strUrl == 'petImgZ') {
        this.form.petImgZ = getDownLoadUrl(res.data)
        this.petImgZName = file.name
        this.petImgZPath = res.data
      } else if (strUrl == 'petImgF') {
        this.form.petImgF = getDownLoadUrl(res.data)
        this.petImgFName = file.name
        this.petImgFPath = res.data
      } else if (strUrl == 'posiCard') {
        this.form.posiCard = getDownLoadUrl(res.data)
        this.posiCardName = file.name
        this.posiCardPath = res.data
      } else if (strUrl == 'sideCard') {
        this.form.sideCard = getDownLoadUrl(res.data)
        this.sideCardName = file.name
        this.sideCardPath = res.data
      }
    },
    // 限制上传格式
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 10
      const isPNG = file.type === 'image/png'
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是JPG格式或PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    handlePetDept(value) {
      // 所在地区选择
      if (value.length > 0) {
        this.form.petDept = value[0]
        this.form.street = value[1]
      } else {
        this.form.petDept = ''
        this.form.street = ''
      }
    },
    changeVariOneChange(e, show) {
      console.log('changeVariOneChange:', e)
      // 宠物品种（大类）
      const obj = this.petVariOneList.find((item) => {
        return item.dictKey === e
      })
      const variId = obj.id // 宠物品种(大类)ID
      this.petVarietiesArray = this.petVariAllList.filter((item) => {
        return item.parentId === variId
      })
      if (show) {
        this.form.petVarieties = ''
      }
    },
    // 新增
    addForm: function () {
      if (this.user.userType === 2) {
        if (!this.user.qualifiId) {
          this.$message.warning('该账号暂无免疫资质权限，请联系管理员')
          return
        }
        this.$set(this.form, 'hospitalId', this.user.userQualifi.id) //医院ID
        this.$set(this.form, 'hospital', this.user.userQualifi.name) //医院ID
      }
      this.type = '3'
      this.title = '新增'
      this.disabled = false
      this.readonly = false
      this.formVisible = true
      this.immnueVisible = false
      // 日期自动选择
      const end = new Date() // 获取当前的日期
      end.setTime(end.getTime())
      // 计算，将当期日期-1天
      // 此时得到的是中国标准时间，格式不是yyyy-MM-dd，需要用dateFormat这个函数转换下
      this.form.injectionDate = this.dateFormat(end)
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 364)
      this.form.injectionEnddate = this.dateFormat(end)
      doPost('/immuneRegister/randomMY', {}).then((res) => {
        this.$set(this.form, 'immuneCard', res)
      })

      // 设置宠物类型默认选择第一个选项
      if (this.petVariOneList && this.petVariOneList.length > 0) {
        this.form.petType = this.petVariOneList[0].dictKey
        // 触发品种列表加载
        this.changeVariOneChange(this.form.petType, true)
      }
    },

    dateFormat(dateData) {
      var date = new Date(dateData)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      const time = y + '-' + m + '-' + d
      return time
    },

    dateChange(dateData) {
      const end = new Date(dateData) // 获取当前的日期
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 364)
      this.form.injectionEnddate = this.dateFormat(end)
    },
    // 保存
    formSubmit: function (type) {
      const that = this
      this.uploadFiles = []
      // if (that.residencePath != '') {//户口本照片
      //     that.uploadFiles.push({
      //         fileUrl: that.residencePath, fileName: that.residenceName, modelType: 'residence'
      //     });
      // }
      // if (that.residencePath != '') {//房产证/租赁合同照片
      //     that.uploadFiles.push({
      //         fileUrl: that.propertyPath, fileName: that.propertyName, modelType: 'property'
      //     })
      // }
      if (that.petImgZPath != '') {
        //宠物照片（正面）
        that.uploadFiles.push({
          fileUrl: that.petImgZPath,
          fileName: that.petImgZName,
          modelType: 'petImgZ',
        })
      }
      if (that.petImgFPath != '') {
        //宠物照片（正面）
        that.uploadFiles.push({
          fileUrl: that.petImgFPath,
          fileName: that.petImgFName,
          modelType: 'petImgF',
        })
      }
      if (that.posiCardPath != '') {
        //身份证照片（正面）
        that.uploadFiles.push({
          fileUrl: that.posiCardPath,
          fileName: that.posiCardName,
          modelType: 'posiCard',
        })
      }
      if (that.sideCardPath != '') {
        //身份证照片（反面）
        that.uploadFiles.push({
          fileUrl: that.sideCardPath,
          fileName: that.sideCardName,
          modelType: 'sideCard',
        })
      }
      var imgList = [
        ...this.uploadFiles,
        ...this.residenceImgList,
        ...this.propertyImgList,
      ]
      // console.log(imgList)
      that.form.petImg = that.petImgZPath
      const param = {
        id: that.form.id,
        ownerName: that.form.ownerName,
        petIdCard: that.form.petIdCard,
        tel: that.form.tel,
        ownerAddress: that.form.ownerAddress,
        petName: that.form.petName,
        petNum: that.form.petNum,
        petType: that.form.petType,
        petSex: that.form.petSex,
        petVarieties: that.form.petVarieties,
        petVarietiesOne: that.form.petVarietiesOne,
        petHair: that.form.petHair,
        petAge: that.form.petAge,
        raiseDate: that.form.raiseDate,
        petAddress: that.form.petAddress,
        petDept: that.form.petDept,
        street: that.form.street,
        isAgency: that.form.isAgency,
        agencyCom: that.user.userName, // 代办单位
        source: '2', // 宠物登记来源
        status: '1', // 1：暂存  2：提交审核
        endDate: that.form.injectionEnddate,
        immuneId: that.form.immuneId,
        expresType: that.form.expresType,
        expresAddress: that.form.expresAddress,
        hospitalId: that.form.hospitalId,
        otherVarieties: that.form.otherVarieties, // 其他品种名称
        // aboutStatus: '1',
        petImg: that.form.petImg,
        immuneStr: JSON.stringify({
          id: that.form.immuneId,
          petId: that.form.id,
          hospital: that.form.hospital,
          hospitalId: that.form.hospitalId,
          doctor: that.form.doctor,
          vaccineBrand: that.form.vaccineBrand,
          vaccineBatch: that.form.vaccineBatch,
          vaccineBrandOld: that.form.vaccineBrandOld,
          injectionDate: that.form.injectionDate,
          injectionEnddate: that.form.injectionEnddate,
          immuneCard: that.form.immuneCard,
          petIdCard: that.form.petIdCard,
          status: '1',
        }),
        uploadFilesStr: JSON.stringify(imgList),
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 如果选择了"其他品种"但没有填写具体品种名称，则提示错误
          if (that.form.petVarieties === '107' && !that.form.otherVarieties) {
            this.$message.error('请输入其他品种名称');
            return
          }

          this.$confirm('确认保存免疫吗', '提示', {
            confirmButtonText: '继续保存',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = Object.assign(param)
            doPost('/immuneRegister/savePet', para)
              .then((re) => {
                this.formLoading = false
                this.cancelForm()
                this.getPgaeList()
                this.petConfirm(re.result) //提示
              })
              .catch(() => {
                this.formLoading = false
              })
          })
        }
      })
    },

    petConfirm(petId) {
      doPost('/petCertificates/queryPetCount', { id: petId }).then((res) => {
        if (res != null && res.petCount == '0') {
          this.$confirm('免疫保存成功，是否继续办理犬牌', '提示', {
            confirmButtonText: '办理犬牌',
            cancelButtonText: '暂不办理犬牌',
          }).then(() => {
            this.handleApply(res.id, res.immuneId, res.petNum)
          })
        } else {
          this.$alert(
            '免疫保存成功，该身份证下已绑定过犬牌，暂时无法在办理犬牌。',
            '提示',
            {
              confirmButtonText: '确定',
              callback: (action) => {},
            }
          )
        }
      })
    },
    getRecord(id) {
      doPost('/petRecord/getList', { petId: id }).then((res) => {
        if (res.length > 0) {
          this.recordList = res.filter((item) => {
            return item.node === 1 || item.node === 2 || item.node === 6
          })
        } else {
          this.recordList = []
        }
      })
    },
    // 编辑
    handleEdit: function (type, id) {
      var that = this
      that.type = type
      that.formVisible = true
      that.immnueVisible = false
      let para = { id: id }
      if (type === 1) {
        that.title = '查看'
        that.readonly = true
        that.disabled = true
        this.getRecord(id)
      } else if (type === 2) {
        that.title = '编辑'
        that.readonly = false
        that.disabled = false
      }
      doPost('/immuneRegister/getByRegister', para).then((res) => {
        that.form = res
        if (this.form.expresAddress) {
          this.form.expresAddress =
            this.form.expresRegion.replaceAll('-', '') + this.form.expresAddress
        }
        that.changeVariOneChange(that.form.petType, false) //宠物品种回显
        //所在地区回显
        that.area = [that.form.petDept, that.form.street]
        that.modalKey++
        const imgList = that.form.uploadFiles
        console.log(imgList)
        for (const i in imgList) {
          if (imgList[i].modelType == 'petImgZ' && imgList[i].fileUrl != '') {
            that.petImgZPath = imgList[i].fileUrl
            that.petImgZName = imgList[i].fileName
            that.$set(that.form, 'petImgZ', getDownLoadUrl(imgList[i].fileUrl))
          } else if (
            imgList[i].modelType == 'petImgF' &&
            imgList[i].fileUrl != ''
          ) {
            that.petImgFPath = imgList[i].fileUrl
            that.petImgFName = imgList[i].fileName
            that.$set(that.form, 'petImgF', getDownLoadUrl(imgList[i].fileUrl))
          } else if (
            imgList[i].modelType == 'residence' &&
            imgList[i].fileUrl != ''
          ) {
            //户口本照片集合
            that.residenceImgList.push({
              uid: imgList[i].id,
              url: getDownLoadUrl(imgList[i].fileUrl),
              name: imgList[i].fileName,
              fileName: imgList[i].fileName,
              fileUrl: imgList[i].fileUrl,
              modelType: 'residence',
            })
            // that.residencePath = imgList[i].fileUrl;
            // that.residenceName = imgList[i].fileName;
            // that.$set(that.form, 'residence', getDownLoadUrl(imgList[i].fileUrl));
          } else if (
            imgList[i].modelType == 'property' &&
            imgList[i].fileUrl != ''
          ) {
            //房产证/租赁合同照片集合
            that.propertyImgList.push({
              uid: imgList[i].id,
              url: getDownLoadUrl(imgList[i].fileUrl),
              name: imgList[i].fileName,
              fileName: imgList[i].fileName,
              fileUrl: imgList[i].fileUrl,
              modelType: 'property',
            })
            // that.propertyPath = imgList[i].fileUrl;
            // that.propertyName = imgList[i].fileName;
            // that.$set(that.form, 'property', getDownLoadUrl(imgList[i].fileUrl));
          } else if (
            imgList[i].modelType == 'posiCard' &&
            imgList[i].fileUrl != ''
          ) {
            that.posiCardPath = imgList[i].fileUrl
            that.posiCardName = imgList[i].fileName
            that.$set(that.form, 'posiCard', getDownLoadUrl(imgList[i].fileUrl))
          } else if (
            imgList[i].modelType == 'sideCard' &&
            imgList[i].fileUrl != ''
          ) {
            that.sideCardPath = imgList[i].fileUrl
            that.sideCardName = imgList[i].fileName
            that.$set(that.form, 'sideCard', getDownLoadUrl(imgList[i].fileUrl))
          } else if (
            imgList[i].modelType == 'old_img' &&
            imgList[i].fileUrl != ''
          ) {
            //老系统照片
            that.oldImgList.push({
              url: imgList[i].fileUrl,
            })
          }
        }
        // that.uploadFiles = imgList; //回显防止，直接编辑图片丢失
        if (type === 4) {
          that.$set(that.form, 'immuneId', '')
        } else if (that.form.immuneRegister) {
          that.$set(that.form, 'hospital', that.form.immuneRegister.hospital)
          that.$set(that.form, 'doctor', that.form.immuneRegister.doctor)
          that.$set(
            that.form,
            'vaccineBrand',
            that.form.immuneRegister.vaccineBrand
          )
          that.$set(
            that.form,
            'vaccineBrandOld',
            that.form.immuneRegister.vaccineBrand
          )
          that.$set(
            that.form,
            'vaccineBatch',
            that.form.immuneRegister.vaccineBatch
          )
          that.$set(
            that.form,
            'injectionDate',
            that.form.immuneRegister.injectionDate
          )
          that.$set(
            that.form,
            'injectionEnddate',
            that.form.immuneRegister.injectionEnddate
          )
          that.$set(
            that.form,
            'immuneCard',
            that.form.immuneRegister.immuneCard
          )
        }
        delete that.form.uploadFiles
      })
    },

    //犬牌申请
    ApplySubmit: function () {
      this.$refs.refForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = Object.assign(
              {},
              {
                id: this.form.id,
                petNum: this.form.petNum,
                immuneId: this.form.immuneId,
                isAgency: '1', // 是否代办：1是，2否
                agencyCom: this.user.userName, // 代办单位
                expresType: '0', // 领取类型 0：现场取
                expresAddress: ' ', //领取地��
                expresRegion: ' ', //领取地区
                zfckId: ' ', //执法窗口ID,空格后端去掉转成''
              }
            )
            // console.log(para)
            doPost('/immuneRegister/savePetApply', para)
              .then((res) => {
                this.formLoading = false
                this.$message({
                  message: '提交成功',
                  type: 'success',
                })
                this.getPgaeList()
                this.cancelForm()
              })
              .catch(() => {
                this.formLoading = false
              })
          })
        }
      })
    },

    // 点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.ApplyVisible = false
      this.approvalForm = false
      this.numFlag = false
      this.activeName = 'first'
      this.$refs['form'].resetFields()
      this.form = this.$options.data().form
      this.propertyImgList = [] //户口本照片集合
      this.residenceImgList = [] //租房合同照片集合
      this.uploadFiles = []
      this.area = []
      this.residencePath = ''
      this.propertyPath = ''
      this.petImgFPath = ''
      this.petImgZPath = ''
      this.sideCardPath = ''
      this.posiCardPath = ''
    },
    handleImmnue: function (id) {
      // if (
      //   this.user.qualifi == null ||
      //   this.user.qualifi.split(',')[1] === '0'
      // ) {
      //   this.$alert('用户未获得免疫资格，请获取资质再进行免疫！', '提示', {
      //     confirmButtonText: '确定',
      //     callback: (action) => {},
      //   })
      //   return
      // }
      this.petId = id
      this.title = '免疫记录'
      this.immnueVisible = true
    },
    handleApply: function (id, immuneId, petNum) {
      // if (
      //   this.user.qualifi == null ||
      //   this.user.qualifi.split(',')[0] === '0'
      // ) {
      //   this.$alert(
      //     '用户未获得犬牌发放资格，请获取资质再进行犬牌申请！',
      //     '提示',
      //     {
      //       confirmButtonText: '确定',
      //       callback: (action) => {},
      //     }
      //   )
      //   return
      // }
      this.petNumArray = []
      this.ApplyVisible = true
      this.form.id = id
      this.form.immuneId = immuneId
      this.form.petNum = petNum
      if (this.form.petNum) {
        this.numFlag = true
      }
      this.getPetNum()
    },
    callBack: function () {
      this.immnueVisible = false
      this.getPgaeList()
    },
    vaccineBrandChange: function (e) {
      let obj = {}
      obj = this.vaccineBrandArray.find((item) => {
        return item.id === e
      })
      // this.form.vaccineBatch = obj.batch// 疫苗批次
      // if (obj.batchSurplus == '0') {
      //     this.$message.error('此疫苗数量为0，不能选择!')
      //     this.form.vaccineBrand = '';
      //     this.form.vaccineBatch = '';
      //     return;
      // } else {
      //     this.form.vaccineBatch = obj.batch// 疫苗批次
      // }
    },
    hospitalChange: function (e) {
      let obj = {}
      obj = this.hospitalList.find((item) => {
        return item.id === e
      })
      this.form.hospital = obj.name // 疫苗批次
    },

    formatStatus: function (row, column) {
      if (row.immuneId == '' || row.immuneId == null) {
        return '未免疫'
      } else if (row.immuneRegister.status == '5') {
        return '免疫过期'
      } else if (row.immuneRegister.status == '6') {
        return '免疫即将过期'
      } else if (row.immuneId) {
        return '已免疫'
      }
    },

    formatNumStatus: function (row, column) {
      if (row.applyStatus == '1' && row.petNum == '') {
        return '未上牌'
      } else if (row.applyStatus == '2') {
        return '待审核'
      } else if (row.applyStatus == '4') {
        return '未通过'
      } else if (row.applyStatus == '3' && row.petNum == '') {
        return '已通过（未上牌）'
      } else if (row.applyStatus == '3' && row.activation == '2') {
        return '已上牌（已激活）'
      } else if (row.applyStatus == '3' && row.activation == '1') {
        return '已上牌（未激活）'
      }
    },
    formatDept: function (row, column) {
      for (var i in this.areaOptions) {
        if (row.petDept === this.areaOptions[i].id) {
          return '浙江省/金华市/' + this.areaOptions[i].deptName
        }
      }
    },
    formatAboutMake: function (row, column) {
      for (let i in this.makeList) {
        if (!row.aboutMake) {
          return ''
        } else if (row.aboutMake === this.makeList[i].value) {
          return this.makeList[i].label
        }
      }
    },

    //列表显示
    formatNode: function (row, column) {
      let str = ''
      this.nodeList.forEach(function (item) {
        if (item.dictKey == row.node) {
          str = item.name
        }
      })
      return str
    },
    formatRecordStatus: function (row, column) {
      let str = ''
      this.recordStatusList.forEach(function (item) {
        if (item.dictKey == row.status) {
          str = item.name
        }
      })
      return str
    },

    getTypesData() {
      const str = []
      // 查询宠物类型
      doPost('/sysDict/getAllList', { dictType: 'pet_type' }).then((res) => {
        for (let i in res) {
          this.petTypes.push(res[i])
          str.push(Number(res[i].dictKey))
        }
      })
      // 查询宠物品种
      doPost('/sysDict/getAllList', { dictType: 'varieties_type' }).then(
        (res) => {
          this.petVariAllList = res
        }
      )
      doPost('/sysDict/getAllList', { dictType: 'varieties' }).then((res) => {
        this.petVariOneList = res
      })
      doPost('/sysDict/getAllList', { dictType: 'hair_type' }).then((res) => {
        this.petHairData = res
      })
      // 查询通过审核疫苗品牌
      doPost('/vaccine/getAllList', {
        createBy: this.user.userType == '2' ? this.user.id : '',
      }).then((res) => {
        this.vaccineBrandArray = res
      })
      // 查询通过注射医院
      doPost('/hospital/getAllList', { status: '2' }).then((res) => {
        for (let i in res) {
          this.hospitalList.push(res[i])
        }
      })
      doPost('/dept/getSecondDeptTree', {}).then((res) => {
        console.log('getSecondDeptTree', res)
        this.areaOptions = res
      })

      this.getPgaeList()
    },
    getPetNum() {
      console.log('this.user', this.user)
      // if (this.user.userType === 2) {
      var param = {
        brandCom: this.user?.userQualifi?.id || '',
        brandNum: this.form.petNum,
      }
      console.log('param', param)
      doPost('/petBrand/getAllList', param).then((res) => {
        console.log('res', res)
        this.form.petNum = res[0].brandNum
        for (let i in res) {
          this.petNumArray.push(res[i])
        }
      })
      // }
    },
    getImmuneStatusType(row) {
      if (!row.immuneId) {
        return 'info' // 未免疫 - 灰色
      } else if (row.immuneRegister?.status === '5') {
        return 'danger' // 免疫过期 - 红色
      } else if (row.immuneRegister?.status === '6') {
        return 'warning' // 免疫即将过期 - 黄色
      } else if (row.immuneId) {
        return 'success' // 已免疫 - 绿色
      }
      return ''
    },

    getNumStatusType(row) {
      if (row.applyStatus === '1' && !row.petNum) {
        return 'info' // 未上牌 - 灰色
      } else if (row.applyStatus === '2') {
        return 'warning' // 待审核 - 黄色
      } else if (row.applyStatus === '4') {
        return 'danger' // 未通过 - 红色
      } else if (row.applyStatus === '3') {
        if (!row.petNum) {
          return 'warning' // 已通过(未上牌) - 黄色
        } else if (row.activation === '2') {
          return 'success' // 已上牌(已激活) - 绿色
        } else if (row.activation === '1') {
          return 'info' // 已上牌(未激活) - 灰色
        }
      }
      return ''
    },
  },
  mounted() {
    // this.headers = this.initHeaders()
    if (this.user.userType === 2) {
      this.$set(this.filters, 'hospitalId', this.user.userQualifi?.id || '') //医院ID
    }
    if (this.user.userType === 3) {
      this.$set(
        this.filters,
        'petDept',
        this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
          ? ''
          : this.user.deptId
      )
    }
    this.getTypesData() // 初始化信息
  },
}
</script>

<style lang="scss" scoped>
/* 修改为使用 ::v-deep 或 >>> 选择器 */

::v-deep .hidden-Btn .el-upload {
  display: none;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

::v-deep .avatar {
  width: 100%;
  height: 150px;
  display: block;
}

/* 其他样式保持不变 */
.deleteImg {
  font-size: 20px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
}

.showPhoto {
  position: fixed;
  top: 10%;
  width: 80%;
  height: 80%;
  z-index: 99999;
  display: flex;
}

.showPhoto .img {
  display: block;
  margin: auto 0;
  max-width: 100%;
  max-height: 100%;
  text-align: center;
}
</style>
