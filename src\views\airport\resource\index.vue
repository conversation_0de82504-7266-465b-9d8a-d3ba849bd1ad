<template>
  <div class="app-container">
    <!-- 顶部统计面板 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>应急人员</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.personnelCount || 0 }}</span>
            <span class="unit">人</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>应急物资</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.materialCount || 0 }}</span>
            <span class="unit">种</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>应急装备</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.equipmentCount || 0 }}</span>
            <span class="unit">台</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>联动单位</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.unitCount || 0 }}</span>
            <span class="unit">个</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧资源列表 -->
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>应急资源管理</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="refreshData">刷新</el-button>
              <el-button size="mini" @click="exportData">导出</el-button>
            </el-button-group>
          </div>
          
          <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
            <!-- 应急人员 -->
            <el-tab-pane label="应急人员" name="personnel">
              <div class="tab-content">
                <el-button type="primary" size="mini" @click="addPersonnel" class="mb10">新增人员</el-button>
                <el-table :data="personnelList" style="width: 100%" size="small">
                  <el-table-column prop="personnelCode" label="人员编号" width="120" />
                  <el-table-column prop="name" label="姓名" width="100" />
                  <el-table-column prop="deptName" label="部门" width="120" />
                  <el-table-column prop="position" label="职位" width="100" />
                  <el-table-column prop="emergencyRole" label="应急角色" width="120" />
                  <el-table-column prop="availabilityStatus" label="状态" width="80">
                    <template slot-scope="scope">
                      <el-tag :type="getPersonnelStatusType(scope.row.availabilityStatus)" size="mini">
                        {{ getPersonnelStatusText(scope.row.availabilityStatus) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="viewPersonnel(scope.row)">查看</el-button>
                      <el-button size="mini" @click="dispatchPersonnel(scope.row)">调度</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 应急物资 -->
            <el-tab-pane label="应急物资" name="material">
              <div class="tab-content">
                <el-button type="primary" size="mini" @click="addMaterial" class="mb10">新增物资</el-button>
                <el-table :data="materialList" style="width: 100%" size="small">
                  <el-table-column prop="materialCode" label="物资编码" width="120" />
                  <el-table-column prop="materialName" label="物资名称" width="150" />
                  <el-table-column prop="materialType" label="物资类型" width="100" />
                  <el-table-column prop="currentStock" label="当前库存" width="100" />
                  <el-table-column prop="unit" label="单位" width="60" />
                  <el-table-column prop="storageLocation" label="存储位置" width="120" />
                  <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="viewMaterial(scope.row)">查看</el-button>
                      <el-button size="mini" @click="dispatchMaterial(scope.row)">调度</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 应急装备 -->
            <el-tab-pane label="应急装备" name="equipment">
              <div class="tab-content">
                <el-button type="primary" size="mini" @click="addEquipment" class="mb10">新增装备</el-button>
                <el-table :data="equipmentList" style="width: 100%" size="small">
                  <el-table-column prop="equipmentCode" label="装备编码" width="120" />
                  <el-table-column prop="equipmentName" label="装备名称" width="150" />
                  <el-table-column prop="equipmentType" label="装备类型" width="100" />
                  <el-table-column prop="currentLocation" label="当前位置" width="120" />
                  <el-table-column prop="usageStatus" label="使用状态" width="80">
                    <template slot-scope="scope">
                      <el-tag :type="getEquipmentStatusType(scope.row.usageStatus)" size="mini">
                        {{ getEquipmentStatusText(scope.row.usageStatus) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="viewEquipment(scope.row)">查看</el-button>
                      <el-button size="mini" @click="dispatchEquipment(scope.row)">调度</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 联动单位 -->
            <el-tab-pane label="联动单位" name="unit">
              <div class="tab-content">
                <el-button type="primary" size="mini" @click="addUnit" class="mb10">新增单位</el-button>
                <el-table :data="unitList" style="width: 100%" size="small">
                  <el-table-column prop="unitCode" label="单位编码" width="120" />
                  <el-table-column prop="unitName" label="单位名称" width="200" />
                  <el-table-column prop="unitType" label="单位类型" width="100" />
                  <el-table-column prop="contactPerson" label="联系人" width="100" />
                  <el-table-column prop="contactPhone" label="联系电话" width="120" />
                  <el-table-column prop="cooperationLevel" label="合作级别" width="80">
                    <template slot-scope="scope">
                      <el-tag :type="getCooperationLevelType(scope.row.cooperationLevel)" size="mini">
                        {{ getCooperationLevelText(scope.row.cooperationLevel) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="viewUnit(scope.row)">查看</el-button>
                      <el-button size="mini" @click="contactUnit(scope.row)">联系</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>

      <!-- 右侧操作面板 -->
      <el-col :span="8">
        <!-- 快速调度面板 -->
        <el-card class="box-card mb20">
          <div slot="header" class="clearfix">
            <span>快速调度</span>
          </div>
          <el-form :model="dispatchForm" size="small">
            <el-form-item label="资源类型">
              <el-select v-model="dispatchForm.resourceType" placeholder="请选择">
                <el-option label="应急人员" value="personnel" />
                <el-option label="应急物资" value="material" />
                <el-option label="应急装备" value="equipment" />
                <el-option label="技术资源" value="technical" />
              </el-select>
            </el-form-item>
            <el-form-item label="调度目的">
              <el-input v-model="dispatchForm.purpose" placeholder="请输入调度目的" />
            </el-form-item>
            <el-form-item label="调度地点">
              <el-input v-model="dispatchForm.location" placeholder="请输入调度地点" />
            </el-form-item>
            <el-form-item label="预计时间">
              <el-date-picker
                v-model="dispatchForm.expectedTime"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="small" @click="submitDispatch">提交调度</el-button>
              <el-button size="small" @click="resetDispatchForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 调度记录 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近调度记录</span>
          </div>
          <div class="dispatch-records">
            <div v-for="record in dispatchRecords" :key="record.id" class="record-item">
              <div class="record-header">
                <span class="record-type">{{ record.resourceType }}</span>
                <span class="record-time">{{ record.dispatchTime }}</span>
              </div>
              <div class="record-content">
                <p>{{ record.purpose }}</p>
                <el-tag :type="getDispatchStatusType(record.status)" size="mini">
                  {{ getDispatchStatusText(record.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
