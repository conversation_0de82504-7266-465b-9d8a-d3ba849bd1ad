<template>
  <div class="score-gifts-page">
    <el-card class="page-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="page-title">积分好礼</span>
        <div class="header-info">
          <span class="score-info">我的积分：<strong>{{ userScore }}</strong></span>
          <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshData">
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="page-content">
        <div class="module-description">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>在这里您可以使用积分兑换奖励：</p>
            <ul>
              <li>查看可兑换的积分奖品</li>
              <li>使用积分兑换心仪的礼品</li>
              <li>查看积分获取和消费记录</li>
              <li>了解积分规则和活动</li>
            </ul>
          </el-alert>
        </div>

        <div class="score-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalScore }}</div>
                <div class="stat-label">累计积分</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.usedScore }}</div>
                <div class="stat-label">已使用</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.exchangeCount }}</div>
                <div class="stat-label">兑换次数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.thisMonth }}</div>
                <div class="stat-label">本月获得</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="gifts-section">
          <div class="section-title">
            <h3>积分商城</h3>
            <el-select v-model="filterCategory" placeholder="选择分类" clearable>
              <el-option label="全部" value="" />
              <el-option label="生活用品" value="daily" />
              <el-option label="电子产品" value="electronic" />
              <el-option label="文体用品" value="sports" />
              <el-option label="其他" value="other" />
            </el-select>
          </div>

          <div class="gifts-grid">
            <div 
              v-for="gift in filteredGifts" 
              :key="gift.id" 
              class="gift-card"
            >
              <div class="gift-image">
                <img :src="gift.image" :alt="gift.name" />
              </div>
              <div class="gift-info">
                <h4 class="gift-name">{{ gift.name }}</h4>
                <p class="gift-desc">{{ gift.description }}</p>
                <div class="gift-score">
                  <span class="score-text">{{ gift.score }} 积分</span>
                  <el-button 
                    type="primary" 
                    size="small"
                    :disabled="userScore < gift.score"
                    @click="exchangeGift(gift)"
                  >
                    {{ userScore >= gift.score ? '立即兑换' : '积分不足' }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="exchange-history">
          <h3>兑换记录</h3>
          <el-table :data="exchangeHistory" style="width: 100%" v-loading="loading">
            <el-table-column prop="giftName" label="礼品名称" min-width="200" />
            <el-table-column prop="score" label="消耗积分" width="120" />
            <el-table-column prop="exchangeTime" label="兑换时间" width="180" />
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewExchange(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ScoreGifts',
  data() {
    return {
      loading: false,
      userScore: 1250,
      filterCategory: '',
      stats: {
        totalScore: 2800,
        usedScore: 1550,
        exchangeCount: 8,
        thisMonth: 320
      },
      giftsList: [
        {
          id: 1,
          name: '保温杯',
          description: '不锈钢保温杯，容量500ml',
          score: 200,
          category: 'daily',
          image: '/api/placeholder/150/150'
        },
        {
          id: 2,
          name: '蓝牙耳机',
          description: '无线蓝牙耳机，高音质',
          score: 800,
          category: 'electronic',
          image: '/api/placeholder/150/150'
        },
        {
          id: 3,
          name: '运动毛巾',
          description: '纯棉运动毛巾，吸汗透气',
          score: 150,
          category: 'sports',
          image: '/api/placeholder/150/150'
        },
        {
          id: 4,
          name: '充电宝',
          description: '10000mAh移动电源',
          score: 600,
          category: 'electronic',
          image: '/api/placeholder/150/150'
        }
      ],
      exchangeHistory: [
        {
          id: 1,
          giftName: '保温杯',
          score: 200,
          exchangeTime: '2024-01-15 14:30',
          status: 'completed'
        },
        {
          id: 2,
          giftName: '运动毛巾',
          score: 150,
          exchangeTime: '2024-01-10 09:20',
          status: 'completed'
        }
      ]
    }
  },
  computed: {
    filteredGifts() {
      if (!this.filterCategory) {
        return this.giftsList
      }
      return this.giftsList.filter(gift => gift.category === this.filterCategory)
    }
  },
  methods: {
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    exchangeGift(gift) {
      this.$confirm(`确认使用 ${gift.score} 积分兑换 ${gift.name}？`, '确认兑换', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.userScore -= gift.score
        this.$message.success('兑换成功！')
      })
    },
    getStatusType(status) {
      const typeMap = {
        completed: 'success',
        processing: 'warning',
        cancelled: 'danger'
      }
      return typeMap[status] || 'info'
    },
    getStatusText(status) {
      const textMap = {
        completed: '已完成',
        processing: '处理中',
        cancelled: '已取消'
      }
      return textMap[status] || '未知'
    },
    viewExchange(record) {
      this.$message.info(`查看兑换详情：${record.giftName}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.score-gifts-page {
  padding: 20px;

  .page-card {
    border-radius: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }

      .header-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .score-info {
          font-size: 14px;
          color: #606266;

          strong {
            color: #1890ff;
            font-size: 16px;
          }
        }
      }
    }

    .page-content {
      .module-description {
        margin-bottom: 24px;

        .el-alert {
          border-radius: 8px;

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
              color: #606266;
            }
          }
        }
      }

      .score-stats {
        margin-bottom: 24px;

        .stat-card {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          color: white;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .gifts-section {
        margin-bottom: 32px;

        .section-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
          }
        }

        .gifts-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 20px;

          .gift-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              transform: translateY(-2px);
            }

            .gift-image {
              height: 150px;
              background: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                max-width: 100%;
                max-height: 100%;
                object-fit: cover;
              }
            }

            .gift-info {
              padding: 16px;

              .gift-name {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
              }

              .gift-desc {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #606266;
                line-height: 1.4;
              }

              .gift-score {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .score-text {
                  font-size: 16px;
                  font-weight: 600;
                  color: #1890ff;
                }
              }
            }
          }
        }
      }

      .exchange-history {
        h3 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
        }

        .el-table {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .score-stats {
      .el-col {
        margin-bottom: 12px;
      }
    }

    .gifts-grid {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>
