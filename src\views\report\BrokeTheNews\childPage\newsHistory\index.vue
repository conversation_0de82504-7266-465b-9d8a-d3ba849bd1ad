<template>
  <div class="news-history">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="我的上报记录">
      <template slot="extra">
        <el-button type="primary" @click="onAddNew">新增上报</el-button>
      </template>
    </el-page-header>

    <!-- 搜索和筛选 -->
    <div class="search-filter">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="filterType" placeholder="选择问题分类" clearable @change="onFilterChange">
            <el-option
              v-for="item in filterOptions"
              :key="item.value"
              :label="item.text"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="18">
          <el-input
            v-model="queryParams.keyWord"
            placeholder="请输入搜索关键词"
            clearable
            @keyup.enter.native="onSearch"
            @clear="onSearch">
            <el-button slot="append" icon="el-icon-search" @click="onSearch"></el-button>
          </el-input>
        </el-col>
      </el-row>
    </div>

    <!-- 记录列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="list"
        style="width: 100%"
        @row-click="viewDetail"
        row-class-name="table-row-hover">

        <el-table-column prop="type" label="问题分类" width="120">
          <template slot-scope="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="问题描述" min-width="200" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="address" label="详细地址" min-width="180" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="status" label="处理状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click.stop="viewDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="total === 0 && !loading">
        <el-empty description="暂无上报记录">
          <el-button type="primary" @click="onAddNew">立即上报</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { getNewsList } from "@/api/common";

export default {
  name: "newsHistory",
  data() {
    return {
      loading: false,
      filterType: '',
      filterOptions: [
        { text: '市容环境类', value: '市容环境类' },
        { text: '街面秩序类', value: '街面秩序类' },
        { text: '公共设施类', value: '公共设施类' },
        { text: '突发事件类', value: '突发事件类' },
        { text: '牛皮癣', value: '牛皮癣' },
        { text: '其他', value: '其他' }
      ],
      list: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventdesc: '',
        keyWord: '',
        phone: ''
      }
    }
  },
  computed: {

  },
  mounted() {
    this.initParams();
    this.getRecordList();
  },
  methods: {
    initParams() {
      // 设置当前用户ID
      this.queryParams.phone = this.$store.state.user.phonenumber || '';
    },

    getStatusText(status) {
      const statusMap = {
        1:'上报',
        2:'立案',
        3:'派遣',
        4:'处置',
        5:'核查',
        6:'结案'
      }
      return statusMap[status] || '';
    },

    getStatusTagType(status) {
      const typeMap = {
        1: '',
        2: 'warning',
        3: 'warning',
        4: 'primary',
        5: 'primary',
        6: 'success'
      }
      return typeMap[status] || '';
    },

    getTypeTagType(type) {
      const typeMap = {
        '市容环境类': 'primary',
        '街面秩序类': 'success',
        '公共设施类': 'warning',
        '突发事件类': 'danger',
        '牛皮癣': 'info',
        '其他': ''
      }
      return typeMap[type] || '';
    },

    // 获取记录列表
    async getRecordList() {
      try {
        this.loading = true;

        // 实际API调用
        const res = await getNewsList(this.queryParams);
        if (res.code === 200 && res.rows && res.rows[0]?.eventList) {
          this.list = res.rows[0].eventList.map(item => ({
            id: item.id,
            type: this.getEventListText(item.eventdesc).title || '市容环境类',
            description: this.getEventListText(item.eventdesc).desc || '无描述',
            address: item.address || '无地址信息',
            status: item.status || 0
          }));
          this.total = res.total || res.rows[0]?.eventList?.length || 0;
        } else {
          this.list = [];
          this.total = 0;
        }

        this.loading = false;
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表失败');
        this.loading = false;
      }
    },

    getEventListText(str) {
      if (!str) return { title: '', desc: '' };
      const parts = str.split(":");
      return {
        title: parts[0] || '',
        desc: parts[1] || ''
      };
    },

    // 搜索
    onSearch() {
      this.queryParams.pageNum = 1;
      this.getRecordList();
    },

    // 筛选变化
    onFilterChange() {
      this.queryParams.pageNum = 1;
      this.getRecordList();
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getRecordList();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getRecordList();
    },

    // 查看详情
    viewDetail(item) {
      this.$router.push({
        path: '/newsDetail',
        query: { id: item.id }
      });
    },

    // 新增
    onAddNew() {
      this.$router.push('/BrokeTheNews');
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    }
  }
}
</script>

<style lang="scss" scoped>
.news-history {
  min-height: 100vh;
  background-color: #fff;
  padding: 20px;
}

// 页面头部
.el-page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e6e6e6;
}

// 搜索和筛选区域
.search-filter {
  background: #fff;
  padding: 20px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e6e6e6;

  .el-select {
    width: 100%;
  }
}

// 表格容器
.table-container {
  background: #fff;
  padding: 0;

  .el-table {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    overflow: hidden;

    ::v-deep .el-table__header {
      background-color: #fafafa;
    }

    ::v-deep .table-row-hover {
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

// 分页
.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 40px 0;
}

// 响应式设计
@media (max-width: 768px) {
  .news-history {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
    }
  }

  .search-filter {
    padding: 15px 0;

    .el-row {
      .el-col {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .table-container {
    padding: 0;
    overflow-x: auto;

    .el-table {
      min-width: 600px;
    }
  }

  .pagination-wrapper {
    ::v-deep .el-pagination {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
    }
  }
}

// 深度选择器样式调整
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #333;
        font-weight: 600;
      }
    }
  }
}

::v-deep .el-tag {
  border-radius: 4px;
}

::v-deep .el-button--text {
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }
}

::v-deep .el-pagination {
  .btn-prev,
  .btn-next,
  .el-pager li {
    border-radius: 4px;
  }
}
</style>
