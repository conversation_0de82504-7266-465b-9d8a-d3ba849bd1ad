<template>
  <div class="app-container">
    <!--工具条-->
    <el-col :span="24" class="form-line" style="padding-bottom: 0px">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬牌编号">
          <el-input v-model="filters.petNum" placeholder="犬牌编号"></el-input>
        </el-form-item>
        <el-form-item label="饲主名称">
          <el-input
            v-model="filters.ownerName"
            placeholder="饲主名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="犬只名称">
          <el-input v-model="filters.petName" placeholder="犬只名称"></el-input>
        </el-form-item>
        <el-form-item label="犬只性别">
          <el-select
            v-model="filters.petSex"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in petSexs"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div style="float: right; display: flex">
          <el-button
            type="primary"
            size="small"
            plain
            style="margin-right: 10px"
            @click="changeSearchType"
          >
            筛选
            <i v-if="searchType2 == 1" class="el-icon-arrow-down el-icon"></i>
            <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
          </el-button>

          <el-button-group>
            <el-button
              type="primary"
              size="small"
              icon="el-icon-search"
              @click="handleSearch"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              size="small"
              @click="resetParameter"
            >
              重置
            </el-button>
          </el-button-group>
        </div>
      </el-form>
    </el-col>
    <el-col v-if="searchType2 == 2" :span="24" class="form-line">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬只年龄">
          <el-input
            v-model="filters.petAge"
            type="number"
            placeholder="犬只年龄"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-col>
    <!--列表-->
    <template>
      <el-table
        v-loading="loading"
        :data="data"
        highlight-current-row
        stripe
        style="width: 100%"
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          label="犬牌编号"
          :formatter="formats"
          sortable
        ></el-table-column>
        <el-table-column
          prop="ownerName"
          label="饲主名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petName"
          label="犬只名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petSex"
          label="犬只性别"
          :formatter="formatPetSex"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petAge"
          label="犬只年龄"
          :formatter="formatPetAge"
          sortable
        ></el-table-column>
        <el-table-column prop="yearStatus" label="年审情况" sortable>
          <template slot-scope="scope">
            <el-tag
              v-if="formatYearStatus(scope.row) !== undefined"
              :type="scope.row.yearStatus === 1 ? 'warning' : 'success'"
              effect="plain"
            >
              {{ formatYearStatus(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.yearStatus === 2"
              class="btn-text-pd0"
              type="text"
              @click="handleAnnual(scope.row.id)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.yearStatus === 1"
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(scope.row, 2)"
            >
              办理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <!--工具条-->
    <el-pagination
      :current-page="filters.pageNum"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="filters.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <el-dialog
      :title="title"
      :visible.sync="formVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-form ref="form" :model="form" :rules="formRules" label-width="150px">
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="办理方式" prop="aboutMake">
                <el-input
                  v-model="aboutMake"
                  readonly
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <!--              <el-col :span="12">-->
            <!--                <el-form-item label="预约时间" prop="aboutDate">-->
            <!--                  <el-input readonly v-model="aboutDate" auto-complete="off"></el-input>-->
            <!--                </el-form-item>-->
            <!--              </el-col>-->
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="免疫证号" prop="immuneCard">
                <el-input
                  v-model="form.immuneCard"
                  readonly
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注射医院" prop="hospital">
                <el-input
                  v-model="form.hospital"
                  disabled
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注射医生" prop="doctor">
                <el-input
                  v-model="form.doctor"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入注射医生"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="疫苗品牌" prop="vaccineBrand">
                <el-select
                  v-model="form.vaccineBrand"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="vaccineBrandChange"
                >
                  <el-option
                    v-for="item in vaccineBrandArray"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="注射日期" prop="injectionDate">
                <el-date-picker
                  v-model="form.injectionDate"
                  style="width: 100%"
                  :readonly="disabled"
                  value-format="yyyy-MM-dd"
                  type="date"
                  :picker-options="pickerOptions"
                  placeholder="选择日期"
                  @change="dateChange"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="到期日期" prop="injectionEnddate">
                <el-date-picker
                  v-model="form.injectionEnddate"
                  style="width: 100%"
                  :readonly="disabled"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="疫苗批次" prop="vaccineBatch">
                <el-input
                  v-model="form.vaccineBatch"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入疫苗批次"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancelForm">取消</el-button>
        <el-button
          v-if="type === 2"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="submitForm()"
        >
          提交办理
        </el-button>
      </div>
    </el-dialog>

    <AnnualRecord
      :outer-visible="annualVisible"
      :pet-id="petId"
      @callBack="callBack"
    ></AnnualRecord>
  </div>
</template>
<script>
/* eslint-disable */
import { base, doPost, getDownLoadUrl } from '@/api/dog/index'
import AnnualRecord from './AnnualRecord.vue'
import { getHospitalList } from '@/api/dog/hospital'

export default {
  components: { AnnualRecord },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      annualVisible: false,
      petId: '',
      searchType2: 1,
      filters: {
        brandNum: '',
        brandCity: '',
        brandCom: '',
        isRecovery: '',
        isUse: '',
      },
      type: '',
      loading: false,
      data: [],
      total: 0,
      form: {
        id: '',
        sNum: '',
        eNum: '',
        brandNum: '',
        brandCity: '',
        brandCom: '',
        offDate: '',
        remark: '',
        offCom: '',
        isRecovery: 1,
        isUse: 1,
        immuneId: '',
        immuneCard: '',
        injectionDate: '',
        injectionEnddate: '',
      },
      aboutMake: '',
      aboutDate: '',
      rsissue: {},
      formVisible: false,
      title: '',
      formRules: {
        doctor: [
          { required: true, message: '请输入注射医生', trigger: 'blur' },
        ],
        vaccineBrand: [
          { required: true, message: '请选择疫苗品牌', trigger: 'blur' },
        ],
        injectionDate: [
          { required: true, message: '请选择注射日期', trigger: 'blur' },
        ],
        injectionEnddate: [
          { required: true, message: '请选择到期日期', trigger: 'blur' },
        ],
      },
      disabled: false,
      formLoading: false,
      user: {},
      brandCitys: [],
      petHairs: [],
      petVarieties: [],
      petSexs: [
        { value: 1, label: '雄' },
        { value: 2, label: '雌' },
      ],
      options: [], // 地区下拉

      label: '',
      srcList: [],
      types: [
        { label: '观赏', value: 1 },
        { label: '导盲', value: 2 },
        { label: '辅助', value: 3 },
        { label: '其他', value: 4 },
      ],
      sendTypes: [
        { label: '快递', value: 1 },
        { label: '自取', value: 2 },
      ],
      isCollects: [
        { label: '是', value: 1 },
        { label: '否', value: 2 },
      ],
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      idlower: false,

      vaccineBrandArray: [],
      hospitalList: [],
    }
  },
  methods: {
    fetchHospitalList(id) {
      return getHospitalList({ id: id }).then((res) => {
        if (res.code === 200) {
          return res.data.name
        }
        return ''
      })
    },
    //筛选展开
    changeSearchType() {
      if (this.searchType2 === 1) {
        this.searchType2 = 2
      } else {
        this.searchType2 = 1
      }
    },
    resetParameter() {
      this.filters = {}
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.filters.pageNum = val
      this.getPgaeList()
    },
    handleSearch() {
      this.filters.pageNum = 1
      this.getPgaeList()
    },
    callBack: function () {
      this.annualVisible = false
      this.getPgaeList()
    },
    handleAnnual: function (id) {
      this.petId = id
      this.annualVisible = true
    },
    // 获取列表
    getPgaeList: function () {
      this.loading = true
      if (this.user.userType == '2') {
        this.filters.hospitalId = this.user.userQualifi
          ? this.user.userQualifi.id
          : ''
      } else if (this.user.userType == '3') {
        this.filters.petDept =
          this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
            ? ''
            : this.user.deptId
      }
      doPost('/petCertificates/getPetAndRegister', this.filters)
        .then((res) => {
          this.data = res.list
          if (this.data !== null && this.data.length > 0) {
            for (var a in this.data) {
              this.data[a].index =
                (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
            }
          }
          this.total = res.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    dateFormat(dateData) {
      var date = new Date(dateData)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      const time = y + '-' + m + '-' + d
      return time
    },
    randomMY: function () {
      doPost('/immuneRegister/randomMY', {}).then((res) => {
        this.form.immuneCard = res
      })
      // 日期自动选择
      const end = new Date() // 获取当前的日期
      end.setTime(end.getTime())
      // 计算，将当期日期-1天
      // 此时得到的是中国标准时间，格式不是yyyy-MM-dd，需要用dateFormat这个函数转换下
      this.form.injectionDate = this.dateFormat(end)
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 364)
      this.form.injectionEnddate = this.dateFormat(end)
    },
    dateChange(dateData) {
      console.log(dateData)
      const end = new Date(dateData) // 获取当前的日期
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 364)
      this.form.injectionEnddate = this.dateFormat(end)
    },
    //编辑
    handleEdit(row, type) {
      const { id, aboutMake, aboutDate, immuneId } = row
      this.rsissue = {}
      if (type === 1) {
        this.title = '办理详情'
        this.disabled = true
        if (aboutMake === '1') {
          this.aboutMake = '去医院免疫'
        } else if (aboutMake === '2') {
          this.aboutMake = '上门免疫'
        }
        this.aboutDate = aboutDate
        this.getRegisterByPet(id, immuneId)
      } else if (type === 2) {
        this.randomMY() //获取免疫证号
        this.disabled = false
        this.title = '年审办理'
        this.form.aboutMake = aboutMake
        if (aboutMake === '1') {
          this.aboutMake = '去医院免疫'
        } else if (aboutMake === '2') {
          this.aboutMake = '上门免疫'
        }
        this.aboutDate = aboutDate
        this.form = { ...row.immuneRegister, petId: id }
        this.form.type = 2
        this.form.aboutDate = aboutDate
        this.fetchHospitalList(row.immuneRegister.hospitalId).then((res) => {
          this.form.hospital = res
        })
      }
      this.type = type
      this.formVisible = true
    },
    //点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.updateFormVisible = false
      this.disabled = false
      this.form = Object.assign(this.data.form, this.options.data().form)
      this.$refs['form'].resetFields()
    },
    //提交
    submitForm: function () {
      this.form.status = 3
      console.log(this.form)
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            doPost('/immuneRegister/saveOrUpdate', this.form).then((res) => {
              this.formLoading = false
              this.$message({
                message: '办理成功',
                type: 'success',
              })
              this.$refs['form'].resetFields()
              this.formVisible = false
              this.getPgaeList()
            })
          })
        }
      })
    },
    formatPetSex: function (row, column) {
      if (row.petSex === 1) {
        return '雄'
      } else if (row.petSex === 2) {
        return '雌'
      }
    },
    formatPetAge: function (row, column) {
      return row.petAge + '岁'
    },
    formatYearStatus: function (row, column) {

      const Map = {
        '0': '未免疫',
        '1': '已保存',
        '2': '待审核',
        '3': '已免疫',
        '4': '未通过',
        '5': '疫苗已过期',
        '6': '疫苗快过期',
      }

      return Map[row.immuneRegister.status]

      // if (row.yearStatus === 1) {
      //   return '待办理'
      // } else if (row.yearStatus === 2) {
      //   return '已办理'
      // }
    },
    formats: function (row, column) {
      if (
        row.petNum !== null &&
        row.petNum !== undefined &&
        row.petNum !== ''
      ) {
        return row.petNum
      }
      return '暂无犬牌'
    },

    getHairType() {
      var para = {
        dictType: 'hair_type',
      }
      doPost('/sysDict/getAllList', para).then((res) => {
        this.petHairs = res
      })
    },
    getVarietiesType() {
      var para = {
        dictType: 'varieties_type',
      }
      doPost('/sysDict/getAllList', para).then((res) => {
        this.petVarieties = res
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 10

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是JPG格式或PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    // 照片保存
    handleSuccess(file, fileList) {
      this.rsissue.fileUrl = getDownLoadUrl(file.result)
      this.rsissue.doorwayName = file.name
      this.idlower = file.result
    },
    getRegisterByPet(id, immuneId) {
      let para = {
        petId: id,
        immuneId: immuneId,
      }
      doPost('/immuneRegister/getByPetId', para).then((res) => {
        // console.log(res)
        this.form = res
      })
    },
    getVaccine() {
      doPost('/vaccine/getAllList', { createBy: this.user.id }).then((res) => {
        this.vaccineBrandArray = res
      })
    },
    vaccineBrandChange: function (e) {
      let obj = {}
      obj = this.vaccineBrandArray.find((item) => {
        return item.id === e
      })
      // this.form.vaccineBatch = obj.batch// 疫苗批次
      // if (obj.batchSurplus == '0') {
      //   this.$message.error('此疫苗数量为0，不能选择!')
      //   this.form.vaccineBrand = '';
      //   this.form.vaccineBatch = '';
      //   return;
      // } else {
      //   this.form.vaccineBatch = obj.batch// 疫苗批次
      // }
    },
  },
  mounted() {
    var user = JSON.parse(sessionStorage.getItem('user'))
    this.user = user
    if (this.user.userType === 2) {
      this.$set(this.form, 'hospitalId', this.user.userQualifi.id) //医院ID
      this.$set(this.form, 'hospital', this.user.userQualifi.name) //医院ID
    }
    this.qualifi = user.userQualifi
    this.getPgaeList()
    this.getHairType()
    this.getVaccine()
    this.getVarietiesType()
  },
}
</script>

<style scoped>
.img-box img {
  display: block;
  width: 100%;
  height: auto;
  /*margin-top: 3em;*/
}
</style>
