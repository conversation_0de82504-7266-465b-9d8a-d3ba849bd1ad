<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="filters" size="small">
      <el-form-item label="医院名称">
        <el-input v-model="filters.name" placeholder="请输入"></el-input>
      </el-form-item>
      <!-- 类型   0：宠物医院，1：执法部门 2：救助站-->
      <el-form-item label="类型">
        <el-select v-model="filters.type" clearable placeholder="请选择">
          <el-option label="宠物医院" value="0"></el-option>
          <el-option label="执法部门" value="1"></el-option>
          <el-option label="救助站" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="filters.status" clearable placeholder="请选择">
          <!-- 状态 1:审核中，2:已通过，3:未通过 -->
          <el-option label="审核中" value="1"></el-option>
          <el-option label="已通过" value="2"></el-option>
          <el-option label="未通过" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属地区">
        <el-input v-model="filters.area" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
      >
        新增
      </el-button>
      <!-- <el-button type="danger" icon="el-icon-delete" size="small" @click="handleBatchDelete">删除</el-button> -->
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="list"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" type="index" width="50"></el-table-column>
      <el-table-column prop="name" label="医院名称"></el-table-column>
      <el-table-column prop="area" label="所属地区"></el-table-column>
      <el-table-column prop="address" label="详细地址"></el-table-column>
      <el-table-column prop="person" label="负责人"></el-table-column>
      <el-table-column prop="tel" label="联系方式"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '1' ? 'warning' : 'danger'">
            {{ scope.row.status === '1' ? '即将过期' : '已过期' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间"></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleMessage(scope.row)">
            短信提醒
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增弹窗 -->
    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleDialogClose"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        :disabled="['view', 'review'].includes(dialogType)"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医院名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入医院名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.id" label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择" disabled>
                <el-option label="待审核" value="1"></el-option>
                <el-option label="已通过" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="person">
              <el-input
                v-model="form.person"
                placeholder="请输入负责人"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="tel">
              <el-input
                v-model="form.tel"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传区域 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="门头照片" prop="doorway">
              <single-image-upload
                v-model="form.doorway"
                :file-name="form.doorwayName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照" prop="business">
              <single-image-upload
                v-model="form.business"
                :file-name="form.businessName"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人身份证正面" prop="idupper">
              <single-image-upload
                v-model="form.idupper"
                :file-name="form.idupperName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人身份证反面" prop="idlower">
              <single-image-upload
                v-model="form.idlower"
                :file-name="form.idlowerName"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资格证书" prop="qualifiCer">
              <single-image-upload
                v-model="form.qualifiCer"
                :file-name="form.qualifiCerName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医疗资质" prop="qualifi">
              <el-checkbox
                v-model="checkList[0]"
                true-label="1"
                :checked="checkList[0] == 1 ? true : false"
                false-label="0"
                :disabled="['view', 'review'].includes(dialogType)"
              >
                犬牌发放
              </el-checkbox>
              <el-checkbox
                v-model="checkList[1]"
                true-label="1"
                :checked="checkList[1] == 1 ? true : false"
                false-label="0"
                :disabled="['view', 'review'].includes(dialogType)"
              >
                免疫资格
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="统一信用代码" prop="unifiedCode">
              <el-input
                v-model="form.unifiedCode"
                placeholder="请输入统一社会信用代码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="所属地区" prop="area">
              <div class="area-select">
                <span>浙江省/金华市/</span>
                <el-select v-model="form.county" placeholder="请选择">
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="form.address"
                placeholder="请输入详细地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="dialogType === 'view' && form.reason">
          <el-col :span="24">
            <el-form-item label="审批意见" prop="reason">
              <el-input
                v-model="form.reason"
                placeholder="请输入审批意见"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button
          v-if="['edit', 'add'].includes(dialogType)"
          type="primary"
          @click="handleSubmit"
        >
          确 定
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="danger"
          @click="handleReviewSubmit('no')"
        >
          不通过
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="primary"
          @click="handleReviewSubmit('pass')"
        >
          通 过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { base, doGet, doPost } from '@/api/dog/index'
import SingleImageUpload from '@/components/Upload/SingleImageUpload.vue'
import { saveOrUpdate, sendMobile, updateStatus } from '@/api/dog/hospital'

export default {
  name: 'AnnualReviewList',
  components: {
    SingleImageUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 总条数
      total: 0,
      // 列表数据
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 筛选条件
      filters: {
        name: '',
        status: '',
        area: '',
      },
      // 新增弹窗相关数据
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      form: {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        county: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
      },
      rules: {
        name: [{ required: true, message: '请输入医院名称', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        person: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        doorway: [
          { required: true, message: '请上传门头照片', trigger: 'change' },
        ],
        business: [
          { required: true, message: '请上传营业执照', trigger: 'change' },
        ],
        idupper: [
          { required: true, message: '请上传身份证正面', trigger: 'change' },
        ],
        idlower: [
          { required: true, message: '请上传身份证反面', trigger: 'change' },
        ],
        qualifiCer: [
          { required: true, message: '请上传资格证书', trigger: 'change' },
        ],
        qualifi: [
          {
            validator: (rule, value, callback) => {
              if (this.checkList[0] === '0' && this.checkList[1] === '0') {
                return callback(new Error('请至少选择一项医疗资质'))
              }
              callback()
            },
            trigger: 'change',
          },
        ],
        unifiedCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur',
          },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                return callback()
              }
              // 去除前后空格
              const trimmedValue = value.trim()
              // 更新表单中的值，去除空格
              this.form.unifiedCode = trimmedValue
              const pattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
              if (!pattern.test(trimmedValue)) {
                return callback(new Error('请输入正确的统一社会信用代码'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        area: [
          { required: true, message: '请选择所属地区', trigger: 'change' },
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
        ],
      },
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      // 区域选项
      areaOptions: [
        { label: '婺城区', value: '330702' },
        { label: '金东区', value: '330703' },
        { label: '武义县', value: '330723' },
        { label: '浦江县', value: '330726' },
        { label: '磐安县', value: '330727' },
        { label: '兰溪市', value: '330781' },
        { label: '义乌市', value: '330782' },
        { label: '东阳市', value: '330783' },
        { label: '永康市', value: '330784' },
      ],
      deptList: [],
      checkList: ['1', '1'],
    }
  },
  computed: {
    user() {
      return JSON.parse(sessionStorage.getItem('user')) || {}
    },
  },
  created() {
    this.getList()
    this.getdeptList()
  },
  methods: {
    getdeptList() {
      doPost('/dept/getAllList').then((res) => {
        console.log(res)
        this.deptList = res.filter((item) => item.level === 2)
      })
    },
    async getList() {
      this.loading = true
      try {
        const params = {
          ...this.queryParams,
          ...this.filters,
        }
        const res = await doGet('/hospital/year/alarmList', params)
        this.list = res.list
        this.total = res.total
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },
    resetQuery() {
      this.filters = {
        name: '',
        status: '',
        area: '',
      }
      this.getList()
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
    },
    handleAdd() {
      this.dialogTitle = '新增医院资质'
      this.dialogType = 'add'
      this.dialogVisible = true
      this.checkList = ['0', '0'] // 初始化医疗资质
      this.form = {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        county: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
      }
    },
    handleDialogClose() {
      this.$refs.form?.resetFields()
      this.checkList = ['0', '0'] // 重置医疗资质
      this.form = {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        county: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
      }
      this.dialogVisible = false
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        // 处理医疗资质数据
        this.form.qualifi = this.checkList.join(',')
        this.form.uploadFileStr = this.genUploadFileStr()

        await saveOrUpdate(this.form)

        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getList()
      } catch (error) {
        console.error(error)
      }
    },
    handleEdit(row) {
      return this.getHospitalDetail(row, 'edit')
    },
    handleView(row) {
      return this.getHospitalDetail(row, 'view')
    },
    handleMessage(row) {
      console.log(row)
      this.$confirm('是否发送短信提醒?', '短信提醒', {
        type: 'warning',
      }).then(() => {
        sendMobile(row.id).then((res) => {
          if (res.code === 200) {
            this.$message.success('发送短信提醒成功')
          } else {
            this.$message.error('发送短信提醒失败')
          }
        })
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('是否确认删除该记录?', '警告', {
          type: 'warning',
        })
        await this.$api.hospital.deleteAnnualReview(row.id)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        console.error(error)
      }
    },
    async handleBatchDelete() {
      if (this.ids.length === 0) {
        return this.$message.warning('请选择要删除的记录')
      }
      try {
        await this.$confirm(`是否确认删除这${this.ids.length}条记录?`, '警告', {
          type: 'warning',
        })
        await this.$api.hospital.batchDeleteAnnualReview(this.ids)
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        console.error(error)
      }
    },
    // 上传成功回调方法
    handleDoorwaySuccess(res) {
      this.form.doorway = res.data.url
    },
    handleBusinessSuccess(res) {
      this.form.business = res.data.url
    },
    handleIdUpperSuccess(res) {
      this.form.idupper = res.data.url
    },
    handleIdLowerSuccess(res) {
      this.form.idlower = res.data.url
    },
    handleQualifiCerSuccess(res) {
      this.form.qualifiCer = res.data.url
    },
    // 生成上传文件字符串
    genUploadFileStr() {
      const imgFields = [
        'doorway',
        'business',
        'idupper',
        'idlower',
        'qualifiCer',
      ]
      const fileConfigs = []
      imgFields.forEach((field) => {
        if (this.form[field]) {
          fileConfigs.push({
            fileUrl: this.form[field],
            fileName: this.form[field + 'Name'],
            modelType: field,
          })
        }
      })

      if (fileConfigs.length > 0) {
        return JSON.stringify(fileConfigs)
      }
      return ''
    },
    // 批量处理文件
    handleFileList(resource, list) {
      list.forEach((item) => {
        const modelType = item.modelType
        resource[modelType] = item.fileUrl
        resource[modelType + 'Name'] = item.fileName
      })
    },
    // 获取医院详情并显示弹窗
    async getHospitalDetail(row, type) {
      try {
        const res = await doGet('hospital/year/getById', { id: row.id })
        const dialogTitles = {
          edit: '编辑医院资质',
          view: '查看医院资质',
          review: '审核医院资质',
        }

        this.dialogTitle = dialogTitles[type]
        this.dialogType = type
        this.dialogVisible = true
        this.form = { ...res }

        // 处理医疗资质数据
        if (this.form.qualifi) {
          this.checkList = this.form.qualifi.split(',')
        } else {
          this.checkList = ['0', '0']
        }

        // 查看和审核需要处理文件列表
        this.handleFileList(this.form, res.uploadFileList)
      } catch (error) {
        console.error('获取医院详情失败:', error)
        this.$message.error('获取医院详情失败')
      }
    },
    // 审核
    handleReview(row) {
      return this.getHospitalDetail(row, 'review')
    },

    // 审核提交
    handleReviewSubmit(type) {
      if (type === 'pass') {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatus({
            lawStatus: 2,
            account: this.form.account,
            status: 2,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批通过')
            this.dialogVisible = false
            this.getList()
          })
        })
      } else {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatus({
            lawStatus: 3,
            account: this.form.account,
            status: 3,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批不通过')
            this.dialogVisible = false
            this.getList()
          })
        })
      }
    },
  },
}
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
}

.area-select {
  display: flex;
  align-items: center;
}

.area-select span {
  margin-right: 10px;
}

.upload-demo {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
}
</style>
