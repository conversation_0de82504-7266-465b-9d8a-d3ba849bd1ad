<template>
  <div class="app-container">
    <!--工具条-->
    <el-col :span="24" class="form-line" style="padding-bottom: 0px">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬牌编号">
          <el-input v-model="filters.petNum" placeholder="犬牌编号"></el-input>
        </el-form-item>
        <el-form-item label="饲主名称">
          <el-input
            v-model="filters.ownerName"
            placeholder="饲主名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="犬只名称">
          <el-input v-model="filters.petName" placeholder="犬只名称"></el-input>
        </el-form-item>
        <el-form-item label="犬牌状态">
          <el-select
            v-model="filters.isReissue"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div style="float: right; display: flex">
          <el-button
            type="primary"
            size="small"
            plain
            style="margin-right: 10px"
            @click="changeSearchType"
          >
            筛选
            <i v-if="searchType2 == 1" class="el-icon-arrow-down el-icon"></i>
            <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
          </el-button>

          <el-button-group>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="small"
              @click="handleSearch"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              size="small"
              @click="resetParameter"
            >
              重置
            </el-button>
          </el-button-group>
        </div>
      </el-form>
    </el-col>
    <el-col v-if="searchType2 == 2" :span="24" class="form-line">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬只性别">
          <el-select
            v-model="filters.petSex"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in petSexs"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="犬只年龄">
          <el-input
            v-model="filters.petAge"
            type="number"
            placeholder="犬只年龄"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-col>
    <!--列表-->
    <template>
      <el-table
        v-loading="loading"
        :data="data"
        highlight-current-row
        style="width: 100%"
        stripe
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          label="犬牌编号"
          :formatter="formats"
          sortable
        ></el-table-column>
        <el-table-column
          prop="ownerName"
          label="饲主名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petName"
          label="犬只名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petSex"
          label="犬只性别"
          :formatter="formatPetSex"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petAge"
          label="犬只年龄"
          :formatter="formatPetAge"
          sortable
        ></el-table-column>
        <el-table-column prop="isReissue" label="犬牌状态" sortable>
          <template slot-scope="scope">
            <el-tag
              :type="
                scope.row.isReissue === 1
                  ? 'warning'
                  : scope.row.isReissue === 2
                  ? 'success'
                  : 'danger'
              "
            >
              {{ formatIsReissue(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(scope.row.id, 1)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.isReissue === 1"
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(scope.row.id, 2)"
            >
              去审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <!--工具条-->
    <el-pagination
      :current-page="filters.pageNum"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="filters.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <el-dialog
      :title="title"
      :visible.sync="formVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="补办信息" name="first">
          <el-form
            ref="form"
            :model="form"
            :rules="formRules"
            label-width="150px"
          >
            <div style="padding-top: 10px">
              <div class="section-title">饲主信息</div>
              <el-divider></el-divider>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="饲主姓名" prop="ownerName">
                    <el-input
                      v-model="form.ownerName"
                      disabled
                      auto-complete="off"
                      placeholder="请输入饲主姓名"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证" prop="petIdCard">
                    <el-input
                      v-model="form.petIdCard"
                      disabled
                      auto-complete="off"
                      placeholder="请输入饲主身份证号"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="户籍地址" prop="ownerAddress">
                    <el-input
                      v-model="form.ownerAddress"
                      disabled
                      auto-complete="off"
                      placeholder="请输入户籍地址"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="tel">
                    <el-input
                      v-model="form.tel"
                      disabled
                      auto-complete="off"
                      placeholder="请输入饲主联系电话"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证照片（正面）" prop="posiCard">
                    <el-image
                      v-if="posiCardPath"
                      :src="form.posiCard"
                      :preview-src-list="[form.posiCard]"
                      fit="cover"
                      class="w-[200px] h-[100px] block"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证照片（反面）" prop="sideCard">
                    <el-image
                      v-if="sideCardPath"
                      :src="form.sideCard"
                      :preview-src-list="[form.sideCard]"
                      fit="cover"
                      class="w-[200px] h-[100px] block"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="户口本照片" prop="residence">
                    <el-image
                      v-for="(item, index) in residenceImgList"
                      :key="index"
                      :src="item.url"
                      :preview-src-list="residenceImgList.map(img => img.url)"
                      class="el-upload-list__item-thumbnail rounded-md w-[148px] h-[148px] mr-2"
                      fit="cover"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="房产证/租赁合同照片" prop="property">
                    <el-image
                      v-for="(item, index) in propertyImgList"
                      :key="index"
                      :src="item.url"
                      :preview-src-list="propertyImgList.map(img => img.url)"
                      class="el-upload-list__item-thumbnail rounded-md w-[148px] h-[148px] mr-2"
                      fit="cover"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="section-title">宠物信息</div>
              <el-divider></el-divider>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item v-if="fileList.length > 0" label="宠物照片">
                    <el-image
                      v-for="(item, index) in fileList"
                      :key="index"
                      :src="item.url"
                      :preview-src-list="fileList.map(img => img.url)"
                      class="w-[100px] h-[100px] mr-2"
                      fit="cover"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="宠物名字" prop="petName">
                    <el-input
                      v-model="form.petName"
                      disabled
                      auto-complete="off"
                      placeholder="请输入犬只名称"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物性别" prop="petSex">
                    <el-select
                      v-model="form.petSex"
                      disabled
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petSexs"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物品种" prop="petVarieties">
                    <el-select
                      v-model="form.petVarieties"
                      disabled
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petVarieties"
                        :key="item.dictKey"
                        :label="item.name"
                        :value="item.dictKey"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="form.petVarieties === '107'" :span="12">
                  <el-form-item label="其他品种" prop="otherVarieties">
                    <el-input
                      v-model="form.otherVarieties"
                      disabled
                      auto-complete="off"
                      placeholder="请输入其他品种名称"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物毛色" prop="petHair">
                    <el-select
                      v-model="form.petHair"
                      disabled
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in petHairs"
                        :key="item.dictKey"
                        :label="item.name"
                        :value="item.dictKey"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="宠物年龄" prop="petAge">
                    <el-input
                      v-model="form.petAge"
                      type="number"
                      disabled
                      auto-complete="off"
                      placeholder="请输入犬只年龄"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="饲养日期" prop="raiseDate">
                    <el-date-picker
                      v-model="form.raiseDate"
                      style="width: 100%"
                      disabled
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="养宠地址" prop="petDept">
                    <div style="display: flex; align-items: center">
                      <div style="width: 110px">浙江省/金华市/</div>
                      <el-cascader
                        :key="modalKey"
                        v-model="area"
                        style="flex: 1"
                        disabled
                        :options="options"
                        :props="cateProps"
                        clearable
                      ></el-cascader>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="" prop="petAddress">
                    <el-input
                      v-model="form.petAddress"
                      type="textarea"
                      disabled
                      auto-complete="off"
                      placeholder="请输入详细地址"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="旧犬牌号" prop="petNum">
                    <el-input
                      v-model="rsissue.brandNum"
                      disabled
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="新犬牌号" prop="dogBrand">
                    <el-input
                      v-model="rsissue.dogBrand"
                      disabled
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用途方式" prop="type">
                    <el-select
                      v-model="rsissue.type"
                      disabled
                      style="width: 100%"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in types"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  v-if="rsissue.type === 2 || rsissue.type === 3"
                  :span="12"
                >
                  <el-form-item :label="label">
                    <el-image
                      style="width: 200px; height: 100px"
                      :src="rsissue.fileUrl"
                      :preview-src-list="[rsissue.fileUrl]"
                      class="w-[200px] h-[100px]"
                      fit="cover"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="rsissue.sendType !== 0" :gutter="20">
                <el-col :span="12">
                  <el-form-item label="寄送方式" prop="sendType">
                    <el-select
                      v-model="rsissue.sendType"
                      disabled
                      style="width: 100%"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in sendTypes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="rsissue.sendType === 1" :span="12">
                  <el-form-item label="费用到付" prop="isCollect">
                    <el-select
                      v-model="rsissue.isCollect"
                      disabled
                      style="width: 100%"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in isCollects"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="rsissue.sendType === 1" :gutter="20">
                <el-col :span="12">
                  <el-form-item label="收件人">
                    <el-input
                      v-model="rsissue.name"
                      readonly
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系方式">
                    <el-input
                      v-model="rsissue.phone"
                      readonly
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="rsissue.sendType !== 0" :gutter="20">
                <el-col :span="12">
                  <el-form-item label="收货地址" prop="area">
                    <el-input
                      v-model="rsissue.area"
                      readonly
                      auto-complete="off"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="详细地址" prop="address">
                    <el-input
                      v-model="rsissue.address"
                      readonly
                      auto-complete="off"
                      placeholder="请输入收货地址"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="备注" prop="content">
                    <el-input
                      v-model="rsissue.content"
                      type="textarea"
                      readonly
                      auto-complete="off"
                      placeholder="请输入备注"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="审核意见" prop="reason">
                    <el-input
                      v-model="rsissue.reason"
                      :readonly="disabled"
                      auto-complete="off"
                      placeholder="请输入审核意见"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="type == '1'" label="操作记录" name="two">
          <el-table
            v-loading="loading"
            :data="recordList"
            highlight-current-row
            style="width: 100%"
            stripe
          >
            <el-table-column prop="createName" label="操作人"></el-table-column>
            <el-table-column
              prop="createDate"
              label="操作时间"
            ></el-table-column>
            <el-table-column
              prop="oldPetNum"
              label="旧犬牌号"
            ></el-table-column>
            <el-table-column prop="petNum" label="新犬牌号"></el-table-column>
            <el-table-column
              label="类型"
              :formatter="formatNode"
            ></el-table-column>
            <el-table-column
              label="状态"
              :formatter="formatRecordStatus"
            ></el-table-column>
            <el-table-column
              prop="remark"
              label="原因/审批意见"
            ></el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancelForm">取消</el-button>
        <el-button
          v-if="type === 2"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="submitForm(2)"
        >
          补办通过
        </el-button>
        <el-button
          v-if="type === 2"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="submitForm(3)"
        >
          补办未通过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
/* eslint-disable */
import { doPost, getDownLoadUrl } from '@/api/dog/index'

export default {
  data() {
    return {
      searchType2: 1, //控制展开查询条件
      filters: {
        //查询条件字段
        brandNum: '',
        brandCity: '',
        brandCom: '',
        isRecovery: '',
        isUse: '',
      },
      type: '', //1详情，2编辑
      loading: false, //列表加载
      data: [], //列表数据
      total: 0, //列表总数
      rsissue: {}, //回显过来的犬牌补办申请信息
      form: {
        //主表单
        id: '',
        sNum: '',
        eNum: '',
        brandNum: '',
        brandCity: '',
        brandCom: '',
        offDate: '',
        remark: '',
        offCom: '',
        isRecovery: 1,
        isUse: 1,
      },
      formVisible: false, //表单页面展示
      title: '', //表单标题
      formRules: {
        //表单验证
      },
      disabled: false, //表单控制可输入
      formLoading: false, //表单提交加载
      user: {}, //登录用户数据(医院登录包含医院信息)
      brandCitys: [], //地址
      petHairs: [], //毛色
      petSexs: [
        //性别
        { value: 1, label: '雄' },
        { value: 2, label: '雌' },
      ],
      types: [
        { label: '观赏', value: 1 },
        { label: '导盲', value: 2 },
        { label: '辅助', value: 3 },
        { label: '其他', value: 4 },
      ],
      sendTypes: [
        { label: '快递', value: 1 },
        { label: '自取', value: 2 },
      ],
      isCollects: [
        { label: '是', value: 1 },
        { label: '否', value: 2 },
      ],
      statusList: [
        { label: '待审核', value: '1' },
        { label: '已通过', value: '2' },
        { label: '未通过', value: '3' },
      ],
      petVarieties: [], //品种
      options: [], // 地区下拉
      label: '',
      cateProps: {
        label: 'deptName',
        children: 'children',
        value: 'id',
      },
      recordList: [],
      nodeList: [
        { dictKey: 4, name: '犬牌补办' },
        { dictKey: 5, name: '补办审核' },
        { dictKey: 7, name: '犬证激活' },
      ],
      recordStatusList: [
        { dictKey: 1, name: '已提交' },
        { dictKey: 2, name: '已通过' },
        { dictKey: 3, name: '未通过' },
        { dictKey: 4, name: '已激活' },
      ],
      activeName: 'first',
      fileList: [],
      propertyImgList: [], //户口本照片集合
      residenceImgList: [], //租房合同照片集合
      area: [],
      residencePath: '',
      propertyPath: '',
      posiCardPath: '',
      sideCardPath: '',
      petTypes: [], // 宠物类别
      modalKey: 0,
      photoVisible: false,
      bigImgUrl: '',
    }
  },
  methods: {
    //筛选展开
    changeSearchType() {
      if (this.searchType2 === 1) {
        this.searchType2 = 2
      } else {
        this.searchType2 = 1
      }
    },
    resetParameter() {
      this.filters = {}
      this.getPgaeList()
    },
    handleSearch() {
      this.filters.pageNum = 1
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.filters.pageNum = val
      this.getPgaeList()
    },
    // 获取列表
    getPgaeList: function () {
      this.loading = true
      this.filters.isReissueFlag = 1
      if (this.user.userType == '3') {
        this.filters.petDept =
          this.user.dogDeptId == '753f3c429c5d462cb5e9fa1113461128'
            ? ''
            : this.user.dogDeptId
      }
      doPost('/petCertificates/getPageList', this.filters)
        .then((res) => {
          this.data = res.list
          if (this.data !== null && this.data.length > 0) {
            for (var a in this.data) {
              this.data[a].index =
                (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
            }
          }
          this.total = res.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getRecord(id) {
      doPost('/petRecord/getList', { petId: id }).then((res) => {
        if (res.length > 0) {
          this.recordList = res.filter((item) => {
            return item.node === 4 || item.node === 5 || item.node === 7
          })
        } else {
          this.recordList = []
        }
      })
    },
    //编辑
    handleEdit: function (id, type) {
      var that = this
      if (type === 1) {
        that.title = '申请详情'
        that.disabled = true
        that.getRecord(id)
      } else if (type === 2) {
        that.title = '犬牌补办审核'
        that.disabled = false
      }
      that.getRsissueByPet(id)
      that.type = type
      that.formVisible = true
      let para = {
        id: id,
      }
      that.modalKey++
      doPost('/petCertificates/getById', para).then((res) => {
        that.form = res
        that.area = [that.form.petDept, that.form.street]
        const fileList = that.form.uploadFiles
        for (const i in fileList) {
          if (
            fileList[i].modelType == 'petImgZ' ||
            fileList[i].modelType == 'petImgF'
          ) {
            that.fileList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'residence') {
            //户口本照片集合
            that.residenceImgList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'property') {
            //房产证/租赁合同照片集合
            that.propertyImgList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'posiCard') {
            that.posiCardPath = fileList[i].fileUrl
            that.$set(
              that.form,
              'posiCard',
              getDownLoadUrl(fileList[i].fileUrl)
            )
          } else if (fileList[i].modelType == 'sideCard') {
            that.sideCardPath = fileList[i].fileUrl
            that.$set(
              that.form,
              'sideCard',
              getDownLoadUrl(fileList[i].fileUrl)
            )
          }
        }
        delete that.form.uploadFiles
      })
    },
    //点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.activeName = 'first'
      this.disabled = false
      this.fileList = []
      this.propertyImgList = [] //户口本照片集合
      this.residenceImgList = [] //租房合同照片集合
      this.form = {
        id: '',
        brandNum: '',
        brandCity: '',
        brandCom: '',
        offDate: '',
        remark: '',
        offCom: '',
        isRecovery: 1,
        isUse: 1,
      }
      this.$refs['form'].resetFields()
    },
    //提交
    submitForm: function (status) {
      this.form.isReissue = status
      if (status === 3) {
        if (
          this.rsissue.reason === null ||
          this.rsissue.reason === '' ||
          this.rsissue.reason === undefined
        ) {
          this.$message({
            message: '请输入审核意见',
            type: 'error',
          })
          return
        }
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            // this.formLoading = true
            let para = {
              id: this.rsissue.id,
              brandNum: this.rsissue.brandNum,
              dogBrand: this.rsissue.dogBrand,
              petId: this.form.id,
              status: status,
              reason: this.rsissue.reason,
              sendType:
                this.rsissue.sendType === 0
                  ? 0
                  : this.rsissue.sendType === 1
                  ? 2
                  : 1,
              address: this.rsissue.address,
              area: this.rsissue.area,
              zfckId: this.rsissue.zfckId,
            }
            doPost('/reissue/updateStatus', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: res,
                type: 'success',
              })
              this.$refs['form'].resetFields()
              this.formVisible = false
              this.getPgaeList()
            })
          })
        }
      })
    },

    formatPetSex: function (row, column) {
      if (row.petSex === 1) {
        return '雄'
      } else if (row.petSex === 2) {
        return '雌'
      }
    },
    formatPetAge: function (row, column) {
      return row.petAge + '岁'
    },
    formatIsReissue: function (row, column) {
      // if (row.isReissue === 1) {
      //   return '正常'
      // } else if (row.isReissue === 2) {
      //   return '已申请'
      // } else if (row.isReissue === 3) {
      //   return '未通过'
      // } else if (row.isReissue === 4) {
      //   return '未通过'
      // }

      if (row.isReissue === 1) {
        return '待审核'
      } else if (row.isReissue === 2) {
        return '已通过'
      } else if (row.isReissue === 3) {
        return '未通过'
      } else {
        return ''
      }
    },
    formats: function (row, column) {
      if (
        row.petNum !== null &&
        row.petNum !== undefined &&
        row.petNum !== ''
      ) {
        return row.petNum
      }
      return '暂无犬牌'
    },

    //列表显示
    formatNode: function (row, column) {
      let str = ''
      this.nodeList.forEach(function (item) {
        if (item.dictKey == row.node) {
          str = item.name
        }
      })
      return str
    },
    formatRecordStatus: function (row, column) {
      let str = ''
      this.recordStatusList.forEach(function (item) {
        if (item.dictKey == row.status) {
          str = item.name
        }
      })
      return str
    },

    getHairType() {
      doPost('/sysDict/getAllList', { dictType: 'varieties_type' }).then(
        (res) => {
          this.petVarieties = res
        }
      )
      doPost('/dept/getSecondDeptTree', {}).then((res) => {
        this.options = res
      })
      doPost('/sysDict/getAllList', { dictType: 'hair_type' }).then((res) => {
        this.petHairs = res
      })
      // 查询宠物类型
      // doPost('/sysDict/getAllList', { dictType: 'pet_type' }).then(res => {
      //     this.petTypes = res
      // })
    },
    getRsissueByPet(petId) {
      let para = {
        petId: petId,
      }
      doPost('/reissue/getByPetId', para).then((res) => {
        if (res) {
          this.rsissue = res
        } else {
          this.rsissue = this.$options.data().rsissue
        }
        console.log(this.rsissue)
      })
    },
    showBigImage(e) {
      //点击图片函数，点击后，把photoVisible设置成true
      if (e != '') {
        this.photoVisible = true
        this.bigImgUrl = e.currentTarget.src
      }
    },
    closeClick() {
      this.photoVisible = false
    },
  },
  mounted() {
    var user = JSON.parse(sessionStorage.getItem('user'))
    this.user = user
    this.getPgaeList()
    this.getHairType()
  },
}
</script>
