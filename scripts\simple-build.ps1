Write-Host "开始构建项目..." -ForegroundColor Green

# 运行构建命令
npm run build:prod

if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败" -ForegroundColor Red
    exit 1
}

Write-Host "构建成功！" -ForegroundColor Green

# 检查 dist 目录是否存在
if (-not (Test-Path "dist")) {
    Write-Host "错误: dist 目录不存在" -ForegroundColor Red
    exit 1
}

Write-Host "开始创建压缩包..." -ForegroundColor Green

# 删除已存在的压缩包
if (Test-Path "dist.zip") {
    Remove-Item "dist.zip" -Force
}

# 创建压缩包
Compress-Archive -Path "dist\*" -DestinationPath "dist.zip" -CompressionLevel Optimal

if (Test-Path "dist.zip") {
    $fileSize = (Get-Item "dist.zip").Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "压缩包创建成功: dist.zip" -ForegroundColor Green
    Write-Host "压缩包大小: $fileSizeMB MB" -ForegroundColor Cyan
} else {
    Write-Host "压缩包创建失败" -ForegroundColor Red
    exit 1
}

Write-Host "构建和打包完成！" -ForegroundColor Green
