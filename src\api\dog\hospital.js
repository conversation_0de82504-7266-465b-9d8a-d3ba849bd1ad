import request from '@/utils/request'

// 新增/更新
export function saveOrUpdate(data) {
  return request({
    url: '/hospital/saveOrUpdate',
    method: 'post',
    data: data,
  })
}

/**
 * 审核
 */
export function updateStatus(data) {
  return request({
    url: '/hospital/updateStatus',
    method: 'post',
    data: data,
  })
}

/**
 * 新增/更新
 */
export function saveOrUpdateYear(data) {
  return request({
    url: '/hospital/year/saveOrUpdate',
    method: 'post',
    data: data,
  })
}

/**
 * 审核
 */
export function updateStatusYear(data) {
  return request({
    url: '/hospital/year/updateStatus',
    method: 'post',
    data: data,
  })
}

/**
 * 短信提醒
 */
export function sendMobile(id) {
  return request({
    url: `/hospital/year/sendMobile/${id}`,
    method: 'get',
  })
}

/**
 * 新增/编辑  犬牌领用审核
 */
export function saveOrUpdateBrand(data) {
  return request({
    url: '/brandApply/saveOrUpdate',
    method: 'post',
    data: data,
  })
}

/**
 * 获取医院列表
 */
export function getHospitalList(params) {
  return request({
    url: '/hospital/getById',
    method: 'get',
    params: params,
  })
}

/**
 * 审核
 */
export function updateBrandStatus(data) {
  return request({
    url: '/brandApply/updateStatus',
    method: 'post',
    data: data,
  })
}
