<template>
  <div class="app-container volunteer-page">
    <div class="page-header">
      <h1 class="page-title">568志愿服务平台</h1>
      <p class="page-subtitle">传递爱心，服务社会，共建美好家园</p>
    </div>

    <!-- 统计数据 -->
    <div class="stats-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">1,234</div>
              <div class="stat-label">注册志愿者</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">89</div>
              <div class="stat-label">进行中活动</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">5,678</div>
              <div class="stat-label">服务时长(小时)</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-trophy"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">456</div>
              <div class="stat-label">完成活动</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 功能入口 -->
    <div class="function-grid">
      <div class="function-card" @click="handleFunction('register')">
        <div class="function-icon">
          <i class="el-icon-user-solid"></i>
        </div>
        <div class="function-title">志愿者注册</div>
        <div class="function-desc">成为志愿者，参与公益活动</div>
      </div>

      <div class="function-card" @click="handleFunction('activities')">
        <div class="function-icon">
          <i class="el-icon-calendar"></i>
        </div>
        <div class="function-title">活动报名</div>
        <div class="function-desc">查看并报名志愿服务活动</div>
      </div>

      <div class="function-card" @click="handleFunction('my-activities')">
        <div class="function-icon">
          <i class="el-icon-tickets"></i>
        </div>
        <div class="function-title">我的活动</div>
        <div class="function-desc">查看我参与的志愿活动</div>
      </div>

      <div class="function-card" @click="handleFunction('hours')">
        <div class="function-icon">
          <i class="el-icon-time"></i>
        </div>
        <div class="function-title">服务时长</div>
        <div class="function-desc">查看我的志愿服务时长</div>
      </div>

      <div class="function-card" @click="handleFunction('certificate')">
        <div class="function-icon">
          <i class="el-icon-medal"></i>
        </div>
        <div class="function-title">荣誉证书</div>
        <div class="function-desc">下载志愿服务证书</div>
      </div>

      <div class="function-card" @click="handleFunction('organization')">
        <div class="function-icon">
          <i class="el-icon-office-building"></i>
        </div>
        <div class="function-title">组织管理</div>
        <div class="function-desc">志愿组织活动发布管理</div>
      </div>
    </div>

    <!-- 最新活动 -->
    <div class="activities-section">
      <div class="section-header">
        <h2>最新活动</h2>
        <el-button type="text" @click="viewAllActivities">查看全部</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="8" v-for="activity in latestActivities" :key="activity.id">
          <el-card class="activity-card" :body-style="{ padding: '0px' }">
            <img :src="activity.image" class="activity-image">
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-info">
                <div class="activity-time">
                  <i class="el-icon-time"></i>
                  {{ activity.time }}
                </div>
                <div class="activity-location">
                  <i class="el-icon-location"></i>
                  {{ activity.location }}
                </div>
              </div>
              <div class="activity-participants">
                已报名：{{ activity.participants }}/{{ activity.maxParticipants }}人
              </div>
              <div class="activity-actions">
                <el-button type="primary" size="small" @click="joinActivity(activity)">
                  立即报名
                </el-button>
                <el-button type="text" size="small" @click="viewActivity(activity)">
                  查看详情
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Volunteer',
  data() {
    return {
      latestActivities: [
        {
          id: 1,
          title: '社区环境清洁志愿活动',
          time: '2024-01-20 09:00',
          location: '金华市区各社区',
          participants: 45,
          maxParticipants: 60,
          image: 'https://via.placeholder.com/300x200?text=环境清洁'
        },
        {
          id: 2,
          title: '关爱老人志愿服务',
          time: '2024-01-22 14:00',
          location: '金华市养老院',
          participants: 23,
          maxParticipants: 30,
          image: 'https://via.placeholder.com/300x200?text=关爱老人'
        },
        {
          id: 3,
          title: '交通文明劝导',
          time: '2024-01-25 07:30',
          location: '市区主要路口',
          participants: 18,
          maxParticipants: 25,
          image: 'https://via.placeholder.com/300x200?text=交通劝导'
        }
      ]
    }
  },
  methods: {
    handleFunction(type) {
      switch (type) {
        case 'register':
          this.$message.info('志愿者注册功能开发中...')
          break
        case 'activities':
          this.$message.info('活动报名功能开发中...')
          break
        case 'my-activities':
          this.$message.info('我的活动功能开发中...')
          break
        case 'hours':
          this.$message.info('服务时长功能开发中...')
          break
        case 'certificate':
          this.$message.info('荣誉证书功能开发中...')
          break
        case 'organization':
          this.$message.info('组织管理功能开发中...')
          break
      }
    },
    
    viewAllActivities() {
      this.$message.info('查看全部活动功能开发中...')
    },
    
    joinActivity(activity) {
      this.$confirm(`确认报名参加"${activity.title}"活动吗？`, '确认报名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.$message.success('报名成功！请按时参加活动。')
      }).catch(() => {
        this.$message.info('已取消报名')
      })
    },
    
    viewActivity(activity) {
      this.$message.info(`查看活动"${activity.title}"详情功能开发中...`)
    }
  }
}
</script>

<style scoped lang="scss">
.volunteer-page {
  padding: 20px;

  .page-header {
    text-align: center;
    margin-bottom: 30px;

    .page-title {
      font-size: 28px;
      color: #333;
      margin-bottom: 10px;
    }

    .page-subtitle {
      font-size: 16px;
      color: #666;
      margin: 0;
    }
  }

  .stats-container {
    margin-bottom: 30px;

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;

      .stat-icon {
        font-size: 32px;
        color: #409eff;
        margin-right: 15px;
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
          margin-top: 5px;
        }
      }
    }
  }

  .function-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;

    .function-card {
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }

      .function-icon {
        font-size: 32px;
        color: #409eff;
        margin-bottom: 12px;
      }

      .function-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }

      .function-desc {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }
    }
  }

  .activities-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h2 {
        margin: 0;
        color: #333;
      }
    }

    .activity-card {
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }

      .activity-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
      }

      .activity-content {
        padding: 16px;

        .activity-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 12px;
        }

        .activity-info {
          margin-bottom: 12px;

          .activity-time,
          .activity-location {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;

            i {
              margin-right: 4px;
            }
          }
        }

        .activity-participants {
          font-size: 14px;
          color: #409eff;
          margin-bottom: 12px;
        }

        .activity-actions {
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .volunteer-page {
    padding: 10px;

    .stats-container {
      .el-col {
        margin-bottom: 10px;
      }
    }

    .function-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .activities-section {
      .el-col {
        margin-bottom: 15px;
      }
    }
  }
}
</style>
