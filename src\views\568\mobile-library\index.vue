<template>
  <div class="library-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="流动书吧">
    </el-page-header>

    <!-- 页面描述 -->
    <!-- <div class="page-description">
      <p>发现知识的海洋，享受阅读的乐趣</p>
    </div> -->

    <!-- 书籍分类 -->
    <el-card class="category-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">书籍分类</span>
      </div>
      <div class="category-grid">
        <el-card
          v-for="category in categoryLists"
          :key="category.id"
          class="category-item"
          shadow="hover"
          @click.native="handleCategoryClick(category)"
        >
          <div class="category-content">
            <svg-icon class="category-icon" :icon-class="category.remark" />
            <span class="category-name">{{ category.name }}</span>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="never">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchValue"
            placeholder="搜索书名、作者或内容"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </el-col>

        <el-col :span="4">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-refresh"
            @click="handleRefresh"
            :loading="loading"
          >
            搜索
          </el-button>
          <el-button
            size="small"
            icon="el-icon-refresh"
            @click="handleReset"
            :loading="loading"
          >
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 热门书籍 -->
    <el-card class="books-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">
          {{
            selectedCategory ? getCategoryName(selectedCategory) : "热门书籍"
          }}
          <span v-if="total" class="book-count">({{ total }}本)</span>
        </span>
        <div class="view-controls">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">网格</el-radio-button>
            <el-radio-button label="list">列表</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 书籍列表 -->
      <div v-loading="loading" class="books-list">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="grid-view">
          <el-row :gutter="20">
            <el-col
              v-for="book in bookLists"
              :key="book.id"
              :span="6"
              class="book-col"
            >
              <el-card
                class="book-item"
                shadow="hover"
              >
                <div class="book-cover">
                  <img
                    :src="book.newsImg || bookImg"
                    :alt="book.name"

                  />
                </div>
                <div class="book-info">
                  <h4 class="book-title" :title="book.name">{{ book.name }}</h4>
                  <p class="book-description" :title="book.content">
                    {{ book.content }}
                  </p>
                  <div class="book-meta">
                    <span class="book-author">作者：{{ book.author }}</span>
                    <span class="book-category">{{ book.volCatName }}</span>
                  </div>
                  <div class="book-actions">
                    <el-button
                      type="primary"
                      size="small"
                      icon="el-icon-reading"
                      @click.stop="handleBorrow(book)"
                    >
                      借书
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 列表视图 -->
        <div v-else class="list-view">
          <el-table
            :data="bookLists"
            style="width: 100%"
          >
            <el-table-column width="100">
              <template slot-scope="scope">
                <img
                  :src="scope.row.newsImg || bookImg"
                  :alt="scope.row.name"
                  class="table-book-cover"
                  @error="handleImageError"
                />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="书名" min-width="200">
              <template slot-scope="scope">
                <span class="book-name">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="author" label="作者" width="150" />
            <el-table-column prop="volCatName" label="分类" width="120" />
            <el-table-column prop="content" label="简介" min-width="300">
              <template slot-scope="scope">
                <span class="book-summary">{{ scope.row.content }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-reading"
                  @click.stop="handleBorrow(scope.row)"
                >
                  借书
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            @pagination="getBookLists"
          />
        </div>

        <!-- 空状态 -->
        <el-empty
          v-if="!loading && !bookLists.length"
          description="暂无相关书籍"
          :image-size="120"
        />
      </div>
    </el-card>

    <!-- BookDetail 弹窗 -->
    <el-dialog
      :visible.sync="bookDetailVisible"
      title="书籍详情"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="book-detail-dialog"
    >
      <BookDetail
        v-if="bookDetailVisible && selectedBookId"
        :volBookId="selectedBookId"
        @borrow="handleBookDetailBorrow"
      />
    </el-dialog>

    <!-- BookBorrow 弹窗 -->
    <el-dialog
      :visible.sync="bookBorrowVisible"
      title="书籍借阅"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="book-borrow-dialog"
    >
      <BookBorrow
        v-if="bookBorrowVisible && selectedBookId"
        :volBookId="selectedBookId"
        @success="handleBorrowSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import { bookLists } from "@/api/book";
import { getClassifyBookLists } from "../../../api/book";
import BookDetail from "./BookDetail.vue";
import BookBorrow from "./BookBorrow.vue";

export default {
  name: "PcLibrary",
  components: {
    BookDetail,
    BookBorrow,
  },
  data() {
    return {
      // 热门书籍列表
      bookLists: [],
      // 分类列表
      categoryLists: [],
      // 加载状态
      loading: false,
      // 搜索关键词
      searchValue: "",
      // 选中的分类
      selectedCategory: "",
      // 视图模式
      viewMode: "grid",
      // 分页
      currentPage: 1,
      pageSize: 12,
      total: 0,
      // 默认书籍封面 - 使用一个在线占位图片
      bookImg:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDEyMCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTYwIiBmaWxsPSIjRjVGN0ZBIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxNDAiIGZpbGw9IiNFNEU3RUQiLz4KPHN2ZyB4PSI0NSIgeT0iNzAiIHdpZHRoPSIzMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjOTA5Mzk5Ij4KPHA+Ym9vazwvcD4KPC9zdmc+Cjwvc3ZnPgo=",
      // 弹窗控制
      bookDetailVisible: false,
      bookBorrowVisible: false,
      selectedBookId: null,
    };
  },
  created() {
    this.getBookLists();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取书籍列表
    async getBookLists() {
      try {
        this.loading = true;

        // 尝试从API获取数据
        try {
          const res = await bookLists();

          console.log("data", res);
          // 处理图片

          this.bookLists = res.data.books.map((item) => {
            if (item.imgs && item.imgs[0] && item.imgs[0].filePath) {
              item.newsImg = `${process.env.VUE_APP_BASE_API}${item.imgs[0].filePath}`;
            }
            return item;
          });
          this.categoryLists = res.data.categories;
          // this.total = res.total;
        } catch (apiError) {
          console.warn("API调用失败，使用模拟数据:", apiError);

          this.$message.info("使用演示数据");
        }
      } catch (error) {
        console.error("获取书籍列表失败:", error);
        this.$message.error("网络异常，请重试！");
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    handleRefresh() {
      this.getBookLists();
    },

    // 分类点击
    handleCategoryClick(category) {
      this.selectedCategory = category.id;
      this.currentPage = 1;
      this.fetchClassifyBookLists({
        volBookCatid: category.id,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      });
    },
    fetchClassifyBookLists(params) {
      getClassifyBookLists(params).then((res) => {
        console.log("getClassifyBookLists", res);
        this.bookLists = res.rows.map((item) => {
          if (item.imgs && item.imgs[0] && item.imgs[0].filePath) {
            item.newsImg = `${process.env.VUE_APP_BASE_API}${item.imgs[0].filePath}`;
          }
          return item;
        });
        this.total = res.total;
      });
    },


    // 借书 - 打开书籍详情弹窗
    handleBorrow(book) {
      this.selectedBookId = book.id.toString();
      this.bookDetailVisible = true;
    },

    // 图片加载失败处理
    handleImageError(event) {
      event.target.src = this.bookImg;
    },
    handleReset() {
      this.searchValue = "";
      this.selectedCategory = "";
      this.viewMode = "grid";
      this.currentPage = 1;
      this.pageSize = 10;
      this.total = 0;
      this.getBookLists();
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
      this.fetchClassifyBookLists({
        volBookCatid: this.selectedCategory,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        searchValue: this.searchValue,
      });
    },

    // 分类筛选
    handleCategoryFilter() {
      this.currentPage = 1;
    },

    // 排序处理
    handleSort() {
      this.currentPage = 1;
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 获取分类名称
    getCategoryName(categoryId) {
      const category = this.categoryLists.find((cat) => cat.id === categoryId);
      return category ? category.name : "未知分类";
    },

    // 处理BookDetail中的借阅按钮点击
    handleBookDetailBorrow() {
      this.bookDetailVisible = false;
      this.bookBorrowVisible = true;
    },

    // 处理借阅成功
    handleBorrowSuccess() {
      this.bookBorrowVisible = false;
      this.selectedBookId = null;
      this.$message.success("借阅成功！");
      // 可以在这里刷新数据或执行其他操作
    },
  },
};
</script>

<style lang="scss" scoped>
.library-container {
  max-width: 1200px;
  margin: 20px auto 0;
  min-height: calc(100vh - 50px);

  .page-description {
    text-align: center;
    margin-bottom: 30px;
    padding: 10px 0;

    p {
      color: #909399;
      font-size: 16px;
      margin: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;

      .book-count {
        font-size: 14px;
        color: #909399;
        font-weight: normal;
        margin-left: 8px;
      }
    }

    .view-controls {
      display: flex;
      align-items: center;
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .category-card {
    margin-bottom: 30px;

    .category-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;

      .category-item {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .category-content {
          display: flex;
          align-items: center;
          padding: 10px;

          .category-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            color: #409eff;
          }

          .category-name {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }
      }
    }
  }

  .books-card {
    .books-list {
      .grid-view {
        .book-col {
          margin-bottom: 20px;
        }
      }

      .list-view {
        .table-book-cover {
          width: 60px;
          height: 80px;
          object-fit: cover;
          border-radius: 4px;
        }

        .book-name {
          font-weight: 600;
          color: #303133;
          cursor: pointer;

          &:hover {
            color: #409eff;
          }
        }

        .book-summary {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.4;
          color: #606266;
        }
      }

      .pagination-wrapper {
        margin-top: 30px;
        text-align: center;
      }

      .book-item {
        cursor: pointer;
        height: 100%;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .book-cover {
          text-align: center;
          margin-bottom: 15px;

          img {
            width: 120px;
            height: 160px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        .book-info {
          .book-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 10px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .book-description {
            font-size: 14px;
            color: #606266;
            margin: 0 0 15px 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
            height: 42px;
          }

          .book-meta {
            margin-bottom: 15px;

            .book-author,
            .book-category {
              display: block;
              font-size: 12px;
              color: #909399;
              margin-bottom: 5px;
            }
          }

          .book-actions {
            text-align: center;
          }
        }
      }
    }
  }
}

// 弹窗样式
::v-deep .book-detail-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }

  .book-detail-container {
    padding: 0;
    background-color: transparent;
    min-height: auto;
  }

  .book-detail-content-wrapper {
    background: white;
    border-radius: 8px;
  }
}

::v-deep .book-borrow-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }

  .book-borrow-container {
    padding: 0;
    background-color: transparent;
    min-height: auto;
  }

  .borrow-content-wrapper {
    background: white;
    border-radius: 8px;
    padding: 20px;
  }
}

// PageHeader 样式
::v-deep .el-page-header {
  padding: 0;
  margin-bottom: 20px;

  .el-page-header__content {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .el-page-header__left {
    .el-page-header__back {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .library-container {
    padding: 15px;

    .page-description {
      p {
        font-size: 14px;
      }
    }

    .category-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 15px;
    }
  }

  // 移动端 PageHeader 样式
  ::v-deep .el-page-header {
    .el-page-header__content {
      font-size: 20px;
    }
  }

  // 移动端弹窗样式调整
  ::v-deep .book-detail-dialog,
  ::v-deep .book-borrow-dialog {
    .el-dialog {
      width: 95% !important;
      margin-top: 5vh !important;
    }
  }
}
</style>
