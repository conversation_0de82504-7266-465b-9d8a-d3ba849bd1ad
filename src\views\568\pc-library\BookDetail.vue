<template>
  <div class="book-detail-container">
    <el-card class="book-detail-card" shadow="hover">
      <!-- 返回按钮 -->
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-arrow-left"
          @click="$router.back()"
          size="small"
        >
          返回
        </el-button>
      </div>

      <!-- 书籍详情内容 -->
      <div class="book-detail-content">
        <!-- 书籍封面 -->
        <div class="book-cover-section">
          <div class="cover-wrapper">
            <el-image
              v-if="imgSrc"
              class="book-cover"
              :src="imgSrc"
              fit="cover"
              :preview-src-list="[imgSrc]"
            >
              <div slot="placeholder" class="image-slot">
                <i class="el-icon-loading"></i>
              </div>
              <div slot="error" class="image-slot">
                <img :src="bookImg" alt="默认封面" />
              </div>
            </el-image>
            <img v-else :src="bookImg" alt="默认封面" class="book-cover" />
          </div>
        </div>

        <!-- 书籍信息 -->
        <div class="book-info-section">
          <h1 class="book-title">{{ details.name || '书籍标题' }}</h1>

          <div class="book-meta">
            <div class="author-info">
              <span class="label">作者：</span>
              <span class="value">{{ details.author || '未知作者' }}</span>
            </div>
            <div class="category-info">
              <el-tag
                v-if="details.volCatName"
                type="primary"
                size="small"
                class="category-tag"
              >
                {{ details.volCatName }}
              </el-tag>
            </div>
          </div>

          <!-- 书籍内容描述 -->
          <div class="book-content">
            <h3>内容简介</h3>
            <div class="content-text">
              {{ details.content || '暂无内容简介' }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              icon="el-icon-reading"
              @click="handleBorrow"
              :loading="borrowLoading"
            >
              立即借阅
            </el-button>
            <!-- 可以添加其他操作按钮 -->
            <!-- <el-button
              type="success"
              size="large"
              icon="el-icon-truck"
              @click="handleDelivery"
            >
              一键送书
            </el-button> -->
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDetails } from '@/api/book'

export default {
  name: 'PcBookDetail',
  props: {
    volBookId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      // 默认书籍封面
      bookImg: require('@/assets/images/book.png'),
      // 书籍详情
      details: {},
      imgSrc: '',
      borrowLoading: false
    }
  },
  created() {
    this.loadDetails()
  },
  methods: {
    // 获取书籍详情
    async loadDetails() {
      try {
        const data = await getDetails(this.volBookId)



        if (data.code !== 200) {
          this.$message.error('获取书籍详情失败')
          return
        }

        // 处理图片
        if (data.data.imgs && data.data.imgs[0] && data.data.imgs[0].filePath) {
          this.imgSrc = `${process.env.VUE_APP_BASE_API}${data.data.imgs[0].filePath}`
        } else {
          this.imgSrc = this.bookImg
        }

        this.details = data.data
      } catch (error) {
        console.error('获取书籍详情失败:', error)
        this.$message.error('网络异常，请重试！')
        // 可以选择返回上一页或显示错误页面
        setTimeout(() => {
          this.$router.back()
        }, 2000)
      }
    },

    // 借书操作
    handleBorrow() {
      this.borrowLoading = true
      // 跳转到借书页面
      this.$router.push({
        name: 'PcBookBorrow',
        params: { volBookId: this.details.id.toString() },
        query: { type: '0' }
      })
      this.borrowLoading = false
    },

    // 送书操作（如果需要）
    handleDelivery() {
      this.$router.push({
        name: 'PcBookBorrow',
        params: { volBookId: this.details.id.toString() },
        query: { type: '1' }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.book-detail-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  .book-detail-card {
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 12px;

    .header-actions {
      margin-bottom: 20px;
    }

    .book-detail-content {
      display: flex;
      gap: 40px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 20px;
      }

      .book-cover-section {
        flex: 0 0 300px;

        @media (max-width: 768px) {
          flex: none;
          text-align: center;
        }

        .cover-wrapper {
          position: sticky;
          top: 20px;

          .book-cover {
            width: 100%;
            max-width: 300px;
            height: 400px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;

            &:hover {
              transform: translateY(-5px);
            }
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 20px;
          }
        }
      }

      .book-info-section {
        flex: 1;
        min-width: 0;

        .book-title {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 20px 0;
          line-height: 1.4;
        }

        .book-meta {
          margin-bottom: 30px;

          .author-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 16px;

            .label {
              color: #606266;
              margin-right: 8px;
              font-weight: 500;
            }

            .value {
              color: #303133;
            }
          }

          .category-info {
            .category-tag {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }

        .book-content {
          margin-bottom: 40px;

          h3 {
            font-size: 18px;
            color: #303133;
            margin: 0 0 15px 0;
            font-weight: 600;
          }

          .content-text {
            font-size: 14px;
            line-height: 1.8;
            color: #606266;
            text-indent: 2em;
            background: #fafbfc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #409eff;
          }
        }

        .action-buttons {
          display: flex;
          gap: 15px;
          flex-wrap: wrap;

          .el-button {
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 6px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .book-detail-container {
    padding: 10px;
  }
}
</style>
