<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="filters" size="small">
      <el-form-item label="医院名称">
        <el-input v-model="filters.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="filters.status" clearable placeholder="请选择">
          <el-option
            v-for="item in statuss"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属地区">
        <el-cascader
          v-model="filters.area"
          :options="options"
          :props="cateProps"
          clearable
          placeholder="请选择"
        ></el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" icon="el-icon-search" @click="getPageList">
            查询
          </el-button>
          <el-button icon="el-icon-refresh-right" @click="resetParameter">
            重置
          </el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="data"
      stripe
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55"></el-table-column> -->
      <el-table-column label="序号" align="center" type="index" width="50" />
      <el-table-column prop="name" label="医院名称"></el-table-column>
      <el-table-column prop="area" label="所属地区">
        <template slot-scope="scope">
          <!-- areaOptions -->
          {{ deptList.find((item) => item.id === scope.row.county)?.deptName }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="详细地址"></el-table-column>
      <el-table-column prop="person" label="负责人"></el-table-column>
      <el-table-column prop="tel" label="联系方式"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          {{ status[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="申请时间"></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button
            v-if="scope.row.status == 3"
            type="text"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.status == 1"
            type="text"
            @click="handleReview(scope.row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="getPageList"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleDialogClose"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        :disabled="['view', 'review'].includes(dialogType)"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请年份" prop="year">
              <el-input
                v-model="form.year"
                placeholder="请输入申请年份"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入医院名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="person">
              <el-input
                v-model="form.person"
                placeholder="请输入负责人"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="tel">
              <el-input
                v-model="form.tel"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传区域 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="门头照片" prop="doorway">
              <single-image-upload
                v-model="form.doorway"
                :file-name="form.doorwayName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照" prop="business">
              <single-image-upload
                v-model="form.business"
                :file-name="form.businessName"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人身份证正面" prop="idupper">
              <single-image-upload
                v-model="form.idupper"
                :file-name="form.idupperName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人身份证反面" prop="idlower">
              <single-image-upload
                v-model="form.idlower"
                :file-name="form.idlowerName"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资格证书" prop="qualifiCer">
              <single-image-upload
                v-model="form.qualifiCer"
                :file-name="form.qualifiCerName"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一信用代码" prop="unifiedCode">
              <el-input
                v-model="form.unifiedCode"
                placeholder="请输入统一社会信用代码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="所属地区" prop="area">
              <div class="area-select">
                <span>浙江省/金华市/</span>
                <el-select v-model="form.county" placeholder="请选择">
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="form.address"
                placeholder="请输入详细地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="dialogType === 'view' && form.reason">
          <el-col :span="24">
            <el-form-item label="审批意见" prop="reason">
              <el-input
                v-model="form.reason"
                placeholder="请输入审批意见"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button
          v-if="['edit'].includes(dialogType)"
          type="primary"
          @click="handleSubmit"
        >
          确 定
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="danger"
          @click="handleReviewSubmit('no')"
        >
          不通过
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="primary"
          @click="handleReviewSubmit('pass')"
        >
          通 过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { base, doGet, doPost } from '@/api/dog/index'
import SingleImageUpload from '@/components/Upload/SingleImageUpload.vue'
import { saveOrUpdateYear, updateStatusYear } from '@/api/dog/hospital'

export default {
  name: 'HospitalAgent',
  components: {
    SingleImageUpload,
  },
  data() {
    return {
      filters: {
        name: '',
        status: '',
        area: [],
      },
      statuss: [
        { label: '待审核', value: '1' },
        { label: '已通过', value: '2' },
        { label: '已驳回', value: '3' },
      ],
      options: [],
      cateProps: {
        value: 'code',
        label: 'name',
        children: 'children',
      },
      loading: false,
      data: [],
      selectedRows: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '',
      form: {
        year: '',
        name: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
      },
      rules: {
        year: [{ required: true, message: '请输入申请年份', trigger: 'blur' }],
        name: [{ required: true, message: '请输入医院名称', trigger: 'blur' }],
        person: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        doorway: [
          { required: true, message: '请上传门头照片', trigger: 'change' },
        ],
        business: [
          { required: true, message: '请上传营业执照', trigger: 'change' },
        ],
        idupper: [
          { required: true, message: '请上传身份证正面', trigger: 'change' },
        ],
        idlower: [
          { required: true, message: '请上传身份证反面', trigger: 'change' },
        ],
        qualifiCer: [
          { required: true, message: '请上传资格证书', trigger: 'change' },
        ],
        unifiedCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur',
          },
          {
            pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
            message: '请输入正确的统一社会信用代码',
            trigger: 'blur',
          },
        ],
        area: [
          { required: true, message: '请选择所属地区', trigger: 'change' },
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
        ],
      },
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      areaOptions: [
        { label: '婺城区', value: '330702' },
        { label: '金东区', value: '330703' },
        { label: '武义县', value: '330723' },
        { label: '浦江县', value: '330726' },
        { label: '磐安县', value: '330727' },
        { label: '兰溪市', value: '330781' },
        { label: '义乌市', value: '330782' },
        { label: '东阳市', value: '330783' },
        { label: '永康市', value: '330784' },
      ],
      // 弹窗类型 1:新增 2:编辑 3:查看 4:审核
      dialogType: '',
      // 状态 1:审核中，2:已通过，3:未通过
      status: {
        1: '审核中',
        2: '已通过',
        3: '未通过',
      },
      deptList: [],
    }
  },
  computed: {
    user() {
      return JSON.parse(sessionStorage.getItem('user')) || {}
    },
  },
  created() {
    this.getPageList()
    this.getdeptList()
  },
  methods: {
    getdeptList() {
      doPost('/dept/getAllList').then((res) => {
        console.log(res)
        this.deptList = res.filter((item) => item.level === 2)
      })
    },
    async getPageList() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          type: 0,
          ...this.filters,
        }
        const res = await doGet('/hospital/year/getPageList', params)
        this.data = res.list
        this.total = res.total
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },

    // 批量处理文件
    handleFileList(resource, list) {
      list.forEach((item) => {
        const modelType = item.modelType
        resource[modelType] = item.fileUrl
        resource[modelType + 'Name'] = item.fileName
      })
    },
    resetParameter() {
      this.filters = {
        name: '',
        status: '',
        area: [],
      }
      this.getPageList()
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    // 编辑
    handleEdit(row) {
      return this.getHospitalDetail(row, 'edit')
    },
    // 查看
    handleView(row) {
      return this.getHospitalDetail(row, 'view')
    },
    // 审核
    handleReview(row) {
      return this.getHospitalDetail(row, 'review')
    },
    // 获取医院详情并显示弹窗
    async getHospitalDetail(row, type) {
      try {
        const res = await doGet('/hospital/year/getById', { id: row.id })
        const dialogTitles = {
          edit: '编辑年审',
          view: '查看年审',
          review: '审核年审',
        }

        this.dialogTitle = dialogTitles[type]
        this.dialogType = type
        this.dialogVisible = true
        this.form = { ...res }

        // 查看和审核需要处理文件列表
        this.handleFileList(this.form, res.uploadFileList)
      } catch (error) {
        console.error('获取医院详情失败:', error)
        this.$message.error('获取医院详情失败')
      }
    },
    handleDoorwaySuccess(res) {
      this.form.doorway = res.data.url
    },
    handleBusinessSuccess(res) {
      this.form.business = res.data.url
    },
    handleIdupperSuccess(res) {
      this.form.idupper = res.data.url
    },
    handleidlowerSuccess(res) {
      this.form.idlower = res.data.url
    },
    handlequalifiCerSuccess(res) {
      this.form.qualifiCer = res.data.url
    },
    handleDialogClose() {
      this.$refs.form?.resetFields()
      this.dialogVisible = false
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        this.form.uploadFileStr = this.genUploadFileStr()

        await saveOrUpdateYear(this.form)

        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getPageList()
      } catch (error) {
        console.error(error)
      }
    },
    async handleReviewSubmit(type) {
      if (type === 'pass') {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatusYear({
            lawStatus: 2,
            account: this.form.account,
            status: 2,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批通过')
            this.dialogVisible = false
            this.getPageList()
          })
        })
      } else {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatusYear({
            lawStatus: 3,
            account: this.form.account,
            status: 3,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批不通过')
            this.dialogVisible = false
            this.getPageList()
          })
        })
      }
    },
    // 生成上传文件字符串
    genUploadFileStr() {
      const imgFields = [
        'doorway',
        'business',
        'idupper',
        'idlower',
        'qualifiCer',
      ]
      const fileConfigs = []
      imgFields.forEach((field) => {
        if (this.form[field]) {
          fileConfigs.push({
            fileUrl: this.form[field],
            fileName: this.form[field + 'Name'],
            modelType: field,
          })
        }
      })

      if (fileConfigs.length > 0) {
        return JSON.stringify(fileConfigs)
      }
      return ''
    },
  },
}
</script>

<style scoped>
.toolbar {
  margin-bottom: 20px;
}

.area-select {
  display: flex;
  align-items: center;
}

.area-select span {
  margin-right: 10px;
}

.upload-demo {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
}
</style>
