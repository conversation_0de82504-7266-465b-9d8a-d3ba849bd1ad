<template>
  <div class="single-image-upload">
    <el-upload
      v-if="!isDisabled"
      :headers="headers"
      class="avatar-uploader"
      accept="image/jpeg,image/png"
      :show-file-list="false"
      :action="uploadUrl"
      :on-success="handleSuccess"
      name="multipartFile"
      :before-upload="beforeAvatarUpload"
    >
      <div class="image-container">
        <el-image
          v-if="imageUrl"
          :src="imageUrl"
          fit="contain"
          class="avatar"
        />
        <div v-if="imageUrl" class="delete-button" @click.stop="handleDelete">
          <i class="el-icon-delete"></i>
        </div>
        <template v-else>
          <slot>
            <i class="el-icon-plus avatar-uploader-icon"></i>
          </slot>
        </template>
      </div>
    </el-upload>
    <el-image
      v-if="isDisabled && imageUrl"
      :src="imageUrl"
      :preview-src-list="[imageUrl]"
      fit="cover"
      class="avatar"
    />
    <div v-if="isDisabled && !imageUrl">暂无</div>

    <div v-show="photoVisible" class="showPhoto" @click="closeClick">
      <img class="img" :src="bigImgUrl" alt="图片加载失败" />
    </div>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getDownLoadUrl } from '@/api/dog/index'

export default {
  name: 'SingleImageUpload',

  inject: {
    elForm: {
      default: '',
    },
  },

  props: {
    value: {
      type: String,
      default: '',
    },
    fileName: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      headers: { Authorization: 'Bearer ' + getToken() },
      uploadUrl: process.env.VUE_APP_BASE_API + '/sysUploadFile/uploadFile',
      photoVisible: false,
      bigImgUrl: '',
      imageUrl: '',
      currentFileName: '',
      filePath: '',
    }
  },

  computed: {
    isDisabled() {
      return this.disabled || (this.elForm || {}).disabled
    },
  },

  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          console.log('val', val)
          this.imageUrl = getDownLoadUrl(val)
          this.filePath = val
        } else {
          this.imageUrl = null
          this.filePath = null
        }
      },
    },
    fileName: {
      immediate: true,
      handler(val) {
        if (val) {
          this.currentFileName = val
        }
      },
    },
  },
  created() {
    console.log('this.disabled', this.disabled)
  },

  methods: {
    handleSuccess(res, file) {
      this.currentFileName = file.name
      this.filePath = file.response.data
      this.imageUrl = getDownLoadUrl(file.response.data)

      this.$emit('input', this.filePath)
      this.$emit('update:fileName', this.currentFileName)
      this.$emit('on-success', {
        fileName: this.currentFileName,
        filePath: this.filePath,
        url: this.imageUrl,
      })
    },

    handleDelete() {
      this.$confirm('确定要删除这张图片吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.imageUrl = null
          this.filePath = null
          this.currentFileName = ''

          this.$emit('input', '')
          this.$emit('update:fileName', '')
          this.$emit('on-delete')

          this.$message({
            type: 'success',
            message: '删除成功!',
          })
        })
        .catch(() => {
          // 取消删除操作
        })
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是JPG格式或PNG格式!')
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!')
      }
      return (isJPG || isPNG) && isLt10M
    },

    showBigImage(e) {
      if (e !== '' && this.disabled) {
        this.photoVisible = true
        this.bigImgUrl = e.currentTarget.src
      }
    },

    closeClick() {
      this.photoVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.single-image-upload {
  position: relative;

  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 100%;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 100%;

    &:hover {
      .delete-button {
        opacity: 1;
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }

  .avatar {
    width: 100%;
    height: 300px;
    display: block;
  }

  .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    text-align: center;
    color: #fff;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.8);
    }

    i {
      font-size: 18px;
    }
  }

  .showPhoto {
    position: fixed;
    top: 10%;
    width: 80%;
    height: 80%;
    z-index: 99999;
    display: flex;

    .img {
      display: block;
      margin: auto 0;
      max-width: 100%;
      max-height: 100%;
      text-align: center;
    }
  }
}
</style>
