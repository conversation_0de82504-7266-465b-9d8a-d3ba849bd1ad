# PowerShell 构建和打包脚本
param(
    [string]$OutputPath = "dist.zip"
)

Write-Host "开始构建项目..." -ForegroundColor Green

# 运行构建命令
try {
    & npm run build:prod
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    Write-Host "构建成功！" -ForegroundColor Green
}
catch {
    Write-Host "构建失败: $_" -ForegroundColor Red
    exit 1
}

# 检查 dist 目录是否存在
if (-not (Test-Path "dist")) {
    Write-Host "错误: dist 目录不存在" -ForegroundColor Red
    exit 1
}

Write-Host "开始创建压缩包..." -ForegroundColor Green

# 删除已存在的压缩包
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Force
}

# 创建压缩包
try {
    Compress-Archive -Path "dist\*" -DestinationPath $OutputPath -CompressionLevel Optimal
    Write-Host "压缩包创建成功: $OutputPath" -ForegroundColor Green

    # 显示文件大小
    $fileSize = (Get-Item $OutputPath).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    Write-Host "压缩包大小: $fileSizeMB MB" -ForegroundColor Cyan
}
catch {
    Write-Host "压缩包创建失败: $_" -ForegroundColor Red
    exit 1
}

Write-Host "构建和打包完成！" -ForegroundColor Green
