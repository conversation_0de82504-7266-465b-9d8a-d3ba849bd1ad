const legacy568 = {
  namespaced: true,
  state: {
    // 如果上面从本地获取的lifeData对象下有对应的属性，就赋值给state中对应的变量
    // 加上vuex_前缀，是防止变量名冲突，也让人一目了然
    vuex_user_id: "",
    vuex_user_vol_id: "",
    vuex_user_id_card: "",
    vuex_user_phone: "",
    vuex_user_realName: "",
    vuex_user_headpicture: "",
    vuex_user_mobile: "",
    vuex_ticket: "",
    // 角色: 0-游客 1-志愿者 2-管理员
    vuex_user_role: 0,
    // 页面缓存列表
    vuex_cachedViews: ["LayoutIndex"],
    vuex_token: "",
    vuex_uiStyle: "normal",
    vuex_link: "http://10.45.13.116/zqWeb/#/evaluate",
    vuex_zwlog: null,
  },
  mutations: {
    $uStore(state, payload) {
      // 判断是否多层级调用，state中为对象存在的情况，诸如user.info.score = 1
      let nameArr = payload.name.split(".");
      let len = nameArr.length;
      if (nameArr.length >= 2) {
        let obj = state[nameArr[0]];
        for (let i = 1; i < len - 1; i++) {
          obj = obj[nameArr[i]];
        }
        obj[nameArr[len - 1]] = payload.value;
      } else {
        // 单层级变量，在state就是一个普通变量的情况
        state[payload.name] = payload.value;
      }
    },
  },
  actions: {
    // 可以在这里添加异步操作
  },
  getters: {
    // 可以在这里添加计算属性
  },
};

export default legacy568;
