<template>
  <div class="my-tasks-page">
    <el-card class="page-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="page-title">我的任务</span>
        <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <div class="page-content">
        <div class="module-description">
          <el-alert
            title="功能说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>在这里您可以查看和管理您的志愿服务任务：</p>
            <ul>
              <li>查看已分配的志愿服务任务</li>
              <li>跟踪任务执行进度</li>
              <li>提交任务完成报告</li>
              <li>查看任务评价和反馈</li>
            </ul>
          </el-alert>
        </div>

        <div class="task-stats">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">待执行</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.inProgress }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="task-list">
          <el-table :data="taskList" style="width: 100%" v-loading="loading">
            <el-table-column prop="taskName" label="任务名称" min-width="200" />
            <el-table-column prop="taskType" label="任务类型" width="120" />
            <el-table-column prop="startTime" label="开始时间" width="180" />
            <el-table-column prop="endTime" label="结束时间" width="180" />
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewTask(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MyTasks',
  data() {
    return {
      loading: false,
      stats: {
        total: 12,
        pending: 3,
        inProgress: 2,
        completed: 7
      },
      taskList: [
        {
          id: 1,
          taskName: '社区环境清洁志愿服务',
          taskType: '环境保护',
          startTime: '2024-01-15 09:00',
          endTime: '2024-01-15 12:00',
          status: 'completed'
        },
        {
          id: 2,
          taskName: '老年人关爱服务',
          taskType: '助老服务',
          startTime: '2024-01-20 14:00',
          endTime: '2024-01-20 17:00',
          status: 'inProgress'
        },
        {
          id: 3,
          taskName: '交通文明引导',
          taskType: '文明引导',
          startTime: '2024-01-25 08:00',
          endTime: '2024-01-25 11:00',
          status: 'pending'
        }
      ]
    }
  },
  methods: {
    refreshData() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('数据刷新成功')
      }, 1000)
    },
    getStatusType(status) {
      const typeMap = {
        pending: 'warning',
        inProgress: 'primary',
        completed: 'success'
      }
      return typeMap[status] || 'info'
    },
    getStatusText(status) {
      const textMap = {
        pending: '待执行',
        inProgress: '进行中',
        completed: '已完成'
      }
      return textMap[status] || '未知'
    },
    viewTask(task) {
      this.$message.info(`查看任务：${task.taskName}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.my-tasks-page {
  padding: 20px;

  .page-card {
    border-radius: 12px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .page-content {
      .module-description {
        margin-bottom: 24px;

        .el-alert {
          border-radius: 8px;

          ul {
            margin: 8px 0 0 0;
            padding-left: 20px;

            li {
              margin-bottom: 4px;
              color: #606266;
            }
          }
        }
      }

      .task-stats {
        margin-bottom: 24px;

        .stat-card {
          text-align: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 8px;
          color: white;

          .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
          }

          .stat-label {
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .task-list {
        .el-table {
          border-radius: 8px;
          overflow: hidden;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .task-stats {
      .el-col {
        margin-bottom: 12px;
      }
    }
  }
}
</style>
