<template>
  <div class="mine-page">
    <div class="mine-container">
      <!-- 头部用户信息卡片 -->
      <el-card class="user-info-card" shadow="hover">
        <div class="user-header">
          <div class="user-avatar-section">
            <el-avatar
              :size="80"
              :src="photo"
              class="user-avatar"
              @click.native="
                $router.push({ name: 'Profile', query: { photo } })
              "
            >
              <img src="@/assets/images/avatar.png" />
            </el-avatar>
            <div class="user-info">
              <h3 class="user-name">{{ vuex_user_realName | name }}</h3>
              <p class="user-slogan">服务他人，提升自己！</p>
            </div>
          </div>
          <el-button
            type="text"
            icon="el-icon-refresh"
            :loading="isLoading"
            @click="onAllRefresh"
            class="refresh-btn"
          >
            刷新
          </el-button>
        </div>

        <!-- 统计数据 -->
        <div class="stats-section">
          <div class="stat-item">
            <div class="stat-number">{{ hour }}</div>
            <div class="stat-label">服务时长（时）</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-number">{{ score }}</div>
            <div class="stat-label">积分明细（分）</div>
          </div>
        </div>
      </el-card>

      <!-- 功能菜单 -->
      <el-card class="menu-card" shadow="hover">
        <div slot="header" class="card-header">
          <span class="card-title">我的服务</span>
        </div>
        <div class="menu-grid">
          <div class="menu-item" @click="handleOpenPage('/568/my-tasks')">
            <div class="menu-icon">
              <img :src="mineDoneIcon.love" alt="我的任务" />
            </div>
            <div class="menu-content">
              <div class="menu-title">我的任务</div>
              <div class="menu-desc">查看我的志愿服务任务</div>
            </div>
            <i class="el-icon-arrow-right menu-arrow"></i>
          </div>

          <div class="menu-item" @click="handleOpenPage('/568/my-evaluations')">
            <div class="menu-icon">
              <img :src="mineDoneIcon.zan" alt="我的评价" />
            </div>
            <div class="menu-content">
              <div class="menu-title">我的评价</div>
              <div class="menu-desc">查看服务评价记录</div>
            </div>
            <i class="el-icon-arrow-right menu-arrow"></i>
          </div>

          <div class="menu-item" @click="handleOpenPage('/568/repair-sign')">
            <div class="menu-icon">
              <img :src="mineDoneIcon.repair" alt="补卡申请" />
            </div>
            <div class="menu-content">
              <div class="menu-title">补卡申请</div>
              <div class="menu-desc">申请补签服务记录</div>
            </div>
            <i class="el-icon-arrow-right menu-arrow"></i>
          </div>

          <div class="menu-item" @click="handleOpenPage('/568/review-apply')">
            <div class="menu-icon">
              <img :src="mineDoneIcon.apply" alt="审批事项" />
            </div>
            <div class="menu-content">
              <div class="menu-title">审批事项</div>
              <div class="menu-desc">处理待审批事项</div>
            </div>
            <i class="el-icon-arrow-right menu-arrow"></i>
          </div>

          <div class="menu-item" @click="handleOpenPage('/568/score-gifts')">
            <div class="menu-icon">
              <img :src="mineDoneIcon.gift" alt="积分好礼" />
            </div>
            <div class="menu-content">
              <div class="menu-title">积分好礼</div>
              <div class="menu-desc">兑换积分奖励</div>
            </div>
            <i class="el-icon-arrow-right menu-arrow"></i>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { hourScore } from "@/api/568/mine";
import { downloadFile } from "@/api/568/index";

export default {
  name: "Mine",
  components: {},

  props: {},
  data() {
    return {
      isLoading: false,
      // 总积分
      score: 0,
      // 总时长
      hour: 0,
      photo: "",
      headpicture: require("@/assets/images/avatar.png"),
      mineDoneIcon: {
        love: require("@/assets/images/love.png"),
        zan: require("@/assets/images/zan.png"),
        // 'violation': require('@/assets/images/violation.png'),
        gift: require("@/assets/images/gift.png"),
        repair: require("@/assets/images/repair.png"),
        apply: require("@/assets/images/apply.png"),
      },
    };
  },
  computed: {},
  watch: {},
  created() {
    // this.photo = this.vuex_user_headpicture ? this.vuex_user_headpicture : this.headpicture
    // this.downloadFiles()

    this.getHourScore();
    this.$bus.$off("reLoadmine");
    this.$bus.$on("reLoadmine", () => {
      this.onAllRefresh();
      // this.downloadFiles()
    });
  },
  methods: {
    handleOpenPage(url) {
      if (this.vuex_user_role == 0) {
        this.$confirm(
          "成为志愿者后查看，是否申请成为志愿者？",
          "申请成为志愿者",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.$router.push("/clause");
          })
          .catch(() => {
            // 用户取消操作
          });
      } else {
        this.$router.push(url);
      }
    },
    // 获取头像图片
    async downloadFiles() {
      const data = await downloadFile({
        businessId: this.vuex_user_vol_id,
        tableName: "vol_user",
      }).catch(() => {
        this.$message.error("网络异常，请重试！");
      });

      if (!data || data.code != 200) return;

      const fileList = data.rows.map((item) => {
        return {
          url: `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`,
        };
      });
      // console.log(fileList)
      this.photo = fileList[fileList.length - 1].url;
    },
    async getHourScore() {
      this.isLoading = true;
      const data = await hourScore().catch(() => {
        this.isLoading = false;
        this.$message.error("网络异常，请重试！");
      });

      if (data.code != 200) {
        this.isLoading = false;
        return;
      }

      this.score = data.data.score;
      this.hour = data.data.hour;

      let reg = /^\/.*/;
      this.photo =
        data.data.user.profile && reg.test(data.data.user.profile)
          ? `${process.env.VUE_APP_BASE_API}${data.data.user.profile}`
          : data.data.user.headpicture
          ? data.data.user.headpicture
          : this.headpicture;

      // 关闭加载状态
      if (this.isLoading) {
        this.isLoading = false;
        this.$message.success("刷新成功");
      }
    },
    // 刷新数据
    onAllRefresh() {
      this.getHourScore();
    },
  },
};
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  padding: 20px;

  .mine-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .user-info-card {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;

    .user-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 30px;

      .user-avatar-section {
        display: flex;
        align-items: center;
        gap: 20px;

        .user-avatar {
          cursor: pointer;
          transition: transform 0.3s ease;
          border: 3px solid #fff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

          &:hover {
            transform: scale(1.05);
          }
        }

        .user-info {
          .user-name {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
          }

          .user-slogan {
            margin: 0;
            font-size: 14px;
            color: #7f8c8d;
          }
        }
      }

      .refresh-btn {
        color: #1890ff;
        font-size: 14px;

        &:hover {
          color: #40a9ff;
        }
      }
    }

    .stats-section {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      padding: 24px;
      margin: 0 -20px -20px -20px;

      .stat-item {
        flex: 1;
        text-align: center;
        color: #fff;

        .stat-number {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stat-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }

      .stat-divider {
        width: 1px;
        height: 40px;
        background: rgba(255, 255, 255, 0.3);
        margin: 0 20px;
      }
    }
  }

  .menu-card {
    border-radius: 12px;

    .card-header {
      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }

    .menu-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 16px;

      .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        background: #fff;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .menu-icon {
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 12px;
          margin-bottom: 12px;

          img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
          }
        }

        .menu-content {
          text-align: center;

          .menu-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
          }

          .menu-desc {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
          }
        }

        .menu-arrow {
          display: none;
        }
      }
    }
  }

  // 响应式设计 - 平板端
  @media (max-width: 1024px) and (min-width: 769px) {
    .menu-card {
      .menu-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 14px;

        .menu-item {
          padding: 18px 14px;

          .menu-icon {
            width: 44px;
            height: 44px;
            margin-bottom: 10px;

            img {
              width: 22px;
              height: 22px;
            }
          }

          .menu-content {
            .menu-title {
              font-size: 13px;
            }

            .menu-desc {
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  // 响应式设计 - 移动端
  @media (max-width: 768px) {
    padding: 16px;

    .user-info-card {
      .user-header {
        .user-avatar-section {
          gap: 16px;

          .user-avatar {
            width: 60px;
            height: 60px;
          }

          .user-info {
            .user-name {
              font-size: 20px;
            }
          }
        }
      }

      .stats-section {
        .stat-item {
          .stat-number {
            font-size: 24px;
          }
        }
      }
    }

    .menu-card {
      .menu-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;

        .menu-item {
          padding: 16px 12px;

          .menu-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 8px;

            img {
              width: 20px;
              height: 20px;
            }
          }

          .menu-content {
            .menu-title {
              font-size: 13px;
            }

            .menu-desc {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}
</style>
