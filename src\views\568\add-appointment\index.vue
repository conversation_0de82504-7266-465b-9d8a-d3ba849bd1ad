<template>
  <div class="add_appointment_container">
    <!-- 预约信息 -->
    <van-form @submit="onSubmit">
      <!-- 车次信息 -->
      <van-field
        v-model="user.trainNumber"
        class="cell"
        name="乘车班次"
        label="乘车班次："
        placeholder="请填写您的乘车班次"
        :rules="[{ required: true ,trigger:'onChange'}]"
      />

      <van-field
        v-model="user.trainTime"
        class="cell"
        name="发车时间"
        label="发车时间："
        placeholder="请选择发车时间"
        readonly
        :rules="[{ required: true ,trigger:'onChange'}]"
        @click="handleOpenTime"
      />

      <!-- 预约人信息 -->
      <van-field
        v-model="user.name"
        class="cell m-t-10"
        name="姓名"
        label="您的姓名："
        placeholder="请输入姓名"
        :rules="[{ required: true }]"
      />
      <van-field
        v-model="user.phone"
        class="cell"
        name="手机号码"
        label="手机号码："
        placeholder="请填写手机号码"
        :rules="[{ required: true, trigger:'onChange', validator: phoneValidator, message: '手机号码不正确' }]"
      />
      <van-field
        v-model="user.diseaseTypeName"
        class="cell"
        name="残病类型"
        label="残病类型："
        placeholder="请输入残病类型"
        :rules="[{ required: true ,trigger:'onChange'}]"
      />
      <!-- right-icon="arrow-down" @click="diseaseTypeShow = true" -->
      <van-field name="types" label="服务需求：">
        <template #input>
          <span v-if="!typeColums.length">加载中...</span>
          <van-checkbox-group v-else v-model="user.types">
            <van-checkbox v-for="(item, index) in typeColums" :key="index" :name="item.value" shape="square" class="m-b-10">{{ item.text }}</van-checkbox>
          </van-checkbox-group>
        </template>
      </van-field>

      <div v-if="user.types.includes('1')" class="m-t-10">
        <van-field name="types" label="相关人员：">
          <template #input>
            <van-checkbox v-model="user.isEmergency">是否有陪同人员或应急联系人</van-checkbox>
          </template>
        </van-field>
        <van-field
          v-if="user.isEmergency"
          v-model="user.emergencyName"
          class="cell m-t-10"
          name="人员姓名"
          label="人员姓名："
          placeholder="请输入陪同人员或应急联系人姓名"
          :rules="[{ required: true }]"
        />
        <van-field
          v-if="user.isEmergency"
          v-model="user.emergencyPhone"
          class="cell"
          name="手机号码"
          label="手机号码："
          placeholder="请填写手机号码"
          :rules="[{ required: true, trigger:'onChange', pattern: phonePattern, message: '手机号码不正确' }]"
        />
        <van-field
          v-model="user.address"
          class="cell"
          name="上门地址"
          label="上门地址："
          placeholder="请填写上门地址"
          :rules="[{ required: true }]"
        />
      </div>

      <van-field
        v-model="user.description"
        class="message"
        rows="3"
        autosize
        label="需求描述："
        type="textarea"
        maxlength="300"
        placeholder="请输入您的具体服务需求详情"
        show-word-limit
        :rules="[{ required: true}]"
      />

      <div class="submit">
        <van-button round block type="info" native-type="submit">提交预约</van-button>
      </div>
    </van-form>
    <!-- /预约信息 -->
    <p class="tip-msg">
      <van-checkbox v-model="isAgree" shape="square">您的预约应符合<span class="highlight" @click="$router.push({ name: 'AppointmentInstructions' })">《568助老助残直通车服务说明》</span> 的相关要求，点击提交预约即表示您已同意相关要求</van-checkbox>
    </p>
    <!-- 提示 -->
    <!-- <div class="msg">
      <h4>注意事项：</h4>
      <p>1.至少提前一天预约。</p>
      <p>2.个人基本信息、身体情况、乘车信息、服务需求等。</p>
      <p>3.如需上门接送，还需提供详细地址。</p>
      <p>4.如果没有随行陪同人员必须提供应急联系人电话。</p>
      <p>5.如有变化需提前取消预约的服务。</p>
      <p>6.超过约定时间半小时后预约人没有出现，服务自动取消，次数过多，以后将不接受预约申请。</p>
    </div> -->
    <!-- 发车时间弹窗 -->
    <van-popup v-model="trainTimeShow" :safe-area-inset-bottom="true" round position="bottom" :style="{ height: 'calc(1.17333rem + 264px)' }">
      <van-datetime-picker
        v-model="currenttrainTimeDate"
        type="datetime"
        title="选择发车时间"
        @cancel="trainTimeShow=false"
        @confirm="trainTimeConfirm"
      />
    </van-popup>
    <!-- 残病类型 -->
    <van-popup v-model="diseaseTypeShow" :safe-area-inset-bottom="true" round position="bottom" :style="{ height: '50%' }">
      <van-picker
        title="残病类型"
        show-toolbar
        :columns="diseaseTypeColumns"
        @confirm="diseaseTypeConfirm"
        @cancel="diseaseTypeShow = false"
      />
    </van-popup>
  </div>
</template>

<script>
import {addAppointment} from '@/api/home'
import dayjs from 'dayjs'
// import {pvMixin} from '@/util/pvMixin'

export default {
  name: 'AddAppointment',
  components: {

  },
  // mixins: [pvMixin],
  props: {

  },
  data() {
    return {
      user: {
        name: '',
        phone: '',
        description: '',
        types: [],
        isEmergency: false
      },
      phonePattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
      trainTimeShow: false,
      currenttrainTimeDate: new Date(),
      diseaseTypeShow: false,
      diseaseTypeColumns: [],
      typeColums: [],
      isAgree: false
    }
  },
  computed: {},
  watch: {
  },
  async created() {
    const name = this.aseName(this.vuex_user_realName)
    const phone = this.asePhone(this.vuex_user_mobile)
    this.user = {...this.user, name, phone }
    // 残病字典
    /* await this.$getCase('vol_disease_type').then(res => {
      this.diseaseTypeColumns = res.data.map(item => {
        return {
          text: item.dictLabel,
          value: item.dictValue
        }
      })
    }) */

    // 服务类型
    await this.$getCase('vol_detail_type').then(res => {
      this.typeColums = res.data.map(item => {
        return {
          text: item.dictLabel,
          value: item.dictValue
        }
      })
    })
  },
  mounted() {
    // 埋点处理，计算页面加载完成时间
    // this.pv_getCalcTime('pv_time_t2')
  },
  methods: {
    /* 预约人手机号 */
    phoneValidator(value) {
      /* 如果和原本手机号相同直接过，否则进入手机号正则判断 */
      const ph = this.asePhone(this.vuex_user_mobile)
      if (ph == value.trim()) return true
      else return this.phonePattern.test(value)
    },
    /* 伤残类型确认选择 */
    diseaseTypeConfirm(item) {
      this.user.diseaseTypeName = item.text
      this.user.diseaseType = item.value
      this.diseaseTypeShow = false
    },
    /* 打开发车时间 */
    handleOpenTime() {
      if (this.user.trainTime) {
        this.currenttrainTimeDate = ''
      }
      this.trainTimeShow = true
    },
    /* 确认选择发车时间 */
    trainTimeConfirm() {
      this.user.trainTime = dayjs(this.currenttrainTimeDate).format('YYYY-MM-DD HH:mm:ss')
      this.trainTimeShow = false
    },
    async onSubmit() {
      if (!this.isAgree) {
        this.$dialog.alert({
          title: '提示',
          message: '请先阅读《568助老助残直通车服务说明》后再提交'
        })
        return
      }
      const name = this.aseName(this.vuex_user_realName)
      const phone = this.asePhone(this.vuex_user_mobile)
      const params = { ...this.user, isEmergency: Number(this.user.isEmergency) }
      if (this.user.name.trim() == name) params.name = this.vuex_user_realName
      if (this.user.phone.trim() == phone) params.phone = this.vuex_user_mobile
      if (this.user.emergencyName) params.emergencyName = this.encrypt(this.user.emergencyName)
      if (this.user.emergencyPhone) params.emergencyPhone = this.encrypt(this.user.emergencyPhone)
      if (this.user.types.length) {
        params.types = this.user.types.join(',')
      } else {
        this.$dialog.alert({
          title: '提示',
          message: '请选择至少一项服务需求'
        })
        return
      }
      console.log(params)
      this.$dialog.confirm({
        title: '提示',
        message: '是否确认提交申请？'
      }).then(() => {
        this.$toast.loading({ message: '加载中...', duration: 0, forbidClick: true })
        addAppointment(params).then(() => {
          this.$toast.clear()
          this.$dialog.alert({
            title: '提示',
            message: '已提交审核 (提交申请后可在“我的-审批事项”中查看进度)'
          }).then(() => {
            this.$router.back()
          })
        }).catch(() => {
          this.$toast.fail({
            message: '网络异常，请重试！'
          })
        })
      })
      /* const data = await addAppointment(params).catch(() => {
        this.$toast.fail({
          message: '网络异常，请重试！'
        })
      })
      if (data.code != 200) return

      this.user.type = ''
      this.user.description = ''
      this.$toast.success('预约成功！')

      this.$router.back()
      this.$bus.$emit('reLoad') */
    }
  }
}
</script>

<style lang="scss" scoped>
.add_appointment_container {
  background: #f6f6f8;
  min-height: 100vh;
  padding-bottom: 57px;
  .m-t-10 {
    margin-top: 11px;
  }
  .m-b-10 {
    margin-bottom: 11px;
  }
  .van-form {
    .van-cell {
      padding: 12px 17px 12px 15px;
    }
    .message {
      height: 148px;
      display: flex;
      flex-direction: column;
      margin-top: 11px;
      padding: 8px 15px 7px;
      ::v-deep.van-field__error-message {
        position: absolute;
        top: 22px;
        font-size: 15px;
      }
      ::v-deep .van-field__word-limit {
        margin-top: 16px;
        color: #9b9b9b;
      }
    }
    ::v-deep .van-field__label {
      color: #9b9b9b;
      font-size: 15px;
    }
    ::v-deep .van-field__control {
      font-size: 15px;
      color: #333;
    }
    .submit {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #fff;
      height: 57px;
      padding: 7px 15px;
      box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.08);
      .van-button {
        height: 43px;
        font-size: 16px;
      }
    }
  }
  .tip-msg {
    font-size: 12px;
    padding: 10px 15px;
    .highlight {
      color: #1989fa;
    }
  }
  .msg {
    border: 1PX dashed #e0e0e0;
    margin: 15px;
    padding: 15px;
    border-radius: 5px;
    font-size: 14px;
    color: #818086;
    p {
      margin-top: 10px;
      line-height: 20px;
    }
  }
}
</style>

