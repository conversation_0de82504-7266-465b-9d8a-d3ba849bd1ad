<!--我要爆料-->
<template>
  <div class="problem-report">
    <!-- 页面头部 -->
    <div class="page-header-wrapper">
      <el-page-header
        @back="goBack"
        content="我要爆料"
        class="page-header-main"
      >
      </el-page-header>
      <div class="page-header-extra">
        <el-button type="primary" @click="historyPage">上报记录</el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-form
        ref="reportForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="report-form"
      >
        <!-- 问题分类选择 -->
        <el-form-item label="问题分类">
          <el-select
            v-model="type2id"
            placeholder="请选择问题分类"
            style="width: 100%"
          >
            <el-option
              v-for="item in columns.type2Options"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 位置选择 -->
        <el-form-item label="位置选择" prop="address">
          <div class="location-wrapper">
            <el-button type="primary" plain @click="showMapDialog = true">
              {{ formData.address ? "已定位" : "选择定位" }}
            </el-button>
            <span class="location-tip">点击按钮选择位置</span>
          </div>
        </el-form-item>

        <!-- 位置描述 -->
        <el-form-item label="位置描述" prop="address">
          <el-input
            type="textarea"
            v-model="formData.address"
            placeholder="请输入位置描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-form-item>

        <!-- 问题描述 -->
        <el-form-item label="问题描述" prop="eventdesc">
          <div class="description-wrapper">
            <el-input
              type="textarea"
              v-model="formData.eventdesc"
              placeholder="请输入问题描述"
              :rows="4"
              maxlength="500"
              show-word-limit
            >
            </el-input>
            <div class="idiom-selector">
              <el-button type="text" @click="showIdiomDialog = true"
                >选择惯用语</el-button
              >
            </div>
          </div>
        </el-form-item>

        <!-- 图片上传 -->
        <el-form-item label="现场照片" prop="fileStr">
          <div class="upload-wrapper">
            <el-upload
              class="upload-demo"
              action="#"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              :file-list="fileList"
              list-type="picture-card"
              multiple
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <div class="upload-tip">最少上传一张图片，支持jpg、png格式</div>
          </div>
        </el-form-item>

        <!-- 联系方式 -->
        <el-form-item label="联系方式" prop="reporterphone">
          <el-input
            v-model="formData.reporterphone"
            placeholder="请输入联系方式"
            maxlength="20"
          >
          </el-input>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <div class="submit-wrapper">
            <el-button
              type="primary"
              size="large"
              @click="onSubmit"
              :loading="submitting"
            >
              {{ submitting ? "提交中..." : "提交上报" }}
            </el-button>
            <el-button size="large" @click="resetForm">重置</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 地图弹窗 -->
    <el-dialog
      title="选择位置"
      :visible.sync="showMapDialog"
      width="80%"
      :before-close="handleMapClose"
    >
      <div class="map-dialog">
        <div class="map-placeholder">
          <p>地图组件暂未配置</p>
          <p>请手动输入位置信息</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showMapDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmLocation">确认位置</el-button>
      </span>
    </el-dialog>

    <!-- 惯用语选择弹窗 -->
    <el-dialog title="选择惯用语" :visible.sync="showIdiomDialog" width="50%">
      <div class="idiom-list">
        <el-button
          v-for="idiom in commonIdioms"
          :key="idiom"
          type="text"
          @click="selectIdiom(idiom)"
          class="idiom-item"
        >
          {{ idiom }}
        </el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showIdiomDialog = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import Map from '@/components/map/index.vue'
// import IdiomsSelector from "@/components/IdiomsSelector";
import { myRevelation } from "@/api/common";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "ProblemReport",
  components: {
    // Map,
    // IdiomsSelector
  },
  data() {
    return {
      showMapDialog: false,
      showIdiomDialog: false,
      type2id: "",
      submitting: false,
      fileList: [],
      commonIdioms: [
        "垃圾乱扔",
        "占道经营",
        "违章停车",
        "噪音扰民",
        "违法建筑",
        "环境污染",
        "其他问题",
      ],

      // 表单数据
      formData: {
        source: "",
        createid: "",
        areaid: "", //定位获取所属区县
        streetid: "", //定位获取所属街道
        address: "", //位置描述
        eventdesc: "", //问题描述
        reporterphone: "", //联系方式
        fileStr: "",
        x84: "",
        y84: "",
      },

      // 表单验证规则
      rules: {
        type2id: [
          { required: true, message: "请选择问题分类", trigger: "change" },
        ],
        address: [
          { required: true, message: "请输入位置描述", trigger: "blur" },
        ],
        eventdesc: [
          { required: true, message: "请输入问题描述", trigger: "blur" },
        ],
        reporterphone: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },

      // 选择器数据
      columns: {
        type2Options: [
          { text: "市容环境类", value: "市容环境类" },
          { text: "街面秩序类", value: "街面秩序类" },
          { text: "公共设施类", value: "公共设施类" },
          { text: "突发事件类", value: "突发事件类" },
          { text: "牛皮癣", value: "牛皮癣" },
          { text: "其他", value: "其他" },
        ],
      },
      areaOptions: [],
    };
  },
  created() {},
  mounted() {
    this.getDictsList();
    this.initParams();
  },
  methods: {
    historyPage() {
      this.$router.push("/newsHistory");
    },

    goBack() {
      this.$router.go(-1);
    },

    initParams() {
      this.formData.source = "10"; //浙里办上报
      this.formData.createid = this.$store.state.user.name || "";
    },

    //初始化字典表
    getDictsList() {
      //区划
      getDicts("county").then((response) => {
        let result = response.data;
        this.areaOptions = result.map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      });
      this.type2id = this.columns.type2Options[0].value;
    },

    handleLocationSelected(e) {
      console.log(e);
      this.formData.address = e.formatted_address;
      this.formData.x84 = e.location.lon;
      this.formData.y84 = e.location.lat;
      this.formData.areaid =
        this.areaOptions.find((item) => item.label == e.addressComponent.county)
          ?.value || "";
      this.formData.streetid = e.addressComponent.road;
    },

    confirmLocation() {
      this.showMapDialog = false;
    },

    handleMapClose() {
      this.showMapDialog = false;
    },

    // 文件上传相关方法
    handlePreview(file) {
      console.log(file);
    },

    handleRemove(file, fileList) {
      this.fileList = fileList;
      this.updateFileStr();
    },

    beforeUpload(file) {
      const isJPGOrPNG =
        file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPGOrPNG) {
        this.$message.error("只能上传 JPG/PNG 格式的图片!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return false;
      }

      // 模拟上传成功，实际项目中需要调用上传接口
      this.fileList.push({
        name: file.name,
        url: URL.createObjectURL(file),
        raw: file,
      });
      this.updateFileStr();
      return false; // 阻止自动上传
    },

    updateFileStr() {
      this.formData.fileStr = this.fileList.map((file) => file.url).join(",");
    },

    // 提交表单
    onSubmit() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          // 验证图片
          if (this.fileList.length === 0) {
            this.$message.error("请至少上传一张图片");
            return;
          }

          this.submitting = true;

          let formData = JSON.parse(JSON.stringify(this.formData));
          formData.reporterid = this.$store.state.user.name || "";
          formData.type2id = this.type2id;

          // 实际API调用
          myRevelation(formData)
            .then((res) => {
              this.submitting = false;
              if (res.code == 200) {
                this.$message.success("提交成功");
                this.resetFormData();
                this.$router.push("/newsHistory");
              } else {
                this.$message.error(res.msg || "提交失败");
              }
            })
            .catch((error) => {
              this.submitting = false;
              this.$message.error("提交失败，请重试");
            });
        } else {
          this.$message.error("请完善表单信息");
        }
      });
    },

    resetFormData() {
      this.$refs.reportForm.resetFields();
      this.fileList = [];
      this.type2id = "";
      this.formData = {
        source: "",
        createid: "",
        areaid: "",
        streetid: "",
        address: "",
        eventdesc: "",
        reporterphone: "",
        fileStr: "",
        x84: "",
        y84: "",
      };
    },

    resetForm() {
      this.resetFormData();
    },

    // 选择惯用语
    selectIdiom(content) {
      if (this.formData.eventdesc) {
        const parts = this.formData.eventdesc.split(":");
        if (parts.length > 1) {
          this.formData.eventdesc = parts[1];
        }
      }
      this.formData.eventdesc = "[" + this.type2id + "]:" + content;
      this.showIdiomDialog = false;
    },
  },
  watch: {
    type2id(val) {
      if (this.formData.eventdesc)
        this.formData.eventdesc = this.formData.eventdesc.split(":")[1];
      this.formData.eventdesc = "[" + val + "]:" + this.formData.eventdesc;
    },
  },
};
</script>

<style lang="scss" scoped>
.problem-report {
  min-height: 100vh;
  background-color: #fff;
  padding: 20px;
}

// 页面头部
.page-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e6e6e6;

  .page-header-main {
    flex: 1;
  }

  .page-header-extra {
    flex: 0 0 auto;
  }
}

// 表单内容
.form-content {
  background: #fff;
  padding: 0;
}

.report-form {
  max-width: 800px;
  margin: 0;

  .el-form-item {
    margin-bottom: 25px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
}

// 位置选择
.location-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;

  .location-tip {
    color: #999;
    font-size: 14px;
  }
}

// 问题描述
.description-wrapper {
  .idiom-selector {
    margin-top: 10px;
    text-align: right;
  }
}

// 上传组件
.upload-wrapper {
  .upload-demo {
    ::v-deep .el-upload--picture-card {
      width: 100px;
      height: 100px;
      line-height: 100px;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
    }

    ::v-deep .el-upload-list--picture-card .el-upload-list__item {
      width: 100px;
      height: 100px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }
  }

  .upload-tip {
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

// 提交按钮
.submit-wrapper {
  text-align: left;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;

  .el-button {
    margin-right: 15px;
    min-width: 120px;
  }
}

// 地图弹窗
.map-dialog {
  height: 500px;
  border-radius: 4px;
  overflow: hidden;

  .map-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #fafafa;
    color: #999;
    font-size: 16px;
    border: 1px solid #e6e6e6;

    p {
      margin: 10px 0;
    }
  }
}

// 惯用语列表
.idiom-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;

  .idiom-item {
    padding: 10px 15px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    text-align: center;
    transition: all 0.3s;
    background: #fff;

    &:hover {
      border-color: #409eff;
      color: #409eff;
      background: #f0f9ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .problem-report {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
    }
  }

  .form-content {
    padding: 0;
  }

  .report-form {
    .el-form-item__label {
      width: 100px !important;
    }
  }

  .location-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .submit-wrapper {
    text-align: center;

    .el-button {
      display: block;
      width: 100%;
      margin: 10px 0;
    }
  }
}

// 深度选择器样式调整
::v-deep .el-form-item__label {
  line-height: 1.5;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  border-radius: 4px;
}

::v-deep .el-select .el-input__inner {
  border-radius: 4px;
}

::v-deep .el-button {
  border-radius: 4px;
}

::v-deep .el-dialog {
  border-radius: 4px;
}

::v-deep .el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e6e6e6;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
