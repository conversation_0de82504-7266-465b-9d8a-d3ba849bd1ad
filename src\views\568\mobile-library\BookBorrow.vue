<template>
  <div class="book-borrow-container">
    <div class="borrow-content-wrapper">
      <!-- 页面标题 -->
      <div class="header-section">
        <div class="header-center">
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>
      </div>

      <!-- 书籍信息展示 -->
      <div class="book-info-section">
        <el-card class="book-card" shadow="never">
          <div class="book-display">
            <!-- 书籍封面 -->
            <div class="book-cover">
              <el-image
                v-if="imgSrc"
                class="cover-image"
                :src="imgSrc"
                fit="cover"
              >
                <div slot="placeholder" class="image-slot">
                  <i class="el-icon-loading"></i>
                </div>
                <div slot="error" class="image-slot">
                  <img :src="bookImg" alt="默认封面" />
                </div>
              </el-image>
              <img v-else :src="bookImg" alt="默认封面" class="cover-image" />
            </div>

            <!-- 书籍详细信息 -->
            <div class="book-details">
              <h3 class="book-title">{{ details.name || '书籍标题' }}</h3>
              <p class="book-content">{{ details.content || '暂无内容简介' }}</p>
              <div class="book-meta">
                <div class="author-section">
                  <span class="meta-label">作者：</span>
                  <span class="meta-value">{{ details.author || '未知作者' }}</span>
                  <el-tag
                    v-if="details.volCatName"
                    type="primary"
                    size="mini"
                    class="category-tag"
                  >
                    {{ details.volCatName }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 借阅人信息表单 -->
      <div class="borrower-info-section">
        <el-card class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="header-title">借阅人信息</span>
          </div>

          <el-form
            :model="borrowerForm"
            label-width="120px"
            class="borrower-form"
            label-position="left"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="借阅人：">
                  <el-input
                    v-model="borrowerForm.realName"
                    readonly
                    class="readonly-input"
                  >
                    <template slot="prepend">
                      <i class="el-icon-user"></i>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="联系电话：">
                  <el-input
                    v-model="borrowerForm.phone"
                    readonly
                    class="readonly-input"
                  >
                    <template slot="prepend">
                      <i class="el-icon-phone"></i>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="需归还日期：">
                  <el-input
                    v-model="borrowerForm.planEndTime"
                    readonly
                    class="readonly-input"
                  >
                    <template slot="prepend">
                      <i class="el-icon-date"></i>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>

      <!-- 温馨提示 -->
      <div class="tips-section">
        <el-alert
          title="温馨提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template slot="default">
            书籍属于免费公共资源，阅读完后请尽快归还！
          </template>
        </el-alert>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-section">
        <el-button
          type="primary"
          size="small"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          <i class="el-icon-check"></i>
          确认{{ type === '0' ? '借阅' : '送书' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getRecord, getDetails } from '@/api/book'

export default {
  name: 'PcBookBorrow',
  props: {
    volBookId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      // 默认书籍封面
      bookImg: require('@/assets/images/book.png'),
      // 书籍详情
      details: {},
      // 0-借书 1-送书
      type: '0',
      imgSrc: '',
      submitLoading: false,
      // 借阅人表单数据
      borrowerForm: {
        realName: '',
        phone: '',
        planEndTime: ''
      }
    }
  },
  computed: {
    pageTitle() {
      const titleMap = {
        '0': '立即借阅',
        '1': '一键送书'
      }
      return titleMap[this.type] || '书籍借阅'
    },

    successTip() {
      return this.type === '0' ? '借书成功' : '送书成功'
    }
  },
  created() {
    // 获取操作类型：0-借书 1-送书
    this.type = this.$route.query.type || '0'

    // 设置页面标题
    document.title = this.pageTitle

    // 设置还书日期（当前日期+7天）
    this.borrowerForm.planEndTime = dayjs().add(7, 'day').format('YYYY-MM-DD')

    // 设置借阅人信息
    this.setBorrowerInfo()

    // 获取书籍详情
    this.loadDetails()
  },
  methods: {
    // 设置借阅人信息
    setBorrowerInfo() {
      // 从vuex或其他地方获取用户信息
      this.borrowerForm.realName = this.vuex_user_realName || '当前用户'
      this.borrowerForm.phone = this.vuex_user_mobile || '138****8888'
    },

    // 获取书籍详情
    async loadDetails() {
      try {
        const data = await getDetails(this.volBookId)

        if (data.code !== 200) {
          this.$message.error('获取书籍详情失败')
          return
        }

        // 处理图片
        if (data.data.imgs && data.data.imgs[0] && data.data.imgs[0].filePath) {
          this.imgSrc = `${process.env.VUE_APP_BASE_API}${data.data.imgs[0].filePath}`
        } else {
          this.imgSrc = this.bookImg
        }

        this.details = data.data
      } catch (error) {
        console.error('获取书籍详情失败:', error)
        this.$message.error('网络异常，请重试！')
      }
    },

    // 提交借书/送书请求
    async handleSubmit() {
      try {
        this.submitLoading = true

        const requestData = {
          volBookId: this.volBookId,
          realName: this.borrowerForm.realName,
          phone: this.borrowerForm.phone,
          planEndTime: this.borrowerForm.planEndTime,
          type: this.type
        }

        const data = await getRecord(requestData)

        if (data.code !== 200) {
          this.$message.error(data.msg || '操作失败')
          return
        }

        this.$message.success(this.successTip)

        // 触发相关事件（如果需要）
        this.$bus && this.$bus.$emit('reLoadApprove')
        this.$bus && this.$bus.$emit('reloadMy')
        this.$bus && this.$bus.$emit('reloadHistory')

        // 触发成功事件，让父组件处理
        this.$emit('success')

      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('网络异常，请重试！')
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.book-borrow-container {
  padding: 0;
  background-color: transparent;
  min-height: auto;

  .borrow-content-wrapper {
    max-width: 100%;
    margin: 0 auto;
    border-radius: 12px;

  }

  .header-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .header-center {
      text-align: center;

      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

    .book-info-section {
      margin-bottom: 30px;

      .book-card {
        border: 1px solid #e4e7ed;

        .book-display {
          display: flex;
          gap: 20px;

          @media (max-width: 768px) {
            flex-direction: column;
          }

          .book-cover {
            flex: 0 0 120px;

            .cover-image {
              width: 120px;
              height: 160px;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .image-slot {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 120px;
              height: 160px;
              background: #f5f7fa;
              color: #909399;
              border-radius: 8px;
            }
          }

          .book-details {
            flex: 1;
            min-width: 0;

            .book-title {
              font-size: 20px;
              font-weight: 600;
              color: #303133;
              margin: 0 0 10px 0;
            }

            .book-content {
              font-size: 14px;
              color: #606266;
              line-height: 1.6;
              margin: 0 0 15px 0;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              overflow: hidden;
            }

            .book-meta {
              .author-section {
                display: flex;
                align-items: center;
                gap: 10px;
                flex-wrap: wrap;

                .meta-label {
                  color: #909399;
                  font-size: 14px;
                }

                .meta-value {
                  color: #303133;
                  font-size: 14px;
                  font-weight: 500;
                }

                .category-tag {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    .borrower-info-section {
      margin-bottom: 30px;

      .info-card {
        border: 1px solid #e4e7ed;

        .card-header {
          .header-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .borrower-form {
          .readonly-input {
            ::v-deep .el-input__inner {
              background-color: #f5f7fa;
              border-color: #e4e7ed;
              color: #606266;
            }
          }
        }
      }
    }

    .tips-section {
      margin-bottom: 30px;

      ::v-deep .el-alert {
        border-radius: 8px;

        .el-alert__content {
          .el-alert__title {
            font-size: 14px;
            line-height: 1.6;
          }
        }
      }
    }

    .submit-section {
      text-align: center;
      padding-top: 20px;

      .submit-button {
        padding: 15px 50px;
        font-size: 16px;
        border-radius: 8px;
        min-width: 200px;
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .book-borrow-container {
    padding: 10px;

    .borrow-card {
      .header-section {
        .header-left, .header-right {
          flex: 0 0 80px;
        }

        .header-center .page-title {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
