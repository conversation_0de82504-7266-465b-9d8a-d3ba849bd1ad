<template>
  <div class="news-detail-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="动态详情" class="py-[20px]"> </el-page-header>

    <!-- 加载状态 -->
    <div v-loading="loading" class="detail-content">
      <!-- 动态详情卡片 -->
      <div class="detail-card" shadow="never" v-if="newsDetail" >
        <div slot="header" class="card-header">
          <span class="card-title">{{
            newsDetail.title || newsDetail.serveTypeName
          }}</span>
          <div class="header-actions">
            <el-tag
              v-if="newsDetail.status"
              :type="getStatusType(newsDetail.status)"
              size="small"
            >
              {{ getStatusText(newsDetail.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <i class="el-icon-user"></i>
              <span class="label">志愿者：</span>
              <span class="value">{{ newsDetail.createBy | name }}</span>
            </div>
            <div class="info-item">
              <i class="el-icon-time"></i>
              <span class="label">发布时间：</span>
              <span class="value">{{ newsDetail.createTime }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <i class="el-icon-thumb"></i>
              <span class="label">点赞数：</span>
              <span class="value">{{ newsDetail.thumbsNum || 0 }}人</span>
            </div>
            <div class="info-item" v-if="newsDetail.serveTypeName">
              <i class="el-icon-collection-tag"></i>
              <span class="label">服务类型：</span>
              <span class="value">{{ newsDetail.serveTypeName }}</span>
            </div>
          </div>
        </div>

        <!-- 内容详情 -->
        <div class="content-section">
          <h3 class="section-title">内容详情</h3>
          <div
            class="content-text"
            v-html="formatContent(newsDetail.content)"
          ></div>
        </div>

        <!-- 图片展示 -->
        <div class="images-section" v-if="newsImages.length > 0">
          <h3 class="section-title">相关图片</h3>
          <div class="images-grid">
            <div
              v-for="(image, index) in newsImages"
              :key="index"
              class="image-item"
              @click="previewImage(index)"
            >
              <img :src="image" :alt="`图片${index + 1}`" />
              <div class="image-overlay">
                <i class="el-icon-zoom-in"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="goBack">
            <i class="el-icon-back"></i>
            返回列表
          </el-button>
          <el-button type="primary" @click="handleLike" :disabled="liking">
            <i class="el-icon-thumb"></i>
            {{ liking ? "点赞中..." : "点赞" }}
          </el-button>
          <el-button type="success" @click="handleShare">
            <i class="el-icon-share"></i>
            分享
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && !newsDetail"
        description="动态不存在或已被删除"
        :image-size="120"
      >
        <el-button type="primary" @click="goBack">返回列表</el-button>
      </el-empty>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="false"
      :close-on-click-modal="true"
      width="80%"
      custom-class="image-preview-dialog"
    >
      <div class="preview-container">
        <img :src="currentPreviewImage" alt="预览图片" class="preview-image" />
        <div class="preview-controls">
          <el-button
            type="primary"
            icon="el-icon-arrow-left"
            circle
            @click="prevImage"
            :disabled="currentImageIndex === 0"
          ></el-button>
          <span class="image-counter"
            >{{ currentImageIndex + 1 }} / {{ newsImages.length }}</span
          >
          <el-button
            type="primary"
            icon="el-icon-arrow-right"
            circle
            @click="nextImage"
            :disabled="currentImageIndex === newsImages.length - 1"
          ></el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getNewsDetail } from "@/api/568/home";

export default {
  name: "NewsDetail",
  data() {
    return {
      newsDetail: null,
      loading: false,
      liking: false,

      // 图片相关
      newsImages: [],
      previewVisible: false,
      currentImageIndex: 0,
    };
  },

  computed: {
    newsId() {
      return this.$route.params.newsId;
    },
    currentPreviewImage() {
      return this.newsImages[this.currentImageIndex] || "";
    },
  },

  created() {
    this.loadNewsDetail();
  },

  beforeDestroy() {
    // 清理sessionStorage中的缓存数据
    sessionStorage.removeItem('currentNewsItem');
  },

  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载动态详情
    async loadNewsDetail() {
      try {
        this.loading = true;

        // 先尝试从sessionStorage获取列表中的数据
        const cachedItem = sessionStorage.getItem('currentNewsItem');
        let listItemData = null;
        if (cachedItem) {
          try {
            listItemData = JSON.parse(cachedItem);
          } catch (e) {
            console.warn('解析缓存数据失败:', e);
          }
        }

        const data = await getNewsDetail(this.newsId);

        if (data.code !== 200) {
          this.$message.error("获取动态详情失败");
          return;
        }

        this.newsDetail = data.data;

        // 处理图片 - 兼容不同的数据结构
        if (this.newsDetail.imgs && this.newsDetail.imgs.length > 0) {
          this.newsImages = this.newsDetail.imgs.map((img) => {
            // 如果img是对象且有filePath属性
            if (typeof img === 'object' && img.filePath) {
              return `${process.env.VUE_APP_BASE_API}${img.filePath}`;
            }
            // 如果img直接是字符串路径
            if (typeof img === 'string') {
              return img.startsWith('http') ? img : `${process.env.VUE_APP_BASE_API}${img}`;
            }
            return img;
          });
        }

        // 如果没有imgs数组，但有newsImg属性，也添加到图片列表中
        if ((!this.newsImages || this.newsImages.length === 0) && this.newsDetail.newsImg) {
          this.newsImages = [this.newsDetail.newsImg];
        }

        // 如果详情API没有返回图片，但列表中有图片，使用列表中的图片
        if ((!this.newsImages || this.newsImages.length === 0) && listItemData && listItemData.newsImg) {
          this.newsImages = [listItemData.newsImg];
        }

        // 如果列表数据中有imgs数组，也尝试使用
        if ((!this.newsImages || this.newsImages.length === 0) && listItemData && listItemData.imgs) {
          this.newsImages = listItemData.imgs.map((img) => {
            if (typeof img === 'object' && img.filePath) {
              return `${process.env.VUE_APP_BASE_API}${img.filePath}`;
            }
            if (typeof img === 'string') {
              return img.startsWith('http') ? img : `${process.env.VUE_APP_BASE_API}${img}`;
            }
            return img;
          });
        }


      } catch (error) {
        console.error("获取动态详情失败:", error);
        this.$message.error("网络异常，请重试！");
      } finally {
        this.loading = false;
      }
    },

    // 格式化内容
    formatContent(content) {
      if (!content) return "";
      // 将换行符转换为<br>标签
      return content.replace(/\n/g, "<br>");
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        1: "info", // 新建
        2: "warning", // 审批中
        9: "success", // 已发布
      };
      return statusMap[status] || "info";
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "新建",
        2: "审批中",
        9: "已发布",
      };
      return statusMap[status] || "未知";
    },

    // 预览图片
    previewImage(index) {
      this.currentImageIndex = index;
      this.previewVisible = true;
    },

    // 上一张图片
    prevImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--;
      }
    },

    // 下一张图片
    nextImage() {
      if (this.currentImageIndex < this.newsImages.length - 1) {
        this.currentImageIndex++;
      }
    },

    // 点赞
    async handleLike() {
      try {
        this.liking = true;
        // 这里可以调用点赞API
        // await likeNews(this.newsId);
        this.$message.success("点赞成功！");
        // 更新点赞数
        if (this.newsDetail.thumbsNum) {
          this.newsDetail.thumbsNum++;
        } else {
          this.newsDetail.thumbsNum = 1;
        }
      } catch (error) {
        this.$message.error("点赞失败，请重试！");
      } finally {
        this.liking = false;
      }
    },

    // 分享
    handleShare() {
      // 复制链接到剪贴板
      const url = window.location.href;
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(url)
          .then(() => {
            this.$message.success("链接已复制到剪贴板！");
          })
          .catch(() => {
            this.$message.error("复制失败，请手动复制链接");
          });
      } else {
        // 降级处理
        this.$message.info("请手动复制当前页面链接进行分享");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.news-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 84px);

  .detail-content {
    min-height: 400px;
  }

  .detail-card {
    border-radius: 8px;
    border: none !important;

    .card-header {
      background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      color: white;
      padding: 20px;
      border-radius: 8px 8px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        flex: 1;
        margin-right: 15px;
      }

      .header-actions {
        flex-shrink: 0;
      }
    }

    ::v-deep .el-card__body {
      padding: 30px;
    }
  }

  // 基本信息
  .basic-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;

    .info-row {
      display: flex;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 30px;

        &:last-child {
          margin-right: 0;
        }

        i {
          color: #409eff;
          margin-right: 8px;
          font-size: 16px;
        }

        .label {
          color: #606266;
          font-weight: 500;
          margin-right: 5px;
        }

        .value {
          color: #303133;
          font-weight: 400;
        }
      }
    }
  }

  // 内容区域
  .content-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid #409eff;
    }

    .content-text {
      line-height: 1.8;
      color: #606266;
      font-size: 14px;
      padding: 15px;
      background-color: #fafafa;
      border-radius: 6px;
      border-left: 4px solid #409eff;

      ::v-deep br {
        margin-bottom: 10px;
      }
    }
  }

  // 图片区域
  .images-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid #409eff;
    }

    .images-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;

      .image-item {
        position: relative;
        height: 150px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

          .image-overlay {
            opacity: 1;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;

          i {
            color: white;
            font-size: 24px;
          }
        }
      }
    }
  }

  // 操作按钮
  .action-buttons {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;

    .el-button {
      margin: 0 10px;
      padding: 12px 24px;
      border-radius: 6px;

      i {
        margin-right: 5px;
      }
    }
  }
}

// 图片预览对话框
::v-deep .image-preview-dialog {
  .el-dialog {
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .preview-container {
    text-align: center;
    position: relative;

    .preview-image {
      max-width: 100%;
      max-height: 80vh;
      object-fit: contain;
    }

    .preview-controls {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      align-items: center;
      gap: 15px;
      background-color: rgba(0, 0, 0, 0.7);
      padding: 10px 20px;
      border-radius: 25px;

      .image-counter {
        color: white;
        font-size: 14px;
        min-width: 60px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail-container {
    padding: 10px;

    .detail-card {
      .card-header {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;

        .card-title {
          font-size: 16px;
          margin-right: 0;
        }
      }

      ::v-deep .el-card__body {
        padding: 20px;
      }
    }

    .basic-info {
      padding: 15px;

      .info-row {
        flex-direction: column;
        gap: 10px;

        .info-item {
          margin-right: 0;
        }
      }
    }

    .images-section {
      .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;

        .image-item {
          height: 120px;
        }
      }
    }

    .action-buttons {
      .el-button {
        margin: 5px;
        padding: 10px 20px;
      }
    }
  }
}
</style>
