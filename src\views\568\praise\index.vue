<template>
  <div class="praise-container">
    <el-page-header @back="goBack" content="我要赞美" class="py-[20px]"> </el-page-header>
    <el-card class="praise-card" shadow="never">
      <!-- 赞美内容表单 -->
      <el-form
        ref="praiseForm"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent="onSubmit"
      >
        <el-form-item label="赞美概述" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入赞美概述"
            :disabled="disable"
            clearable
          />
        </el-form-item>

        <el-form-item label="赞美内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="7"
            :maxlength="3000"
            :disabled="disable"
            placeholder="请输入一段详细描述..."
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <!-- 图片上传 -->
        <el-form-item label="上传图片">
          <div class="upload-section">
            <ImageUpload
              v-model="uploadedImages"
              :limit="9"
              :disabled="disable"
              :file-size="10"
              :is-show-tip="true"
            />
            <div class="upload-tip">
              <i class="el-icon-info"></i>
              最多可上传9张图片，单张图片不超过10MB
            </div>
          </div>
        </el-form-item>

        <!-- 审批进度步骤条 -->
        <el-form-item v-if="businessId" label="审批进度">
          <Step :vol-approve="volApprove" />
        </el-form-item>

        <div class="submit-section">
          <el-button
            type="primary"
            size="large"
            :loading="aloading"
            :disabled="disable"
            @click="onSubmit"
            class="submit-btn"
          >
            <i class="el-icon-check"></i>
            提交赞美
          </el-button>
          <el-button
            size="large"
            @click="resetForm"
            :disabled="disable || businessId"
          >
            重置
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { addNews, getNews } from "@/api/568/home";
import { uploadPic, downloadFile } from "@/api/568/index";
import Step from "@/components/step";

export default {
  name: "Praise",
  components: {
    Step,
  },
  data() {
    return {
      form: {
        title: "",
        content: "",
      },
      // 表单验证规则
      rules: {
        title: [
          { required: true, message: "请输入赞美概述", trigger: "blur" },
          {
            min: 2,
            max: 100,
            message: "长度在 2 到 100 个字符",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "请输入赞美内容", trigger: "blur" },
          {
            min: 10,
            max: 3000,
            message: "长度在 10 到 3000 个字符",
            trigger: "blur",
          },
        ],
      },
      // 上传的图片
      uploadedImages: "",
      // 原始文件列表（用于兼容原有逻辑）
      fileList: [],
      // 处理图片上传失败，文案提交id
      dataId: 0,
      businessId: "",
      // 审批详情
      volApprove: {},
      disable: false,
      aloading: false,
    };
  },
  computed: {},
  watch: {
    // 监听上传图片变化，转换为原有格式
    uploadedImages(newVal) {
      if (newVal) {
        // 将字符串转换为数组格式，兼容原有逻辑
        const imageUrls = newVal.split(",").filter((url) => url.trim());
        this.fileList = imageUrls.map((url) => ({
          url: url,
          file: null, // PC端上传后不需要file对象
        }));
      } else {
        this.fileList = [];
      }
    },
  },
  created() {
    this.businessId = this.$route.query.businessId;
    if (this.businessId) {
      this.loadNews();
      this.downloadFiles();
      this.disable = true;
    } else {
      this.disable = false;
    }
  },
  mounted() {
    // 埋点处理，计算页面加载完成时间
    // this.pv_getCalcTime('pv_time_t2')
  },
  methods: {
        // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    async loadNews() {
      const loading = this.$loading({
        lock: true,
        text: "加载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      try {
        const data = await getNews(this.businessId);
        if (data.code !== 200) {
          this.$message.error("获取数据失败");
          return;
        }

        // 设置审批详情
        this.volApprove = data.data.volApprove;
        this.form = { ...this.form, ...data.data };
      } catch (error) {
        this.$message.error("网络异常，请重试！");
        setTimeout(() => {
          this.$router.back();
        }, 1500);
      } finally {
        loading.close();
      }
    },

    // 获取图片
    async downloadFiles() {
      try {
        const data = await downloadFile({
          businessId: this.businessId,
          tableName: "vol_news",
        });

        if (!data || data.code !== 200) return;

        // 转换为ImageUpload组件需要的格式
        const imageUrls = data.rows.map(
          (item) =>
            `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`
        );
        this.uploadedImages = imageUrls.join(",");

        // 保持原有格式兼容
        this.fileList = data.rows.map((item) => {
          return {
            url: `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`,
          };
        });
      } catch (error) {
        this.$message.error("获取图片失败");
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.praiseForm.resetFields();
      this.uploadedImages = "";
      this.fileList = [];
    },
    onSubmit() {
      // 表单验证
      this.$refs.praiseForm.validate((valid) => {
        if (!valid) {
          this.$message.warning("请完善表单信息");
          return false;
        }

        // 确认提交
        this.$confirm(
          "是否确认提交赞美内容？提交后可在“我的-审批事项”中查看进度",
          "提交赞美内容",
          {
            confirmButtonText: "确定提交",
            cancelButtonText: "取消",
            type: "info",
            center: true,
          }
        )
          .then(async () => {
            await this.submitPraise();
          })
          .catch(() => {
            // 用户取消提交
          });
      });
    },

    async submitPraise() {
      this.aloading = true;
      const loading = this.$loading({
        lock: true,
        text: "提交中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      try {
        // type 1-服务记录 2-我要赞美 3-站前动态
        // status:1-新建，2-审批中，9-同意
        const params = {
          ...this.form,
          status: 2,
          type: 2,
          volUserId: this.vuex_user_vol_id,
          volUserName: this.vuex_user_realName,
        };

        // 上传除图片外的其他信息
        if (this.dataId === 0) {
          const data = await addNews(params);
          if (data.code !== 200) {
            this.$message.error("提交失败，请重试");
            return;
          }
          this.dataId = data.data.id;
        }

        // 处理图片上传（如果有图片且是新上传的）
        if (this.fileList.length && this.fileList.some((item) => item.file)) {
          const fd = new FormData();
          this.fileList.forEach((v) => {
            if (v.file) {
              fd.append("files", v.file);
            }
          });
          fd.append("businessId", this.dataId);
          fd.append("tableName", "vol_news");
          fd.append("status", 2);

          const uploadPicData = await uploadPic(fd);
          if (!uploadPicData || uploadPicData.code !== 200) {
            this.$message.error("图片上传失败，请重新提交");
            return;
          }
        }

        this.$message.success("提交成功！");

        // 重置表单
        this.resetForm();
        this.dataId = 0;

        // 延迟返回，让用户看到成功提示
        setTimeout(() => {
          this.$router.back();
        }, 1500);
      } catch (error) {
        this.$message.error("网络异常，请重试！");
      } finally {
        this.aloading = false;
        loading.close();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.praise-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;


  .praise-card {
    .card-header {
      text-align: center;
      padding: 20px 0;

      h3 {
        margin: 0 0 8px 0;
        color: #409eff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .upload-section {
    .upload-tip {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
      display: flex;
      align-items: center;

      i {
        margin-right: 4px;
        color: #409eff;
      }
    }
  }

  .submit-section {
    text-align: center;
    margin-top: 30px;

    .submit-btn {
      min-width: 120px;
      margin-right: 16px;

      i {
        margin-right: 4px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .praise-card {
      margin: 0;
      border-radius: 8px;

      .card-header {
        padding: 15px 0;

        h3 {
          font-size: 20px;
        }
      }
    }

    .submit-section {
      .submit-btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
      }

      .el-button:last-child {
        width: 100%;
      }
    }
  }
}

// Element UI 组件样式覆盖
::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  border-radius: 6px;
  transition: all 0.3s ease;

  &:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

::v-deep .el-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
    border: none;
  }
}

::v-deep .el-card {
  border: none;

  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
  }
}
</style>
