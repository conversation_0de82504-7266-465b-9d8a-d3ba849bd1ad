<template>
  <div class="honor_container">
    <el-page-header @back="goBack" content="荣誉榜" class="py-[20px]">
    </el-page-header>

    <!-- 荣誉榜 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 志愿榜 -->
      <el-tab-pane label="志愿榜" name="volunteer">
        <div class="rank_header">
          <el-button
            type="primary"
            @click="refreshVolRank"
            :loading="volLoading"
          >
            刷新数据
          </el-button>
        </div>

        <el-table
          :data="volRankLists"
          v-loading="volLoading"
          stripe
          style="width: 100%"
          @row-click="handleRowClick"
        >
          <el-table-column label="排名" width="80" align="center">
            <template slot-scope="scope">
              <div class="ranking">
                <svg-icon
                  v-if="scope.$index === 0"
                  class="rank_icon"
                  icon-class="one"
                />
                <svg-icon
                  v-else-if="scope.$index === 1"
                  class="rank_icon"
                  icon-class="two"
                />
                <svg-icon
                  v-else-if="scope.$index === 2"
                  class="rank_icon"
                  icon-class="three"
                />
                <span v-else class="rank_number">{{ scope.$index + 1 }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="头像" min-width="80" align="center">
            <template slot-scope="scope">
              <el-avatar
                :src="scope.row.profile || avaterSrc"
                :size="50"
                class="avatar"
              />
            </template>
          </el-table-column>

          <el-table-column label="姓名" prop="realName" min-width="120">
            <template slot-scope="scope">
              <span class="vol_name">{{ scope.row.realName | name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="评价分数" min-width="120" align="center">
            <template slot-scope="scope">
              <div class="score">
                评价<span class="score_digit">{{ scope.row.score }}</span
                >分
              </div>
            </template>
          </el-table-column>

          <el-table-column label="服务时长" min-width="150" align="center">
            <template slot-scope="scope">
              <div class="duration_hour">
                <div class="total_time">总时长</div>
                <div>{{ scope.row.durationHour }}小时</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click.stop="previewCode(scope.row.qrCode)"
                icon="el-icon-view"
              >
                二维码
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="volTotal > 0"
          :total="volTotal"
          :page.sync="volPageNum"
          :limit.sync="volPageSize"
          @pagination="loadVolRank"
        />
      </el-tab-pane>

      <!-- 积分榜 -->
      <!-- <el-tab-pane label="积分榜" name="score">
        <div class="rank_header">
          <el-button
            type="primary"
            @click="refreshGiftScoreRank"
            :loading="giftScoreLoading"
          >
            刷新数据
          </el-button>
        </div>

        <el-table
          :data="giftScoreRankLists"
          v-loading="giftScoreLoading"
          stripe
          style="width: 100%"
          @row-click="handleRowClick"
        >
          <el-table-column label="排名" width="80" align="center">
            <template slot-scope="scope">
              <div class="ranking">
                <svg-icon
                  v-if="scope.$index === 0"
                  class="rank_icon"
                  icon-class="one"
                />
                <svg-icon
                  v-else-if="scope.$index === 1"
                  class="rank_icon"
                  icon-class="two"
                />
                <svg-icon
                  v-else-if="scope.$index === 2"
                  class="rank_icon"
                  icon-class="three"
                />
                <span v-else class="rank_number">{{ scope.$index + 1 }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="头像" min-width="80" align="center">
            <template slot-scope="scope">
              <el-avatar
                :src="scope.row.profile || avaterSrc"
                :size="50"
                class="avatar"
              />
            </template>
          </el-table-column>

          <el-table-column label="姓名" prop="realName" min-width="120">
            <template slot-scope="scope">
              <span class="vol_name">{{ scope.row.realName | name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="评价分数" min-width="120" align="center">
            <template slot-scope="scope">
              <div class="score">
                评价<span class="score_digit">{{ scope.row.score }}</span
                >分
              </div>
            </template>
          </el-table-column>

          <el-table-column label="服务时长" min-width="150" align="center">
            <template slot-scope="scope">
              <div class="duration_hour">
                <div class="total_time">总时长</div>
                <div>{{ scope.row.durationHour }}小时</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="积分" width="120" align="center">
            <template slot-scope="scope">
              <div class="gift_score">
                <svg-icon class="gift_score_icon" icon-class="score" />
                <span>{{ scope.row.giftScore }} 分</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-wrapper">
          <pagination
            v-show="giftScoreTotal > 0"
            :total="giftScoreTotal"
            :page.sync="giftScorePageNum"
            :limit.sync="giftScorePageSize"
            @pagination="loadGiftScoreRank"
          />
        </div>
      </el-tab-pane> -->
    </el-tabs>

    <!-- 二维码显示与隐藏 -->
    <el-dialog
      :visible.sync="codeShow"
      title="志愿者评价二维码"
      width="300px"
      center
    >
      <div class="code_dialog_content">
        <img :src="`data:image/jpeg;base64,${codeUrl}`" class="qr_code_img" />
        <div class="code_word">扫一扫，评价志愿者</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRankLists } from "@/api/568/honor";
import avater from "@/assets/images/honor_avater.png";

export default {
  name: "Honor",
  components: {},
  props: {},
  data() {
    return {
      activeTab: "volunteer",
      // 志愿榜分页参数
      volLoading: false,
      volPageNum: 1,
      volPageSize: 10,
      volTotal: 0,
      // 二维码的宽高
      volWidth: "300",
      volHeight: "300",
      volRankLists: [],
      // score:服务评分 gift_score:积分
      orderByColumn: "score",
      isAsc: "desc",
      // 积分榜分页参数
      giftScoreLoading: false,
      giftScorePageNum: 1,
      giftScorePageSize: 10,
      giftScoreTotal: 0,
      giftScoreRankLists: [],
      // 二维码显示与隐藏
      codeShow: false,
      codeUrl: "",
      avaterSrc: avater,
    };
  },
  computed: {},
  watch: {},
  mounted() {
    this.loadVolRank();
  },
  created() {},
  methods: {
    // 标签页点击事件
    handleTabClick(tab) {
      if (tab.name === "volunteer" && this.volRankLists.length === 0) {
        this.loadVolRank();
      } else if (tab.name === "score" && this.giftScoreRankLists.length === 0) {
        this.loadGiftScoreRank();
      }
    },

    // 行点击事件
    handleRowClick(row) {
      this.$router.push({
        name: "ServeRecord",
        query: { volUserId: row.userId },
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取志愿榜列表数据
    async loadVolRank() {
      this.volLoading = true;
      try {
        const data = await getRankLists({
          url: this.vuex_link,
          width: this.volWidth,
          height: this.volHeight,
          pageNum: this.volPageNum,
          pageSize: this.volPageSize,
          orderByColumn: this.orderByColumn,
          isAsc: this.isAsc,
        });

        data.rows.forEach((item) => {
          let reg = /^\/.*/;
          item.profile =
            item.profile && reg.test(item.profile)
              ? `${process.env.VUE_APP_BASE_API}${item.profile}`
              : item.headpicture
              ? item.headpicture
              : this.avaterSrc;
        });

        this.volRankLists = data.rows;
        this.volTotal = data.total;
      } catch (error) {
        this.$message.error("网络异常，请重试！");
      } finally {
        this.volLoading = false;
      }
    },

    // 志愿榜刷新
    refreshVolRank() {
      this.volPageNum = 1;
      this.loadVolRank();
    },

    // 志愿榜分页变化
    handleVolPageChange(page) {
      this.volPageNum = page;
      this.loadVolRank();
    },

    // 获取积分榜列表数据
    async loadGiftScoreRank() {
      this.giftScoreLoading = true;
      try {
        const data = await getRankLists({
          pageNum: this.giftScorePageNum,
          pageSize: this.giftScorePageSize,
          orderByColumn: "gift_score",
          isAsc: this.isAsc,
        });

        data.rows.forEach((item) => {
          let reg = /^\/.*/;
          item.profile =
            item.profile && reg.test(item.profile)
              ? `${process.env.VUE_APP_BASE_API}${item.profile}`
              : item.headpicture
              ? item.headpicture
              : this.avaterSrc;
        });

        this.giftScoreRankLists = data.rows;
        this.giftScoreTotal = data.total;
      } catch (error) {
        this.$message.error("网络异常，请重试！");
      } finally {
        this.giftScoreLoading = false;
      }
    },

    // 积分榜刷新
    refreshGiftScoreRank() {
      this.giftScorePageNum = 1;
      this.loadGiftScoreRank();
    },

    // 积分榜分页变化
    handleGiftScorePageChange(page) {
      this.giftScorePageNum = page;
      this.loadGiftScoreRank();
    },

    // 预览二维码
    previewCode(qrCode) {
      this.codeUrl = qrCode;
      this.codeShow = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.honor_container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .rank_header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }

  // 排名样式
  .ranking {
    display: flex;
    align-items: center;
    justify-content: center;

    .rank_icon {
      width: 24px;
      height: 26px;
    }

    .rank_number {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  // 头像样式
  .avatar {
    border: 2px solid #f0f0f0;
  }

  // 姓名样式
  .vol_name {
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }

  // 评分样式
  .score {
    font-size: 14px;
    color: #666;

    .score_digit {
      font-size: 18px;
      color: #4689f5;
      font-weight: 600;
      margin: 0 2px;
    }
  }

  // 服务时长样式
  .duration_hour {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px 4px 0;
    background: #e5f0ff;
    border-radius: 12px;
    font-size: 13px;
    color: #4689f5;

    &.elder {
      background: #f0f0f0;
    }

    .total_time {
      color: #fff;
      padding: 2px 8px;
      background: url("~@/assets/images/time_bg.png") no-repeat center / cover;
      border-radius: 8px;
      margin-right: 8px;
      font-size: 12px;
    }
  }

  // 积分样式
  .gift_score {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    font-weight: 500;

    &.elder {
      color: #666;
    }

    .gift_score_icon {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
  }

  // 分页样式
  .pagination {
    margin-top: 30px;
    text-align: center;
  }
}

// 二维码弹窗样式
.code_dialog_content {
  text-align: center;

  .qr_code_img {
    width: 200px;
    height: 200px;
    margin-bottom: 15px;
  }

  .code_word {
    font-size: 16px;
    color: #666;

    &.elder {
      font-size: 14px;
    }
  }
}
</style>
