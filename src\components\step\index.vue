<template>
  <div class="step_container">
    <el-steps direction="vertical" :active="active" finish-status="success">
      <el-step title="申请中" :description="stepDescriptions.apply">
        <template slot="icon">
          <i class="el-icon-edit"></i>
        </template>
      </el-step>
      <el-step
        v-if="volApprove.approveType == 0"
        title="审批通过"
        :description="stepDescriptions.approve"
        status="success"
      >
        <template slot="icon">
          <i class="el-icon-check"></i>
        </template>
      </el-step>
      <el-step
        v-else-if="volApprove.approveType == 1"
        title="审批驳回"
        :description="stepDescriptions.reject"
        status="error"
      >
        <template slot="icon">
          <i class="el-icon-close"></i>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script>

export default {
  name: 'Step',
  components: {

  },
  props: {
    volApprove: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      active: 0
    }
  },
  computed: {
    stepDescriptions() {
      return {
        apply: this.volApprove.happenTime ? `申请时间：${this.volApprove.happenTime}` : '等待申请',
        approve: this.getApproveDescription(),
        reject: this.getRejectDescription()
      }
    }
  },
  watch: {
    volApprove: {
      handler(val) {
        if (val && val.approveTime) {
          this.active = 1
        } else {
          this.active = 0
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getApproveDescription() {
      if (!this.volApprove.approveTime) return '等待审批';

      let desc = `审批时间：${this.volApprove.approveTime}`;
      if (this.volApprove.approveReason) {
        desc += `\n审批理由：${this.volApprove.approveReason}`;
      }
      return desc;
    },

    getRejectDescription() {
      if (!this.volApprove.approveTime) return '等待审批';

      let desc = `驳回时间：${this.volApprove.approveTime}`;
      if (this.volApprove.approveReason) {
        desc += `\n驳回理由：${this.volApprove.approveReason}`;
      }
      return desc;
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

