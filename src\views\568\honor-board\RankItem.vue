<template>
  <div class="rank_item_container">
    <div class="rank_item" @click="$router.push({name:'Serve',query:{volUserId:rankContent.userId}})">
      <!-- 名次 -->
      <div class="ranking">
        <svg-icon v-if="rankIndex==0" class="rank_icon" icon-class="one" />
        <svg-icon v-else-if="rankIndex==1" class="rank_icon" icon-class="two" />
        <svg-icon v-else-if="rankIndex==2" class="rank_icon" icon-class="three" />
        <span v-else>{{ rankIndex + 1 }}</span>
      </div>

      <!-- 头像 -->
      <div class="honor_avater">
        <img :src="rankContent.profile?rankContent.profile:avaterSrc" alt="">
      </div>

      <div class="vol_info">
        <div class="vol_name">{{ rankContent.realName | name }}</div>
        <div class="score">评价<span class="score_digit">{{ rankContent.score }}</span>分</div>
        <div class="duration_hour" :class="vuex_uiStyle === 'elder' ? 'elder' : ''">
          <div class="total_time">总时长</div>
          <div>{{ rankContent.durationHour }}小时</div>
        </div>
      </div>

      <!-- 二维码 -->
      <slot />
    </div>
  </div>
</template>

<script>
import avater from '@/assets/images/honor_avater.png'
export default {
  name: 'RankItem',
  components: {

  },
  props: {
    rankContent: {
      type: Object,
      require: true
    },
    rankIndex: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      active: 0,
      avaterSrc: avater
    }
  },
  computed: {

  },
  watch: {
  },
  mounted() {

  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.rank_item_container {
  .rank_item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px 24px;
    height: auto;
    min-height: 100px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: #f8f9fa;
    }

    // 排序
    .ranking {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      font-size: 16px;
      font-weight: 600;
      color: #333;

      .rank_icon {
        width: 32px;
        height: 36px;
      }
    }

    // 头像
    .honor_avater {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      margin: 0 20px 0 16px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      img {
        border-radius: 50%;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // 志愿者信息
    .vol_info {
      position: relative;
      flex: 1;

      .vol_name {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .score {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;

        .score_digit {
          font-size: 20px;
          color: #4689f5;
          font-weight: 600;
          margin: 0 2px;
        }
      }

      .duration_hour {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px 4px 0;
        background: #e5f0ff;
        border-radius: 12px;
        font-size: 13px;
        color: #4689f5;

        &.elder {
          position: static;
          margin-top: 8px;
        }

        .total_time {
          color: #fff;
          padding: 2px 8px;
          background: url('~@/assets/images/time_bg.png') no-repeat center / cover;
          border-radius: 8px;
          margin-right: 8px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
