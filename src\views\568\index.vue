<template>
  <div class="app-container volunteer-management">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h1 class="welcome-title">568志愿服务</h1>
      <p class="welcome-subtitle">传递爱心，服务社会，共建美好家园</p>
    </div>

    <!-- 统计数据 -->
    <div class="stats-container">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-time"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">5,678</div>
            <div class="stat-label">服务时长(时)</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="el-icon-coin"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">12,345</div>
            <div class="stat-label">积分明细(分)</div>
          </div>
        </div>
      </div>
    </div>

    <div class="service-grid">
      <!-- 动态生成服务模块 -->
      <div
        v-for="section in serviceSections"
        :key="section.name"
        class="service-section"
      >
        <h2 class="section-title">
          <svg-icon
            v-if="section.meta.icon && section.meta.icon !== '#'"
            :icon-class="section.meta.icon"
            class-name="section-icon"
          />
          {{ section.meta.title }}
        </h2>
        <div class="service-cards">
          <div
            v-for="item in section.children"
            :key="item.name"
            class="service-card"
            @click="handleCardClick(item)"
          >
            <svg-icon
              v-if="item.meta.icon && item.meta.icon !== '#'"
              :icon-class="item.meta.icon"
              class="!w-[48px] !h-[48px] mr-4 text-[#666]"
            />

            <div class="card-content">
              <h3 class="card-title">{{ item.meta.title }}</h3>
              <p class="card-description">
                {{ getDescription(item.meta.title) }}
              </p>
            </div>
            <div class="card-arrow">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建服务弹窗 -->
    <el-dialog
      title="新建服务记录"
      :visible.sync="addServeDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleAddServeDialogClose"
    >
      <add-serve-form
        v-if="addServeDialogVisible"
        ref="addServeForm"
        :form-data="null"
        :is-edit="false"
        @success="handleAddServeSuccess"
        @cancel="addServeDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import AddServeForm from "./serve-record/AddServeForm.vue";

export default {
  name: "VolunteerIndex",
  components: {
    AddServeForm,
  },
  data() {
    return {
      serviceSections: [],
      // 新建服务弹窗控制
      addServeDialogVisible: false,
      // 描述映射，为每个功能提供描述
      descriptionMap: {
        "568我来帮": "志愿服务首页，查看最新动态",
        "我要参加": "查看志愿地图，寻找附近的志愿活动",
        "我的": "个人中心，管理我的志愿服务信息",
        "服务预约列表": "查看和管理服务预约信息",
        "助老助残": "为老年人和残障人士提供志愿服务",
        "接受预约": "处理和接受服务预约申请",
        "选择服务者": "为服务需求选择合适的志愿者",
        "图书馆": "PC端图书管理系统，浏览和借阅图书",
        "流动书吧": "移动图书服务，传递知识温暖",
        "我的借阅": "查看我的借阅记录",
        "分类书籍列表": "按分类浏览图书资源",
        "借书送书": "图书借阅和配送服务",
        "书籍详情": "查看图书详细信息",
        "我的建议": "提交意见建议，改进服务质量",
        "荣誉榜": "展示优秀志愿者和服务团队",
        "在线志愿者": "查看在线志愿者状态",
        "服务条款": "了解平台服务条款和规则",
        "加入我们": "申请成为志愿者，加入志愿服务团队",
        "加入我们审核": "志愿者申请审核管理",
        "我要赞美": "表扬优秀志愿服务，传递正能量",
        "签到签退": "志愿服务签到签退管理",
        "补卡": "补充遗漏的服务打卡",
        "服务记录": "服务志愿服务记录管理",
        "新增服务记录": "添加新的服务记录",
        "服务详情": "查看具体服务详细信息",
        "动态详情": "查看平台最新动态",
        "服务监督-审核": "对志愿服务进行审核",
        "服务监督-评价": "对志愿服务进行评价",
        "服务监督-监督": "监督志愿服务质量",
        "志愿者评价": "查看志愿者服务评价",
        "志愿者监督记录": "志愿者监督管理记录",
        "头像": "更新个人头像信息",
        "我的任务": "查看我的志愿服务任务",
        "我的评价": "查看我收到的服务评价",
        "评价详情": "查看详细评价信息",
        "积分好礼": "积分兑换和奖励管理",
        "审批事项": "处理各类审批申请",
        "任务报名-审批进度": "查看任务报名审批状态",
        "助老助残-流程管理": "管理助老助残服务流程",
        "助老助残-审批进度": "查看助老助残审批进度",
        "服务说明": "了解各项服务的详细说明",
      },
    };
  },
  computed: {
    ...mapState({
      routes: (state) => state.permission.routes,
      sidebarRouters: (state) => state.permission.sidebarRouters,
    }),
  },
  mounted() {
    this.loadServiceSections();
  },
  methods: {
    // 处理卡片点击事件
    handleCardClick(item) {
      // 如果是新增服务记录，显示弹窗
      if (item.name === 'AddServe') {
        this.addServeDialogVisible = true;
      } else {
        // 其他功能正常跳转
        this.navigateTo(item.path);
      }
    },
    navigateTo(path) {
      // 处理路径，确保正确跳转
      try {
        if (path === '/') {
          // 首页跳转，使用zqgl路由
          this.$router.push('/zqgl');
        } else if (path.startsWith('/')) {
          this.$router.push(path);
        } else {
          this.$router.push(`/${path}`);
        }
      } catch (error) {
        console.error('路由跳转失败:', error);
        this.$message.warning(`功能"${path}"正在开发中，敬请期待！`);
      }
    },
    // 新建服务弹窗关闭
    handleAddServeDialogClose() {
      this.addServeDialogVisible = false;
    },
    // 新建服务成功
    handleAddServeSuccess() {
      this.addServeDialogVisible = false;
      this.$message.success("新建服务记录成功！");
      // 可以在这里添加其他成功后的处理逻辑
    },
    loadServiceSections() {
      // 基于index-zqgl.js路由结构的服务模块
      // 按功能模块组织服务，对应实际的路由配置
      this.serviceSections = [
        {
          name: "core-services",
          meta: { title: "核心服务", icon: "star" },
          children: [
            {
              name: "Home",
              path: "/568/news",
              meta: { title: "568我来帮", icon: "dashboard" }
            },
            {
              name: "Map",
              path: "/map",
              meta: { title: "我要参加", icon: "map" }
            },
            {
              name: "Mine",
              path: "/568/mine",
              meta: { title: "我的", icon: "user" }
            }
          ]
        },
        {
          name: "service-appointment",
          meta: { title: "服务预约", icon: "date" },
          children: [
            {
              name: "Appointment",
              path: "/appointment",
              meta: { title: "服务预约列表", icon: "list" }
            },
            {
              name: "ElderlyAssistance",
              path: "/568/elderly-assistance",
              meta: { title: "助老助残", icon: "peoples" }
            },
            {
              name: "ChooseServers",
              path: "/choose-servers",
              meta: { title: "选择服务者", icon: "peoples" }
            }
          ]
        },
        {
          name: "book-services",
          meta: { title: "图书服务", icon: "education" },
          children: [
            {
              name: "MobileLibrary",
              path: "/568/mobile-library",
              meta: { title: "流动书吧", icon: "education" }
            },
            {
              name: "BorrowBook",
              path: "/568/borrow-book",
              meta: { title: "我的借阅", icon: "component" }
            }
          ]
        },
        {
          name: "service-record",
          meta: { title: "服务记录", icon: "record" },
          children: [
            {
              name: "ServeRecord",
              path: "/568/serve-record",
              meta: { title: "服务记录", icon: "documentation" }
            },
            {
              name: "AddServe",
              path: "/add-serve",
              meta: { title: "新增服务记录", icon: "edit" }
            },
            {
              name: "CheckIn",
              path: "/568/check-in",
              meta: { title: "签到签退", icon: "time" }
            }
          ]
        },
        {
          name: "community-services",
          meta: { title: "社区服务", icon: "people" },
          children: [
            {
              name: "Vol",
              path: "/vol",
              meta: { title: "在线志愿者", icon: "online" }
            },
            {
              name: "HonorBoard",
              path: "/568/honor-board",
              meta: { title: "荣誉榜", icon: "star" }
            },
            {
              name: "JoinUs",
              path: "/568/join-us",
              meta: { title: "加入我们", icon: "user" }
            },
            {
              name: "Praise",
              path: "/568/praise",
              meta: { title: "我要赞美", icon: "like" }
            }
          ]
        },
        {
          name: "management-services",
          meta: { title: "管理服务", icon: "system" },
          children: [
            {
              name: "Suggestion",
              path: "/568/suggestion",
              meta: { title: "我的建议", icon: "message" }
            },
            {
              name: "ReviewApply",
              path: "/review-apply",
              meta: { title: "审批事项", icon: "confirm" }
            },
            {
              name: "MineScore",
              path: "/mine-score",
              meta: { title: "积分好礼", icon: "shopping" }
            }
          ]
        }
      ];
    },
    getDescription(title) {
      return this.descriptionMap[title] || "提供相关志愿服务功能";
    },
  },
};
</script>

<style scoped lang="scss">
.volunteer-management {
  padding: 20px;
  min-height: calc(100vh - 50px);
  background: linear-gradient(to bottom, #409eff 100%, #66b1ff 0%);

  .welcome-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;

    .welcome-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .stats-container {
    margin-bottom: 40px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
      max-width: 600px;
      margin: 0 auto;
    }

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
      }

      .stat-icon {
        font-size: 32px;
        color: #409eff;
        margin-right: 15px;
        min-width: 48px;
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }
  }

  .service-grid {
    max-width: 1200px;
    margin: 0 auto;
  }

  .service-section {
    margin-bottom: 40px;

    .section-title {
      color: white;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .section-icon {
        font-size: 28px;
        margin-right: 12px;
      }
    }

    .service-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
    }
  }

  .service-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #66b1ff, #409eff);
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 6px 0;
      }

      .card-description {
        font-size: 13px;
        color: #666;
        margin: 0;
        line-height: 1.4;
      }
    }

    .card-arrow {
      color: #999;
      font-size: 16px;
      transition: all 0.3s ease;
      margin-left: 8px;
    }

    &:hover .card-arrow {
      color: #409eff;
      transform: translateX(4px);
    }
  }

  // 弹窗样式覆盖
  ::v-deep .el-dialog {
    .el-dialog__header {
      background: #f5f7fa;
      padding: 20px 20px 10px;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      text-align: right;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .welcome-section {
      margin-bottom: 30px;

      .welcome-title {
        font-size: 24px;
      }

      .welcome-subtitle {
        font-size: 14px;
      }
    }

    .stats-container {
      margin-bottom: 30px;

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 300px;
      }
    }

    .service-section {
      margin-bottom: 30px;

      .section-title {
        font-size: 20px;

        .section-icon {
          font-size: 24px;
          margin-right: 8px;
        }
      }

      .service-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
      }
    }

    .service-card {
      padding: 16px;

      .card-content {
        .card-title {
          font-size: 15px;
        }

        .card-description {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
