import request from "@/utils/request";
import Cookies from "js-cookie";
import store from "@/store/index";

// 浙里办APP和支付宝登录方法
export function login(ticket) {
  return new Promise((resolve, reject) => {
    const userId = store.state.legacy568.vuex_user_vol_id;
    let url = `/business/vol/user/zlbLogin/${ticket}`;
    let params = userId ? { userId } : {};
    request({
      url: url,
      method: "get",
      params: params,
    })
      .then((res) => {
        const { authToken } = res.data;
        Cookies.set("ZlbAuthorization", authToken);
        // const token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl96bGJfdXNlcl9rZXkiOiIxZDExYjEwYS01NjNkLTQzYjctOWE4NS1mMDRjYTZiMGI4MTIifQ.O91NIBEmB8a2lX-tbh9aK1P3BQRzDfH1HifCiFrkDgvwlNbvCsZNq2JvOFTh1SR3wLSU4oecMzaDM7Ji1W3Dug'
        const token = res.data.authToken;
        store.commit("legacy568/$uStore", { name: "vuex_token", value: token });
        resolve(res);
      })
      .catch(() => reject());
  });
}

//
export function loginProd() {
  return new Promise((resolve, reject) => {
    let url = `/loginV2`;
    request({
      url: url,
      method: "get",
    })
      .then((res) => {
        Cookies.set("prodToken", res.token);
        resolve(res);
      })
      .catch(() => reject());
  });
}

//
export function wxLogin(ticket) {
  return new Promise((resolve, reject) => {
    const userId = store.state.legacy568.vuex_user_vol_id;
    let url = `/business/vol/user/zlbWxLogin/${ticket}`;
    let params = userId ? { userId } : {};
    request({
      url: url,
      method: "get",
      params: params,
    })
      .then((res) => {
        const { authToken } = res.data;
        Cookies.set("ZlbAuthorization", authToken);
        const token = res.data.authToken;
        store.commit("legacy568/$uStore", { name: "vuex_token", value: token });
        resolve(res);
      })
      .catch(() => reject());
  });
}

// 查询字典
export function getCase(type) {
  return request({
    url: `/business/vol/system/type/${type}`,
    method: "GET",
  });
}

// 查询用户头像
export function getAvatar(userId) {
  return request({
    url: `/business/vol/user/${userId}`,
    method: "GET",
  });
}

// 图片上传
export function uploadPic(data) {
  return request({
    url: "/business/vol/system/upload",
    method: "POST",
    data,
  });
}
// 头像上传
export function editHead(data) {
  return request({
    url: "/business/vol/system/editHead",
    method: "POST",
    data,
  });
}

// 获取文件
export function downloadFile(params) {
  return request({
    url: "/business/vol/system/list",
    method: "GET",
    params,
  });
}

// 删除图片
export function deleteFile(id) {
  return request({
    url: `/business/vol/system/remove/${id}`,
    method: "POST",
  });
}
