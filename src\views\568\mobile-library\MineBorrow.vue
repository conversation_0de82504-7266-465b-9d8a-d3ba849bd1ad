<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="我的借阅">
    </el-page-header>

    <!-- 用户信息卡片 -->
    <el-card class="user-info-card" shadow="never">
      <div class="user-info-content">
        <!-- 用户头像和基本信息 -->
        <div class="user-basic-info">
          <el-avatar
            :size="80"
            :src="profile"
            class="user-avatar"
          >
            <img :src="headpicture" alt="默认头像" />
          </el-avatar>
          <div class="user-details">
            <div class="user-name-section">
              <span class="user-name">{{ vuex_user_realName | name }}</span>
              <el-tag type="success" size="mini" class="verified-tag">已实名</el-tag>
            </div>
            <div class="user-phone">{{ vuex_user_mobile | phone }}</div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-number">{{ recordTimes }}</div>
                <div class="stat-label">借阅次数</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-number">{{ overTimes }}</div>
                <div class="stat-label">逾期次数</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 借书记录 -->
    <el-card class="records-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">我的借书记录</span>
        <el-button
          type="text"
          icon="el-icon-refresh"
          @click="onRecordsRefresh"
          :loading="recordsRefreshing"
        >
          刷新
        </el-button>
      </div>

      <!-- 记录列表 -->
      <div v-loading="recordsRefreshing" class="records-list">
        <div v-if="records.length" class="record-items">
          <el-row :gutter="20">
            <el-col
              v-for="item in records"
              :key="item.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              class="record-col"
            >
              <el-card class="record-item" shadow="hover">
                <div class="book-cover-wrapper">
                  <el-image
                    class="book-cover"
                    :src="item.newsImg || bookImg"
                    fit="cover"
                    :preview-src-list="item.newsImg ? [item.newsImg] : []"
                  >
                    <div slot="placeholder" class="image-slot">
                      <i class="el-icon-loading"></i>
                    </div>
                    <div slot="error" class="image-slot">
                      <img :src="bookImg" alt="默认封面" />
                    </div>
                  </el-image>
                </div>

                <div class="book-info">
                  <div class="book-title-section">
                    <h4 class="book-title">{{ item.volBookName || item.bookName }}</h4>
                    <el-tag
                      :type="getStatusType(item.recordStatus)"
                      size="mini"
                      class="status-tag"
                    >
                      {{ item.recordStatus | recordStatus }}
                    </el-tag>
                  </div>

                  <div class="book-details">
                    <div v-if="item.realName" class="detail-item">
                      <span class="label">借书人：</span>
                      <span class="value">{{ item.realName | name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">借阅时间：</span>
                      <span class="value">{{ item.startTime }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">计划归还：</span>
                      <span class="value">{{ item.planEndTime }}</span>
                    </div>
                    <div v-if="item.recordStatus == 4" class="detail-item">
                      <span class="label">实际归还：</span>
                      <span class="value">{{ item.endTime }}</span>
                    </div>
                  </div>

                  <div v-if="item.recordStatus == 1" class="action-section">
                    <el-button
                      type="primary"
                      size="small"
                      @click="returningBook(item.id)"
                      class="return-btn"
                    >
                      还书
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 空状态 -->
        <el-empty v-else description="暂无借书记录" :image-size="120">
          <el-button type="primary" @click="$router.push('/568/mobile-library')">
            去借书
          </el-button>
        </el-empty>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getMyBook, returningBook} from '@/api/book'
import { getAvatar} from '@/api/index'

export default {
  name: 'MineBorrow',
  filters: {
    recordStatus(status) {
      const names = {0: '借书审核中', 1: '借书审核通过', 2: '借书拒绝', 3: '还书审核中', 4: '还书审核通过'}
      return names[status] || ''
    },
    // 姓名脱敏
    name(value) {
      if (!value) return ''
      if (value.length <= 2) return value
      return value.charAt(0) + '*'.repeat(value.length - 2) + value.charAt(value.length - 1)
    },
    // 手机号脱敏
    phone(value) {
      if (!value) return ''
      return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
  },
  data() {
    return {
      // 头像
      headpicture: require('@/assets/images/avatar.png'),
      profile: '',
      // 借书记录
      records: [],
      // 借阅次数
      recordTimes: 0,
      // 逾期次数
      overTimes: 0,
      // 刷新状态
      recordsRefreshing: false,
      // 默认书籍封面
      bookImg: require('@/assets/images/book.png')
    }
  },
  computed: {
    // 用户信息相关computed属性
    vuex_user_realName() {
      return this.$store.getters.info?.realName || ''
    },
    vuex_user_mobile() {
      return this.$store.getters.info?.mobile || ''
    },
    vuex_user_vol_id() {
      return this.$store.getters.info?.id || ''
    },
    vuex_uiStyle() {
      // 适老化模式，这里暂时返回默认值
      return 'normal'
    }
  },
  created() {
    this.loadAvatar()
    this.loadMyBookLists()
    this.$bus.$off('reloadMy')
    this.$bus.$on('reloadMy', () => {
      this.onRecordsRefresh()
    })
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.back()
    },

    // 获取头像
    async loadAvatar() {
      try {
        const {data} = await getAvatar(this.vuex_user_vol_id)
        let reg = /^\/.*/
        this.profile = data.profile && reg.test(data.profile) ?
          `${process.env.VUE_APP_BASE_API}${data.profile}` :
          data.headpicture ? data.headpicture : this.headpicture
      } catch (error) {
        console.error('获取头像失败:', error)
        this.profile = this.headpicture
      }
    },

    // 计算逾期次数
    getOverTimes() {
      this.overTimes = 0
      this.records.forEach(v => {
        const time = new Date() - new Date(v.planEndTime)
        if (v.recordStatus == 1 && time > 0) {
          this.overTimes++
        }
      })
    },

    // 获取状态类型（用于el-tag）
    getStatusType(status) {
      const types = {
        0: 'warning',  // 借书审核中
        1: 'success',  // 借书审核通过
        2: 'danger',   // 借书拒绝
        3: 'warning',  // 还书审核中
        4: 'success'   // 还书审核通过
      }
      return types[status] || 'info'
    },

    // 获取我的借书列表
    async loadMyBookLists() {
      this.recordsRefreshing = true
      try {
        const {data} = await getMyBook()

        // 处理图片
        const listData = data.records.map(item => {
          if (item.imgs && item.imgs[0] && item.imgs[0].filePath) {
            item.newsImg = `${process.env.VUE_APP_BASE_API}${item.imgs[0].filePath}`
          }
          return item
        })

        this.records = listData
        this.recordTimes = this.records.length
        this.getOverTimes()

        if (this.recordsRefreshing) {
          this.$message.success('刷新成功')
        }
      } catch (error) {
        console.error('获取借书记录失败:', error)
        this.$message.error('网络异常，请重试！')
      } finally {
        this.recordsRefreshing = false
      }
    },

    // 刷新记录
    onRecordsRefresh() {
      this.loadMyBookLists()
    },
    // 点击还书
    returningBook(id) {
      this.$confirm('是否确定将书归还？', '还书提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          const data = await returningBook({
            recordId: id
          })

          if (data.code != 200) {
            this.$message.error(data.msg || '还书失败')
            return
          }

          this.$message.success('还书申请提交成功！')
          this.loadMyBookLists()
          this.$bus.$emit('reLoadApprove')
          this.$bus.$emit('reloadHistory')
        } catch (error) {
          console.error('还书失败:', error)
          this.$message.error('还书失败，请重试')
        }
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  max-width: 1200px;
  margin: 20px auto 0;
  min-height: calc(100vh - 50px);
}

// 页面头部样式
::v-deep .el-page-header {
  padding: 0;
  margin-bottom: 20px;

  .el-page-header__content {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .el-page-header__left {
    .el-page-header__back {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 用户信息卡片
.user-info-card {
  margin-bottom: 20px;

  .user-info-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    .user-basic-info {
      display: flex;
      align-items: center;
      gap: 20px;

      .user-avatar {
        flex-shrink: 0;
      }

      .user-details {
        .user-name-section {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;

          .user-name {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
          }

          .verified-tag {
            font-size: 12px;
          }
        }

        .user-phone {
          font-size: 14px;
          color: #606266;
        }
      }
    }

    .stats-section {
      min-width: 200px;

      .stat-item {
        text-align: center;
        padding: 10px;

        .stat-number {
          font-size: 28px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}
// 借书记录卡片
.records-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .records-list {
    min-height: 200px;

    .record-items {
      .record-col {
        margin-bottom: 20px;
      }

      .record-item {
        height: 100%;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .book-cover-wrapper {
          text-align: center;
          margin-bottom: 15px;

          .book-cover {
            width: 100%;
            height: 180px;
            border-radius: 8px;
            object-fit: cover;
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 180px;
            background: #f5f7fa;
            color: #909399;
            font-size: 14px;
            border-radius: 8px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 8px;
            }
          }
        }

        .book-info {
          .book-title-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            gap: 8px;

            .book-title {
              font-size: 16px;
              font-weight: 600;
              color: #303133;
              margin: 0;
              line-height: 1.4;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .status-tag {
              flex-shrink: 0;
            }
          }

          .book-details {
            margin-bottom: 15px;

            .detail-item {
              display: flex;
              margin-bottom: 6px;
              font-size: 13px;

              .label {
                color: #909399;
                min-width: 70px;
                flex-shrink: 0;
              }

              .value {
                color: #606266;
                flex: 1;
                word-break: break-all;
              }
            }
          }

          .action-section {
            text-align: center;

            .return-btn {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .user-info-content {
    flex-direction: column;
    align-items: flex-start !important;

    .user-basic-info {
      width: 100%;
      margin-bottom: 15px;
    }

    .stats-section {
      width: 100%;
      min-width: auto;
    }
  }

  .record-item {
    .book-info {
      .book-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .status-tag {
          align-self: flex-start;
        }
      }
    }
  }
}
</style>
