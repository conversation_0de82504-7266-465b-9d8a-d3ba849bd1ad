<template>
  <div class="add-serve-form">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="serve-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务者名称" prop="volUserName">
            <el-input
              v-model="form.volUserName"
              placeholder="请输入服务者名称"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务时间" prop="happenTime">
            <el-date-picker
              v-model="form.happenTime"
              type="datetime"
              placeholder="选择服务时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
              :disabled="disable"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务类型" prop="serveType">
            <el-select
              v-model="form.serveType"
              placeholder="请选择服务类型"
              style="width: 100%"
              :disabled="disable"
            >
              <el-option
                v-for="item in serveTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务概述" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入服务概述"
              :disabled="disable"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="服务内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入详细服务描述..."
          maxlength="500"
          show-word-limit
          :disabled="disable"
        />
      </el-form-item>

      <!-- 图片上传 -->
      <el-form-item label="服务图片">
        <el-upload
          ref="upload"
          :file-list="fileList"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          :disabled="disable"
          list-type="picture-card"
          :limit="9"
          accept="image/*"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">
            只能上传jpg/png文件，且不超过2MB，最多9张图片
          </div>
        </el-upload>
      </el-form-item>

      <!-- 审批进度步骤条 -->
      <el-form-item v-if="businessId" label="审批进度">
        <Step :vol-approve="volApprove" />
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? "更新" : "提交" }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { addNews, getNews, updateNews } from "@/api/568/home";
import { uploadPic, downloadFile, getCase } from "@/api/568/index";
import dayjs from "dayjs";
import Step from "@/components/step";
import { getToken } from "@/utils/auth";

export default {
  name: "AddServeForm",
  components: {
    Step,
  },
  props: {
    formData: {
      type: Object,
      default: null,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    editId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      // 表单数据
      form: {
        volUserName: "",
        happenTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        serveType: "",
        title: "",
        content: "",
      },
      // 表单验证规则
      rules: {
        volUserName: [
          { required: true, message: "请输入服务者名称", trigger: "blur" },
        ],
        happenTime: [
          { required: true, message: "请选择服务时间", trigger: "change" },
        ],
        serveType: [
          { required: true, message: "请选择服务类型", trigger: "change" },
        ],
        title: [{ required: true, message: "请输入服务概述", trigger: "blur" }],
      },
      // 服务类型选项
      serveTypeOptions: [],
      // 图片文件列表
      fileList: [],
      // 上传相关
      uploadAction: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken(),
      },
      uploadData: {},
      // 控制状态
      disable: false,
      submitLoading: false,
      // 编辑相关
      id: "",
      businessId: "",
      volApprove: {},
      dataId: 0,
      deleteIds: [],
    };
  },
  computed: {},
  watch: {
    formData: {
      async handler(newVal) {
        if (newVal) {
          await this.initFormData();
        }
      },
      immediate: true,
    },
    editId: {
      async handler(newVal) {
        if (newVal && this.isEdit) {
          await this.initFormData();
        }
      },
      immediate: true,
    },
  },
  async created() {
    // 加载服务类型字典
    await this.loadServeTypes();

    // 初始化表单数据
    await this.initFormData();
  },
  methods: {
    // 加载服务类型字典
    async loadServeTypes() {
      try {
        const { data } = await getCase("vol_appointment_type");
        this.serveTypeOptions = data.map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      } catch (error) {
        console.error("加载服务类型失败:", error);
      }
    },

    // 初始化表单数据
    async initFormData() {
      if (this.isEdit && this.formData) {
        // 编辑模式，获取详情数据
        if (this.formData.id) {
          // 通过 editId 调用 getNews 获取详情
          await this.loadNewsDetail(this.formData.id);
        }
      } else {
        // 新建模式，设置默认值
        this.form.volUserName = this.aseName(this.$store.getters.name) || "";
        this.disable = false;
      }
    },

    // 加载新闻详情
    async loadNewsDetail(id) {
      try {
        const { data } = await getNews(id);
        if (data) {
          this.fillFormData(data);
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$message.error("获取详情失败");
      }
    },

    // 填充表单数据
    fillFormData(data) {
      this.form = {
        volUserName: this.aseName(data.volUserName || ""),
        happenTime: data.happenTime || "",
        serveType: data.serveType || "",
        title: data.title || "",
        content: data.content || "",
      };
      this.id = data.id;
      this.businessId = data.businessId;
      this.volApprove = data.volApprove || {};
      this.disable = true;

      // 加载图片
      if (this.id) {
        this.downloadFiles();
      }
    },

    // 上传前检查
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传图片只能是 JPG/PNG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },

    // 上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.$message.success("图片上传成功");
      } else {
        this.$message.error("图片上传失败");
      }
    },

    // 上传失败
    handleUploadError(err, file, fileList) {
      this.$message.error("图片上传失败");
    },

    // 移除图片
    handleRemove(file, fileList) {
      // 如果是已存在的图片，记录删除ID
      if (file.response && file.response.data && file.response.data.fileId) {
        this.deleteIds.push(file.response.data.fileId);
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return false;
        }

        try {
          this.submitLoading = true;

          const params = {
            ...this.form,
            serveType: this.form.serveType,
            volUserId: this.$store.getters.userId,
            volUserName: this.$store.getters.name,
            status: 2, // 审批中
            type: 1, // 服务记录
          };

          let result;
          if (this.isEdit && this.id) {
            // 编辑模式
            params.id = this.id;
            result = await updateNews(params);
          } else {
            // 新建模式
            result = await addNews(params);
          }

          if (result.code === 200) {
            this.$emit("success");
            this.$message.success(this.isEdit ? "更新成功！" : "提交成功！");
          } else {
            this.$message.error(result.msg || "操作失败");
          }
        } catch (error) {
          console.error("提交失败:", error);
          this.$message.error("网络异常，请重试！");
        } finally {
          this.submitLoading = false;
        }
      });
    },

    // 取消
    handleCancel() {
      this.$emit("cancel");
    },

    // 获取图片
    async downloadFiles() {
      try {
        const data = await downloadFile({
          businessId: this.id,
          tableName: "vol_news",
        });

        if (data && data.code === 200 && data.rows) {
          this.fileList = data.rows.map((item) => ({
            name: item.fileName,
            url: `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`,
            fileId: item.fileId,
          }));
        }
      } catch (error) {
        console.error("获取图片失败:", error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.add-serve-form {
  .serve-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .form-actions {
      margin-top: 30px;
      text-align: center;

      .el-button {
        margin: 0 10px;
        padding: 12px 30px;
      }
    }
  }

  .el-upload {
    .el-upload__tip {
      margin-top: 10px;
      color: #909399;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .add-serve-form {
    .el-row {
      .el-col {
        margin-bottom: 15px;
      }
    }

    .serve-form {
      .el-form-item {
        margin-bottom: 15px;
      }

      .form-actions {
        .el-button {
          width: 100%;
          margin: 5px 0;
        }
      }
    }
  }
}
</style>
