<template>
  <div>
    <div class="serve_item" @click="toUpdate">
      <div class="serve_title_row">
        <div class="serve_title">
          <svg-icon class="clock_icon" icon-class="clock" />
          <div class="serve_title_content">{{ serveItem.serveTypeName }}服务</div>
        </div>
        <div class="time">{{ serveItem.happenTime }}</div>
      </div>
      <div class="serve_container">
        <div class="user_info">
          <div class="user_name">
            <van-icon class-prefix="icon" name="head" class="head_icon" size="13px" />
            <div>服务者：{{ serveItem.volUserName | name }}</div>
          </div>
          <!-- <div class="tel">
            <van-icon class-prefix="icon" name="tel" class="tel_icon" size=" 13px" />
            <div>15520809317</div>
          </div> -->
        </div>
        <div class="serve_content">{{ serveItem.content }}</div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'ServeItem',
  components: {

  },
  props: {
    serveItem: {
      type: Object,
      required: true
    }
    // taskId: {
    //   type: [Number, String],
    //   require: true
    // }
  },
  data() {
    return {

    }
  },
  computed: {},
  watch: {
  },
  created() {

  },
  methods: {
    toUpdate() {
      // this.$bus.$emit('transmitServeItem', this.serveItem)
      this.$router.push({name: 'AddServe', query: { id: this.serveItem.id}})
    }
  }
}
</script>

<style lang="scss" scoped>
  .serve_item {
    width: calc(100% - 30px);
    // height: 110px;
    background: #fff;
    box-shadow: 0 1px 5px 0 rgba(46, 92, 167, 0.1);
    border-radius: 6px;
    margin: 10px 15px 10px;
    padding: 0 10px 15px;
    .serve_title_row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #e0e0e0;
      // background: #666;
      .clock_icon {
        margin-right: 12px;
        width: 24px;
        height: 24px;
      }
      .serve_title {
        display: flex;
        align-items: flex-end;
        padding: 8px 0;
        font-size: 16px;
        color: #333;
        .serve_title_content {
          width: 35vw;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .time {
        font-size: 12px;
        color: #808080;
        text-align: right;
      }
    }
    .serve_container {
      margin-top: 15px;
      .user_info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        color: #333;
        .user_name {
          display: flex;
          align-items: flex-end;
          .head_icon {
            color: #bbc3cd;
            margin-right: 5px;
            margin-bottom: 2px;
          }
        }
        .tel {
          display: flex;
          align-items: flex-end;
          .tel_icon {
            color: #bbc3cd;
            margin-right: 5px;
            margin-bottom: 2px;
          }
        }
      }
      .serve_content {
        margin-top: 8px;
        font-size: 13px;
        color: #666;
      }
    }
  }
</style>

