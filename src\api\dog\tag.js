import request from '@/utils/request'

export function deleteTag(data) {
  return request({
    url: '/brandApply/delete',
    method: 'post',
    data: data,
  })
}

/**
 * 新增/编辑
 */
export function saveOrUpdateBrandApply(data) {
  return request({
    url: '/brandApply/saveOrUpdate',
    method: 'post',
    data: data,
  })
}

/**
 *  确认领取
 */
export function confirmTake(data) {
  return request({
    url: '/brandApply/updateByEntity',
    method: 'post',
    data: data,
  })
}
