<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="filters" size="small">
      <el-form-item label="医院名称">
        <el-input v-model="filters.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="filters.status" clearable placeholder="请选择">
          <el-option
            v-for="item in statuss"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属地区">
        <el-select v-model="filters.county" clearable placeholder="请选择">
          <el-option
            v-for="item in areaOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" icon="el-icon-search" @click="getPageList">
            查询
          </el-button>
          <el-button icon="el-icon-refresh-right" @click="resetParameter">
            重置
          </el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button
        v-if="user.userType !== 2"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
      >
        新增
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="data"
      stripe
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55"></el-table-column> -->
      <el-table-column label="序号" align="center" type="index" width="60px" />
      <el-table-column prop="name" label="医院名称"></el-table-column>
      <el-table-column prop="area" label="所属地区">
        <template slot-scope="scope">
          <!-- areaOptions -->
          {{ deptList.find((item) => item.id === scope.row.county)?.deptName }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="详细地址"></el-table-column>
      <el-table-column prop="person" label="负责人"></el-table-column>
      <el-table-column prop="tel" label="联系方式"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          {{ status[scope.row.status] }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="immuneStatus" label="免疫注销">
        <template slot-scope="scope">
          {{ immuneStatus[scope.row.immuneStatus] }}
        </template>
      </el-table-column>
      <el-table-column prop="licenseStatus" label="犬牌注销">
        <template slot-scope="scope">
          {{ licenseStatus[scope.row.licenseStatus] }}
        </template>
      </el-table-column> -->
      <el-table-column prop="createDate" label="申请时间"></el-table-column>
      <el-table-column label="操作" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button
            v-if="scope.row.status == 3 && user.userType === 3"
            type="text"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="scope.row.status == 1 && user.userType === 3"
            type="text"
            @click="handleReview(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-if="scope.row.status == 2"
            type="text"
            @click="handleVaccine(scope.row)"
          >
            疫苗管理
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="getPageList"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleDialogClose"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="['view', 'review'].includes(dialogType) ? null : rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医院名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入医院名称"
                :disabled="isFieldDisabled('name')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.id" label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择" disabled>
                <el-option label="待审核" value="1"></el-option>
                <el-option label="已通过" value="2"></el-option>
                <el-option label="不通过" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="person">
              <el-input
                v-model="form.person"
                placeholder="请输入负责人"
                :disabled="isFieldDisabled('person')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="tel">
              <el-input
                v-model="form.tel"
                placeholder="请输入联系电话"
                :disabled="isFieldDisabled('tel')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传区域 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="门头照片" prop="doorway">
              <single-image-upload
                v-model="form.doorway"
                :file-name="form.doorwayName"
                :disabled="isFieldDisabled('doorway')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="营业执照" prop="business">
              <single-image-upload
                v-model="form.business"
                :file-name="form.businessName"
                :disabled="isFieldDisabled('business')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人身份证正面" prop="idupper">
              <single-image-upload
                v-model="form.idupper"
                :file-name="form.idupperName"
                :disabled="isFieldDisabled('idupper')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人身份证反面" prop="idlower">
              <single-image-upload
                v-model="form.idlower"
                :file-name="form.idlowerName"
                :disabled="isFieldDisabled('idlower')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资格证书" prop="qualifiCer">
              <single-image-upload
                v-model="form.qualifiCer"
                :file-name="form.qualifiCerName"
                :disabled="isFieldDisabled('qualifiCer')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医疗资质" prop="qualifi">
              <el-checkbox
                v-model="checkList[0]"
                true-label="1"
                :checked="checkList[0] == 1 ? true : false"
                false-label="0"
                :disabled="isFieldDisabled('qualifi')"
              >
                犬牌发放
              </el-checkbox>
              <el-checkbox
                v-model="checkList[1]"
                true-label="1"
                :checked="checkList[1] == 1 ? true : false"
                false-label="0"
                :disabled="isFieldDisabled('qualifi')"
              >
                免疫资格
              </el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="统一信用代码" prop="unifiedCode">
              <el-input
                v-model="form.unifiedCode"
                placeholder="请输入统一社会信用代码"
                :disabled="isFieldDisabled('unifiedCode')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="所属地区" prop="county">
              <div class="area-select">
                <span>浙江省/金华市/</span>
                <el-select
                  v-model="form.county"
                  placeholder="请选择"
                  :disabled="isFieldDisabled('county')"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="疫苗品牌" prop="vaccineList">
              <el-select
                v-model="form.vaccineList"
                multiple
                placeholder="请选择疫苗品牌"
                style="width: 100%"
                :disabled="isFieldDisabled('vaccineList')"
              >
                <el-option
                  v-for="item in vaccineOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="form.address"
                placeholder="请输入详细地址"
                :disabled="isFieldDisabled('address')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="dialogType === 'view' && form.reason">
          <el-col :span="24">
            <el-form-item label="审批意见" prop="reason">
              <el-input
                v-model="form.reason"
                placeholder="请输入审批意见"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button
          v-if="['edit', 'add'].includes(dialogType)"
          type="primary"
          @click="handleSubmit"
        >
          确 定
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="danger"
          @click="handleReviewSubmit('no')"
        >
          不通过
        </el-button>
        <el-button
          v-if="dialogType === 'review'"
          type="primary"
          @click="handleReviewSubmit('pass')"
        >
          通 过
        </el-button>
        <el-button
          v-if="dialogType === 'vaccine'"
          type="primary"
          @click="handleVaccineSubmit"
        >
          保 存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.toolbar {
  margin-bottom: 20px;
}

.area-select {
  display: flex;
  align-items: center;
}

.area-select span {
  margin-right: 10px;
}

.upload-demo {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
}

.form-line {
  margin-bottom: 20px;
}
</style>

<script>
import { getToken } from '@/utils/auth'
import { base, doGet, doPost } from '@/api/dog/index'
import SingleImageUpload from '@/components/Upload/SingleImageUpload.vue'
import { saveOrUpdate, updateStatus } from '@/api/dog/hospital'
export default {
  name: 'HospitalQualification',
  components: {
    SingleImageUpload,
  },
  data() {
    return {
      // 搜索条件
      filters: {
        name: undefined,
        status: undefined,
        area: undefined,
      },
      // 状态选项
      statuss: [
        { label: '待审核', value: '1' },
        { label: '已通过', value: '2' },
        { label: '不通过', value: '3' },
      ],
      // 地区选项
      options: [],
      cateProps: {
        value: 'code',
        label: 'name',
        children: 'children',
      },
      // 表格数据
      loading: false,
      data: [],
      selectedRows: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 弹窗
      dialogVisible: false,
      dialogTitle: '',
      // 表单
      form: {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        county: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
        vaccineList: [],
      },
      // 表单校验规则
      rules: {
        name: [{ required: true, message: '请输入医院名称', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        person: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        doorway: [
          { required: true, message: '请上传门头照片', trigger: 'change' },
        ],
        business: [
          { required: true, message: '请上传营业执照', trigger: 'change' },
        ],
        idupper: [
          { required: true, message: '请上传身份证正面', trigger: 'change' },
        ],
        idlower: [
          { required: true, message: '请上传身份证反面', trigger: 'change' },
        ],
        qualifiCer: [
          { required: true, message: '请上传资格证书', trigger: 'change' },
        ],
        qualifi: [
          {
            validator: (rule, value, callback) => {
              if (this.checkList[0] === '0' && this.checkList[1] === '0') {
                return callback(new Error('请至少选择一项医疗资质'))
              }
              callback()
            },
            trigger: 'change',
          },
        ],
        unifiedCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur',
          },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                return callback()
              }
              // 去除前后空格
              const trimmedValue = value.trim()
              // 更新表单中的值，去除空格
              this.form.unifiedCode = trimmedValue
              const pattern =
                /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
              if (!pattern.test(trimmedValue)) {
                return callback(new Error('请输入正确的统一社会信用代码'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        area: [
          { required: true, message: '请选择所属地区', trigger: 'change' },
        ],
        county: [
          { required: true, message: '请选择所属地区', trigger: 'change' },
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
        ],
      },
      // 上传相关
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      // 区域选项
      areaOptions: [
        { label: '婺城区', value: '330702' },
        { label: '金东区', value: '330703' },
        { label: '武义县', value: '330723' },
        { label: '浦江县', value: '330726' },
        { label: '磐安县', value: '330727' },
        { label: '兰溪市', value: '330781' },
        { label: '义乌市', value: '330782' },
        { label: '东阳市', value: '330783' },
        { label: '永康市', value: '330784' },
      ],
      // 免疫注销状态（1：待审核 2：通过 3：未通过）
      immuneStatus: {
        1: '待审核',
        2: '通过',
        3: '未通过',
      },
      // 犬牌注销状态（1：待审核 2：通过 3：未通过）
      licenseStatus: {
        1: '待审核',
        2: '通过',
        3: '未通过',
      },
      // 状态 1:审核中，2:已通过，3:未通过
      status: {
        1: '待审核',
        2: '已通过',
        3: '不通过',
      },
      // 弹窗类型 1:新增 2:编辑 3:查看 4:审核
      dialogType: '',
      deptList: [],
      // 疫苗选项
      vaccineOptions: [],
      // 医疗资质复选框
      checkList: ['0', '0'], // 0: 未选择, 1: 选择
    }
  },
  computed: {
    user() {
      return JSON.parse(sessionStorage.getItem('user')) || {}
    },
  },
  created() {
    // 从 URL 获取 title 参数
    const title = this.$route.query.title
    if (title) {
      this.filters.name = title
    }
    this.getdeptList().then(() => {
      this.getPageList()
    })
    this.getAllVaccine()
  },
  methods: {
    getAllVaccine() {
      return doGet('/hospital/getAllVaccine')
        .then((res) => {
          this.vaccineOptions = res || []
        })
        .catch((error) => {
          console.error('获取疫苗列表失败:', error)
          this.$message.error('获取疫苗列表失败')
        })
    },
    getdeptList() {
      return doPost('/dept/getAllList').then((res) => {
        console.log(res)
        this.deptList = res.filter((item) => item.level === 2)
      })
    },
    // 获取列表数据
    async getPageList() {
      this.loading = true
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          ...this.filters,
        }
        const res = await doGet('/hospital/getPageList', params)
        this.data = res.list
        this.total = res.total
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },

    // 重置搜索条件
    resetParameter() {
      this.filters = {
        name: undefined,
        status: undefined,
        area: undefined,
      }
      this.getPageList()
    },

    // 表格选择
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增医院资质'
      this.dialogType = 'add'
      this.dialogVisible = true
      this.form = {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        county: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
        vaccineList: [],
      }
      this.checkList = ['0', '0'] // 新增时重置复选框
    },

    // 获取医院详情并显示弹窗
    async getHospitalDetail(row, type) {
      try {
        const res = await doGet('/hospital/getById', { id: row.id })
        const dialogTitles = {
          edit: '编辑医院资质',
          view: '查看医院资质',
          review: '审核医院资质',
          vaccine: '疫苗品牌管理',
        }

        this.dialogTitle = dialogTitles[type]
        this.dialogType = type
        this.dialogVisible = true
        this.form = { ...res }

        this.form.vaccineList = this.form.vaccineList || []

        // 处理医疗资质数据
        if (this.form.qualifi) {
          this.checkList = this.form.qualifi.split(',')
        } else {
          this.checkList = ['0', '0']
        }

        // 查看和审核需要处理文件列表
        this.handleFileList(this.form, res.uploadFileList)
      } catch (error) {
        console.error('获取医院详情失败:', error)
        this.$message.error('获取医院详情失败')
      }
    },

    // 编辑
    handleEdit(row) {
      return this.getHospitalDetail(row, 'edit')
    },

    // 查看
    handleView(row) {
      return this.getHospitalDetail(row, 'view')
    },

    // 审核
    handleReview(row) {
      return this.getHospitalDetail(row, 'review')
    },

    // 批量处理文件
    handleFileList(resource, list) {
      list.forEach((item) => {
        const modelType = item.modelType
        resource[modelType] = item.fileUrl
        resource[modelType + 'Name'] = item.fileName
      })
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPageList()
    },

    // 上传成功回调
    handleDoorwaySuccess(res) {
      this.form.doorway = res.data.url
    },
    handleBusinessSuccess(res) {
      this.form.business = res.data.url
    },
    handleIdFrontSuccess(res) {
      this.form.idupper = res.data.url
    },
    handleIdBackSuccess(res) {
      this.form.idlower = res.data.url
    },
    handleQualificationSuccess(res) {
      this.form.qualifiCer = res.data.url
    },

    // 关闭弹窗
    handleDialogClose() {
      this.$refs.form?.resetFields()
      this.form = {
        name: '',
        status: '',
        person: '',
        tel: '',
        doorway: '',
        business: '',
        idupper: '',
        idlower: '',
        qualifiCer: '',
        unifiedCode: '',
        area: '',
        address: '',
        doorwayName: '',
        businessName: '',
        idupperName: '',
        idlowerName: '',
        qualifiCerName: '',
        county: '',
        vaccineList: [],
      }
      this.checkList = ['0', '0'] // 关闭时重置复选框
      this.dialogVisible = false
    },

    // 生成上传文件字符串
    genUploadFileStr() {
      const imgFields = [
        'doorway',
        'business',
        'idupper',
        'idlower',
        'qualifiCer',
      ]
      const fileConfigs = []
      imgFields.forEach((field) => {
        if (this.form[field]) {
          fileConfigs.push({
            fileUrl: this.form[field],
            fileName: this.form[field + 'Name'],
            modelType: field,
          })
        }
      })

      if (fileConfigs.length > 0) {
        return JSON.stringify(fileConfigs)
      }
      return ''
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        // 处理医疗资质数据
        this.form.qualifi = this.checkList.join(',')
        this.form.uploadFileStr = this.genUploadFileStr()

        await saveOrUpdate(this.form)

        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getPageList()
      } catch (error) {
        console.error(error)
      }
    },

    // 审核提交
    handleReviewSubmit(type) {
      console.log('handleReviewSubmit', this.form)
      if (type === 'pass') {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatus({
            lawStatus: 2,
            account: this.form.account,
            status: 2,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批通过')
            this.dialogVisible = false
            this.getPageList()
          })
        })
      } else {
        this.$prompt('请输入审批意见', '审批意见', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // 审批意见不能为空
          inputPattern: /^(?!\s*$).+/,
          inputErrorMessage: '审批意见不能为空',
        }).then(({ value }) => {
          updateStatus({
            lawStatus: 3,
            account: this.form.account,
            status: 3,
            node: '2',
            createName: this.user.realName,
            recordType: '7', // 审批意见
            id: this.form.id,
            reason: value,
            recordRemark: value,
          }).then(() => {
            this.$message.success('审批不通过')
            this.dialogVisible = false
            this.getPageList()
          })
        })
      }
    },

    // 疫苗管理
    handleVaccine(row) {
      return this.getHospitalDetail(row, 'vaccine')
    },

    // 疫苗管理提交
    async handleVaccineSubmit() {
      try {
        // 使用原有的编辑接口
        await saveOrUpdate(this.form)

        this.$message.success('疫苗品牌更新成功')
        this.dialogVisible = false
        this.getPageList()
      } catch (error) {
        console.error('更新疫苗品牌失败:', error)
        this.$message.error('更新疫苗品牌失败')
      }
    },

    // 判断字段是否应该禁用
    isFieldDisabled(fieldName) {
      // 查看和审核模式下所有字段都禁用
      if (['view', 'review'].includes(this.dialogType)) {
        return true
      }

      // 疫苗管理模式下，除了疫苗品牌外的所有字段都禁用
      if (this.dialogType === 'vaccine') {
        return fieldName !== 'vaccineList'
      }

      // 其他情况（编辑和新增模式）都不禁用
      return false
    },
  },
}
</script>
