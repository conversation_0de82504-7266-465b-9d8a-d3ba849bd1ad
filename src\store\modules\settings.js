import defaultSettings from "@/settings";
import { useDynamicTitle } from "@/utils/dynamicTitle";

const {
  sideTheme,
  showSettings,
  topNav,
  tagsView,
  tagsIcon,
  fixedHeader,
  sidebarLogo,
  dynamicTitle,
  footerVisible,
  footerContent,
} = defaultSettings;

const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";
const state = {
  title: "",
  theme: storageSetting.theme || "#409EFF",
  sideTheme: storageSetting.sideTheme || sideTheme,
  showSettings: showSettings,
  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
  tagsView:
    storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
  tagsIcon:
    storageSetting.tagsIcon === undefined ? tagsIcon : storageSetting.tagsIcon,
  fixedHeader:
    storageSetting.fixedHeader === undefined
      ? fixedHeader
      : storageSetting.fixedHeader,
  sidebarLogo:
    storageSetting.sidebarLogo === undefined
      ? sidebarLogo
      : storageSetting.sidebarLogo,
  dynamicTitle:
    storageSetting.dynamicTitle === undefined
      ? dynamicTitle
      : storageSetting.dynamicTitle,
  footerVisible:
    storageSetting.footerVisible === undefined
      ? footerVisible
      : storageSetting.footerVisible,
  footerContent: footerContent,
};
const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value;
    }
  },
};

const actions = {
  // 修改布局设置
  changeSetting({ commit }, data) {
    commit("CHANGE_SETTING", data);
  },
  // 设置网页标题
  setTitle({ commit }, title) {
    state.title = title;
    useDynamicTitle();
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
