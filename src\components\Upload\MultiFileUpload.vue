<template>
  <div class="multi-file-upload">
    <el-upload
      ref="upload"
      :headers="headers"
      class="file-uploader"
      :accept="accept"
      :show-file-list="true"
      :action="uploadUrl"
      :on-success="handleSuccess"
      :on-remove="handleRemove"
      :before-upload="beforeFileUpload"
      :file-list="fileList"
      :limit="limit"
      :on-exceed="handleExceed"
      multiple
      name="multipartFile"
      :on-preview="handlePreview"
    >
      <el-button size="small" type="primary">
        <i class="el-icon-upload"></i>
        <span>点击上传</span>
      </el-button>
      <div slot="tip" class="el-upload__tip">
        {{ tipText }}
      </div>
    </el-upload>
    <div v-if="isDisabled" class="file-list">
      <div
        v-for="(file, index) in fileList"
        :key="index"
        class="file-item"
        @click="handlePreview(file)"
      >
        <i :class="getFileIconClass(file)"></i>
        <span class="file-name">{{ getFileName(file) }}</span>
      </div>
    </div>
    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" append-to-body>
      <img width="100%" :src="previewImageUrl" alt="预览图片" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { getDownLoadUrl } from '@/api/dog/index'

export default {
  name: 'MultiFileUpload',

  inject: {
    elForm: {
      default: '',
    },
  },

  props: {
    value: {
      type: Array,
      default: () => [],
    },
    fileNames: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: '*', // 默认接受所有类型文件
    },
    limit: {
      type: Number,
      default: 10, // 默认最多上传10个文件
    },
    fileSize: {
      type: Number,
      default: 10, // 默认文件大小限制为10MB
    },
    tipText: {
      type: String,
      default: '单个文件不超过10MB',
    },
  },

  data() {
    return {
      headers: { Authorization: 'Bearer ' + getToken() },
      uploadUrl: process.env.VUE_APP_BASE_API + '/sysUploadFile/uploadFile',
      fileList: [],
      previewVisible: false,
      previewImageUrl: '',
    }
  },

  computed: {
    isDisabled() {
      return this.disabled
    },
  },

  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val && val.length > 0) {
          this.initFileList()
        } else {
          this.fileList = []
        }
      },
    },
  },

  methods: {
    initFileList() {
      this.fileList = this.value.map((path, index) => {
        const name = this.fileNames[index] || this.getFileNameFromPath(path)
        return {
          name: name,
          url: getDownLoadUrl(path),
          path: path,
        }
      })
    },

    getFileNameFromPath(path) {
      if (!path) return ''
      const parts = path.split('/')
      return parts[parts.length - 1]
    },

    getFileName(file) {
      return file.name || this.getFileNameFromPath(file.path || file.url)
    },

    getFileIconClass(file) {
      const fileName = this.getFileName(file)
      const extension = fileName.split('.').pop().toLowerCase()

      // 根据文件扩展名返回对应的图标类名
      const iconMap = {
        // 图片
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        bmp: 'el-icon-picture',
        // 文档
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        pdf: 'el-icon-document',
        txt: 'el-icon-document-checked',
        // 表格
        xls: 'el-icon-tickets',
        xlsx: 'el-icon-tickets',
        csv: 'el-icon-tickets',
        // 压缩包
        zip: 'el-icon-folder',
        rar: 'el-icon-folder',
        '7z': 'el-icon-folder',
        // 其他
        mp3: 'el-icon-headset',
        mp4: 'el-icon-film',
        avi: 'el-icon-film',
        mov: 'el-icon-film',
      }

      return iconMap[extension] || 'el-icon-document'
    },

    isImage(file) {
      const fileName = this.getFileName(file)
      const extension = fileName.split('.').pop().toLowerCase()
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      return imageExtensions.includes(extension)
    },

    handlePreview(file) {
      if (this.isImage(file)) {
        // 如果是图片，则预览
        this.previewImageUrl = file.url
        this.previewVisible = true
      } else {
        // 如果不是图片，则下载
        this.downloadFile(file)
      }
    },

    downloadFile(file) {
      const link = document.createElement('a')
      link.href = file.url
      link.download = this.getFileName(file)
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    handleSuccess(res, file) {
      if (res.code === 200) {
        const filePath = res.data
        const fileName = file.name

        // 更新内部fileList
        this.fileList.push({
          name: fileName,
          url: getDownLoadUrl(filePath),
          path: filePath,
        })

        // 提交更新后的文件路径数组和文件名数组
        const paths = this.fileList.map((file) => file.path)
        const names = this.fileList.map((file) => file.name)

        this.$emit('input', paths)
        this.$emit('update:fileNames', names)
        this.$emit('on-success', {
          fileName: fileName,
          filePath: filePath,
          url: getDownLoadUrl(filePath),
          fileList: this.fileList,
        })
      } else {
        this.$message.error(res.msg || '上传失败')
      }
    },

    handleRemove(file) {
      // 从fileList中移除文件
      const index = this.fileList.findIndex((item) => {
        return item.path === file.path || item.url === file.url
      })

      if (index !== -1) {
        this.fileList.splice(index, 1)

        // 更新value和fileNames
        const paths = this.fileList.map((file) => file.path)
        const names = this.fileList.map((file) => file.name)

        this.$emit('input', paths)
        this.$emit('update:fileNames', names)
        this.$emit('on-remove', file)
      }
    },

    beforeFileUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < this.fileSize

      if (!isLt10M) {
        this.$message.error(`上传文件大小不能超过 ${this.fileSize}MB!`)
      }

      return isLt10M
    },

    handleExceed() {
      this.$message.error(`最多只能上传 ${this.limit} 个文件!`)
    },

    // 提供给外部的方法：清空文件列表
    clearFiles() {
      this.fileList = []
      this.$emit('input', [])
      this.$emit('update:fileNames', [])
    },

    // 提供给外部的方法：手动上传
    submit() {
      this.$refs.upload.submit()
    },
  },
}
</script>

<style lang="scss" scoped>
.multi-file-upload {
  .file-uploader {
    :deep(.el-upload-list__item) {
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .el-upload-list__item-name {
        color: #606266;
        display: inline-block;
        max-width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }

      .el-icon-document,
      .el-icon-picture,
      .el-icon-tickets,
      .el-icon-folder,
      .el-icon-headset,
      .el-icon-film,
      .el-icon-document-checked {
        color: #909399;
        margin-right: 8px;
        font-size: 18px;
      }

      .file-name {
        color: #606266;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
