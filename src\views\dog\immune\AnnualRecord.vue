<template>
  <el-dialog
    title="年审记录"
    :visible.sync="outerVisible"
    width="70%"
    :close-on-click-modal="false"
    :before-close="cancelForm"
    @open="getList"
  >
    <!--        <el-col :span="24" class="form-line" style="padding-bottom: 0px;">-->
    <!--            <el-form size="small" :inline="true" :model="filters">-->
    <!--                <div style="float: right">-->
    <!--                    <el-form-item>-->
    <!--                        <el-button-group>-->
    <!--                            <el-button type="primary" icon="el-icon-plus" v-on:click="addImmnue">-->
    <!--                                新增-->
    <!--                            </el-button>-->
    <!--                        </el-button-group>-->
    <!--                    </el-form-item>-->
    <!--                </div>-->
    <!--            </el-form>-->
    <!--        </el-col>-->
    <!--列表-->
    <template>
      <el-table
        v-loading="loading"
        :data="data"
        highlight-current-row
        style="width: 100%"
        stripe
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="immuneCard"
          label="免疫证号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="hospital"
          label="免疫医院"
          sortable
        ></el-table-column>
        <el-table-column
          prop="doctor"
          label="注射医生"
          sortable
        ></el-table-column>
        <el-table-column
          :formatter="formatBrand"
          label="疫苗品牌"
          sortable
        ></el-table-column>
        <el-table-column
          prop="vaccineBatch"
          label="疫苗批次"
          sortable
        ></el-table-column>
        <el-table-column
          prop="injectionDate"
          label="注射日期"
          sortable
        ></el-table-column>
        <el-table-column
          prop="injectionEnddate"
          label="有效期至"
          sortable
        ></el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(1, scope.row.id)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!--工具条-->
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      width="50%"
      :title="title"
      :visible.sync="innerVisible"
      append-to-body
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <el-form ref="form" :model="form" label-width="150px" :rules="formRules">
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="免疫证" prop="immuneCard">
                <el-input
                  v-model="form.immuneCard"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col v-if="readonly" :span="12">
              <el-form-item label="注射医院" prop="hospitalId">
                <el-input
                  v-model="form.hospital"
                  :disabled="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注射医生" prop="doctor">
                <el-input
                  v-model="form.doctor"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="疫苗品牌" prop="vaccineBrand">
                <el-select
                  v-model="form.vaccineBrand"
                  :disabled="readonly"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="vaccineBrandChange"
                >
                  <el-option
                    v-for="item in vaccineBrandArray"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注射日期" prop="injectionDate">
                <el-date-picker
                  v-model="form.injectionDate"
                  style="width: 100%"
                  :readonly="readonly"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="到期日期" prop="injectionEnddate">
                <el-date-picker
                  v-model="form.injectionEnddate"
                  style="width: 100%"
                  :readonly="readonly"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="疫苗批次" prop="vaccineBatch">
                <el-input
                  v-model="form.vaccineBatch"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancel">取消</el-button>
        <el-button
          v-if="type == '2'"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="formSubmit('1')"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click.native="cancelForm">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
/* eslint-disable */
import { doPost } from '@/api/dog/index'

export default {
  props: {
    outerVisible: Boolean,
    petId: String,
  },
  data() {
    return {
      form: {
        id: '',
        hospitalId: '', // 注射医院ID
        petId: '', // 犬只ID
        hospital: '', // 注射医生
        doctor: '', // 注射医生
        vaccineBrand: '', // 疫苗品牌
        vaccineBrandOld: '', // 原来的疫苗品牌
        vaccineBatch: '', // 疫苗批次
        injectionDate: '', // 注射日期
        status: '1',
      },
      user: {},
      formRules: {},
      vaccineBrandArray: [],
      hospitalList: [],
      innerVisible: false,
      title: '',
      loading: false,
      formLoading: false,
      data: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      readonly: true,
      type: 1,
    }
  },
  methods: {
    resetParameter() {
      this.filters = {}
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPgaeList()
    },
    cancel() {
      this.innerVisible = false
      this.$refs['form'].resetFields()
      this.form.id = ''
    },
    cancelForm() {
      this.outerVisible = false
      this.$emit('callBack')
    },
    // 新增
    addImmnue: function () {
      this.title = '新增'
      this.innerVisible = true
      this.readonly = false
      // 日期自动选择
      const end = new Date() // 获取当前的日期
      end.setTime(end.getTime())
      // 计算，将当期日期-1天
      // 此时得到的是中国标准时间，格式不是yyyy-MM-dd，需要用dateFormat这个函数转换下
      this.form.injectionDate = this.dateFormat(end)
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 364)
      this.form.injectionEnddate = this.dateFormat(end)
    },

    dateFormat(dateData) {
      var date = new Date(dateData)
      var y = date.getFullYear()
      var m = date.getMonth() + 1
      m = m < 10 ? '0' + m : m
      var d = date.getDate()
      d = d < 10 ? '0' + d : d
      const time = y + '-' + m + '-' + d
      return time
    },
    // 编辑
    handleEdit: function (type, id) {
      this.type = type
      this.innerVisible = true
      if (type === 1) {
        this.title = '查看'
        this.readonly = true
      } else if (type === 2) {
        this.title = '编辑'
        this.readonly = false
      }
      doPost('/immuneRegister/getById', { id: id }).then((res) => {
        this.form = res
      })
    },

    // 提交
    formSubmit: function () {
      const para = this.form
      para.petId = this.petId
      console.log(para)
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm('确认保存吗', '提示', {
            confirmButtonText: '继续保存',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            doPost('/immuneRegister/saveOrUpdate', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: '保存成功',
                type: 'success',
              })
              this.cancel()
              this.getList()
            })
          })
        }
      })
    },
    // 获取列表（查询条件，欠缺调整）
    getList: function () {
      this.loading = true
      doPost('/immuneRegister/getPageList', {
        petId: this.petId,
        type: 2,
      }).then((res) => {
        this.data = res.list
        if (this.data !== null && this.data.length > 0) {
          for (var a in this.data) {
            this.data[a].index =
              (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
          }
        }
        this.total = res.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    getTypesData() {
      doPost('/vaccine/getAllList', {
        createBy: this.user.userType == '2' ? this.user.id : '',
      }).then((res) => {
        this.vaccineBrandArray = res
      })
    },
    formatBrand: function (row, column) {
      for (let i in this.vaccineBrandArray) {
        if (row.vaccineBrand === this.vaccineBrandArray[i].id) {
          return this.vaccineBrandArray[i].name
        }
      }
    },
    vaccineBrandChange: function (e) {
      let obj = {}
      obj = this.vaccineBrandArray.find((item) => {
        return item.id === e
      })
      // this.form.vaccineBatch = obj.batch// 疫苗批次
      // if (obj.batchSurplus == '0') {
      //     this.$message.error('此疫苗数量为0，不能选择!')
      //     this.form.vaccineBrand = '';
      //     this.form.vaccineBatch = '';
      //     return;
      // } else {
      //     this.form.vaccineBatch = obj.batch// 疫苗批次
      // }
    },
    initHeaders: function () {
      var headers = {}
      this.user = JSON.parse(sessionStorage.getItem('user'))
      if (this.user) {
        // console.log('用户令牌=' + this.user.reqToken)
        headers.reqToken = this.user.reqToken
      }
      return headers
    },
  },

  mounted() {
    this.headers = this.initHeaders()
    if (this.user.userType === 2) {
      this.$set(this.form, 'hospitalId', this.user.userQualifi.id) // 医院ID
      this.$set(this.form, 'hospital', this.user.userQualifi.name) // 医院ID
    }
    this.getTypesData()
  },
  watch: {
    outerVisible() {
      this.outerVisible = this.outerVisible
    },
  },
}
</script>

<style scoped></style>
