<template>
  <div class="news-detail">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="上报详情">
      <template slot="extra">
        <el-button @click="goBack">返回列表</el-button>
      </template>
    </el-page-header>

    <!-- 详情内容 -->
    <div class="detail-content" v-loading="isLoading">
      <div class="detail-main" v-if="detailData">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">问题分类：</label>
                <el-tag :type="getTypeTagType(getEventListText(detailData.eventdesc).title)">
                  {{ getEventListText(detailData.eventdesc).title }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">处理状态：</label>
                <el-tag :type="getStatusTagType(detailData.status)">
                  {{ getStatusText(detailData.status) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">归属区县：</label>
                <span class="info-value">{{ getDictText(areaOptions, detailData.areaid) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">归属街道：</label>
                <span class="info-value">{{ detailData.streetid || '暂无' }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="info-item">
            <label class="info-label">详细地址：</label>
            <span class="info-value">{{ detailData.address || '暂无' }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">问题描述：</label>
            <div class="info-value description-text">
              {{ getEventListText(detailData.eventdesc).desc || '暂无描述' }}
            </div>
          </div>
        </div>

        <!-- 现场照片 -->
        <div class="photo-section" v-if="photoList.length > 0">
          <h3 class="section-title">现场照片</h3>
          <div class="photo-gallery">
            <div
              class="photo-item"
              v-for="(photo, index) in photoList"
              :key="index"
              @click="previewImage(index)">
              <img :src="photo" :alt="`现场照片${index + 1}`" />
            </div>
          </div>
        </div>

        <!-- 结果反馈 -->
        <div class="feedback-section" v-if="detailData.feedback">
          <h3 class="section-title">结果反馈</h3>
          <div class="feedback-content">
            {{ detailData.feedback }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <el-button @click="goBack">返回列表</el-button>
          <el-button type="primary" @click="editReport" v-if="canEdit">编辑</el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!detailData && !isLoading">
        <el-empty description="未找到相关数据">
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { getProblemDetail } from "@/api/common";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "newsDetail",
  data() {
    return {
      isLoading: false,
      detailData: null,
      photoList: [],
      areaOptions: []
    }
  },
  computed: {
    canEdit() {
      // 只有上报状态的记录可以编辑
      return this.detailData && this.detailData.status === 1;
    }
  },
  mounted() {
    this.getDictList();
    this.getDetail();
  },
  methods: {
    getDictList() {
      // 区划
      getDicts('county').then((response) => {
        this.areaOptions = response.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
      });
    },

    // 获取详情
    async getDetail() {
      try {
        this.isLoading = true;

        // 实际API调用
        const res = await getProblemDetail(this.$route.query.id);
        if (res.code === 200 && res.data) {
          this.detailData = res.data;
          this.processPhotoList();
        } else {
          this.$message.error('获取详情失败');
        }

        this.isLoading = false;
      } catch (error) {
        console.error('获取详情失败:', error);
        this.$message.error('获取详情失败');
        this.isLoading = false;
      }
    },

    // 处理照片列表
    processPhotoList() {
      if (this.detailData && this.detailData.fileStr) {
        this.photoList = this.detailData.fileStr.split(',').filter(url => url.trim());
      }
    },

    getDictText(arr, value) {
      if (!arr || !value) return '暂无数据';
      const item = arr.find(item => item.value === value);
      return item ? item.label : '暂无数据';
    },

    getEventListText(str) {
      if (!str) return { title: '', desc: '' };
      const parts = str.split(":");
      return {
        title: parts[0] || '',
        desc: parts[1] || ''
      };
    },

    getStatusText(status) {
      const statusMap = {
        1: '上报',
        2: '立案',
        3: '派遣',
        4: '处置',
        5: '核查',
        6: '结案'
      };
      return statusMap[status] || '未知';
    },

    getStatusTagType(status) {
      const typeMap = {
        1: '',
        2: 'warning',
        3: 'warning',
        4: 'primary',
        5: 'primary',
        6: 'success'
      };
      return typeMap[status] || '';
    },

    getTypeTagType(type) {
      const typeMap = {
        '市容环境类': 'primary',
        '街面秩序类': 'success',
        '公共设施类': 'warning',
        '突发事件类': 'danger',
        '牛皮癣': 'info',
        '其他': ''
      };
      return typeMap[type] || '';
    },

    // 预览图片
    previewImage(index) {
      // 使用Element UI的图片预览功能
      const viewer = this.$imageViewer({
        images: this.photoList,
        index: index
      });
      viewer.show();
    },

    // 返回列表
    goBack() {
      this.$router.push('/newsHistory');
    },

    // 编辑上报
    editReport() {
      this.$router.push({
        path: '/BrokeTheNews',
        query: { id: this.detailData.id }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.news-detail {
  min-height: 100vh;
  background-color: #fff;
  padding: 20px;
}

// 页面头部
.el-page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e6e6e6;
}

// 详情内容
.detail-content {
  .detail-main {
    background: #fff;
    padding: 0;
  }
}

// 信息区域
.info-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
  }

  .info-item {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      flex: 0 0 auto;
      width: 120px;
      color: #666;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.5;
    }

    .info-value {
      flex: 1;
      color: #333;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-all;

      &.description-text {
        background: #fafafa;
        padding: 12px;
        border-radius: 4px;
        border-left: 3px solid #409eff;
        line-height: 1.6;
      }
    }
  }
}

// 照片区域
.photo-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
  }

  .photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;

    .photo-item {
      position: relative;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.3s ease;
      border: 1px solid #e6e6e6;

      &:hover {
        transform: scale(1.02);
        border-color: #409eff;
      }

      img {
        width: 100%;
        height: 150px;
        object-fit: cover;
        display: block;
      }
    }
  }
}

// 反馈区域
.feedback-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
  }

  .feedback-content {
    background: #fafafa;
    padding: 20px;
    border-radius: 4px;
    border-left: 3px solid #67c23a;
    font-size: 14px;
    color: #333;
    line-height: 1.6;
  }
}

// 操作按钮
.action-section {
  text-align: left;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;

  .el-button {
    margin-right: 15px;
    min-width: 120px;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 0;
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
    }
  }

  .detail-content {
    .detail-main {
      padding: 0;
    }
  }

  .info-section {
    .info-item {
      flex-direction: column;
      align-items: flex-start;

      .info-label {
        width: auto;
        margin-bottom: 5px;
      }
    }
  }

  .photo-section {
    .photo-gallery {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 10px;

      .photo-item {
        img {
          height: 120px;
        }
      }
    }
  }

  .action-section {
    text-align: center;

    .el-button {
      display: block;
      width: 100%;
      margin: 10px 0;
    }
  }
}

// 深度选择器样式调整
::v-deep .el-tag {
  border-radius: 4px;
}

::v-deep .el-row {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

::v-deep .el-loading-mask {
  border-radius: 4px;
}

::v-deep .el-button {
  border-radius: 4px;
}
</style>
