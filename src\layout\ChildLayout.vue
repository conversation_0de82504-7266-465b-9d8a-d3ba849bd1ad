<template>
  <div class="child-layoutLayout">
    <div
      v-if="barList.length > 1"
      class="child-layout-titleBar"
      style="overflow: hidden"
    >
      <nav class="nav-scroll p-4 bg-white w-full overflow-x-auto">
        <ul ref="navList" class="flex flex-nowrap gap-4 w-max">
          <li
            v-for="(item, i) in barList"
            :key="i"
            :ref="i === barActive ? 'activeItem' : null"
          >
            <div
              role="button"
              tabindex="0"
              :class="[
                'flex items-center gap-3 px-5 py-3 rounded-lg transition-all duration-200',
                'hover:bg-blue-50 cursor-pointer border',
                barActive === i
                  ? 'bg-blue-50 text-primary border-blue-200'
                  : 'bg-gray-50 text-gray-600 border-transparent',
              ]"
              @click="pageJump(item, i)"
              @keyup.enter="pageJump(item, i)"
            >
              <svg-icon
                v-if="item.meta.icon && item.meta.icon !== '#'"
                :icon-class="item.meta.icon"
                :class="[
                  '!w-8 !h-8',
                  barActive === i ? 'text-primary' : 'text-gray-400',
                ]"
              />
              <span class="text-[20px] py-[3px]">{{ item.meta.title }}</span>
            </div>
          </li>
        </ul>
      </nav>
    </div>
    <div class="child-layout-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import XEUtils from 'xe-utils'

export default {
  name: 'ChildLayout',
  data() {
    return {
      barActive: 0,
      barList: [],
      iconMap: {},
    }
  },
  watch: {
    $route: {
      handler(to) {
        // 重新获取和设置 barList
        const currentPath = to.path
        const currentRoute = this.$router
          .getRoutes()
          .find((item) => item.path === currentPath)
        const parentName = currentRoute?.parent?.name

        const parentRoute = XEUtils.findTree(
          this.routes,
          (item) => item.name === parentName
        )

        this.barList = parentRoute?.item?.children
          .map((item) => ({
            ...item,
            icon: item.meta?.icon || '',
          }))
          .filter((item) => !item.hidden)

        // 更新 barActive
        this.barActive = this.barList.findIndex((item) => item.name === to.name)

        // 添加滚动逻辑
        this.$nextTick(() => {
          const activeItem = this.$refs.activeItem?.[0]
          if (activeItem) {
            activeItem.scrollIntoView({
              behavior: 'smooth',
              block: 'nearest',
              inline: 'center',
            })
          }
        })
      },
      immediate: true,
    },
  },
  mounted() {
    // 可以删除原来的逻辑，因为 watch 中的 immediate: true 会在组件挂载时执行一次
  },
  methods: {
    pageJump(item, i) {
      this.barActive = i
      // 添加路由跳转
      this.$router.push(item.path)
    },
  },
  computed: {
    ...mapState({
      routes: (state) => state.permission.routes,
    }),
  },
}
</script>

<style scoped lang="scss">
.nav-scroll {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    transition: background-color 0.3s;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(193, 193, 193, 0.5);

      &:hover {
        background: rgba(193, 193, 193, 0.8);
      }
    }

    &::-webkit-scrollbar-track {
      background: rgba(241, 241, 241, 0.5);
    }
  }
}

.child-layoutLayout {
  background: #f2f4f7;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;

  .child-layout-titleBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    // height: 67px;
    padding: 12px 20px 0;

    .child-layout-titleBar-left {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 22px;
      color: #303133;
      text-align: left;
      font-style: normal;
    }
  }
}

.child-layout-content {
  width: 100%;
  padding: 20px;
}
.child-layout-content .app-container {
  background: #fff;
  padding: 20px;
  height: auto;
  // min-height: calc(100vh - 50px);
}
</style>
