<template>
  <div class="serve-detail">
    <div v-loading="loading" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-document"></i>
            服务记录详情
          </span>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务类型">
            <el-tag :type="getServeTypeTagType(detailData.serveType)">
              {{ detailData.serveTypeName }}服务
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务者">
            <div class="user-info">
              <i class="el-icon-user-solid"></i>
              {{ detailData.volUserName | name }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="服务时间">
            <div class="time-info">
              <i class="el-icon-time"></i>
              {{ detailData.happenTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            <div class="time-info">
              <i class="el-icon-date"></i>
              {{ detailData.createTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="服务概述" :span="2">
            {{ detailData.title || "无" }}
          </el-descriptions-item>
          <el-descriptions-item label="服务内容" :span="2">
            <div class="content-text">
              {{ detailData.content || "无" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 服务图片 -->
      <el-card v-if="imageList.length > 0" class="image-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-picture"></i>
            服务图片
          </span>
        </div>

        <div class="image-gallery">
          <el-image
            v-for="(image, index) in imageList"
            :key="index"
            :src="image.url"
            :preview-src-list="imageList.map((img) => img.url)"
            class="gallery-image"
            fit="cover"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
      </el-card>

      <!-- 审批进度 -->
      <el-card v-if="detailData.volApprove" class="approve-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-s-check"></i>
            审批进度
          </span>
        </div>

        <Step :vol-approve="detailData.volApprove" />
      </el-card>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="handleClose">关闭</el-button>
      <el-button v-if="canEdit" type="primary" @click="handleEdit">
        编辑
      </el-button>
    </div>
  </div>
</template>

<script>
import { getNews } from "@/api/568/home";
import { downloadFile } from "@/api/568/index";
import Step from "@/components/step";

export default {
  name: "ServeDetail",
  components: {
    Step,
  },
  filters: {
    name(value) {
      if (!value) return "";
      // 脱敏处理：显示姓氏和最后一个字，中间用*代替
      if (value.length <= 2) return value;
      return (
        value.charAt(0) +
        "*".repeat(value.length - 2) +
        value.charAt(value.length - 1)
      );
    },
  },
  props: {
    serveId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      detailData: {},
      imageList: [],
    };
  },
  computed: {
    canEdit() {
      // 根据业务逻辑判断是否可以编辑
      return this.detailData.volUserId === this.$store.getters.userId;
    },
  },
  created() {
    this.loadDetail();
    this.loadImages();
  },
  methods: {
    // 加载详情数据
    async loadDetail() {
      try {
        this.loading = true;
        const result = await getNews(this.serveId);

        if (result && result.code === 200) {
          this.detailData = result.data || {};
          this.detailData.volUserName = this.aseName(result.data.volUserName);
        } else {
          this.$message.error("获取详情失败");
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$message.error("网络异常，请重试！");
      } finally {
        this.loading = false;
      }
    },

    // 加载图片
    async loadImages() {
      try {
        const result = await downloadFile({
          businessId: this.serveId,
          tableName: "vol_news",
        });

        if (result && result.code === 200 && result.rows) {
          this.imageList = result.rows.map((item) => ({
            name: item.fileName,
            url: `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`,
            fileId: item.fileId,
          }));
        }
      } catch (error) {
        console.error("获取图片失败:", error);
      }
    },

    // 获取服务类型标签类型
    getServeTypeTagType(serveType) {
      const typeMap = {
        1: "primary",
        2: "success",
        3: "warning",
        4: "danger",
        5: "info",
      };
      return typeMap[serveType] || "primary";
    },

    // 关闭
    handleClose() {
      this.$emit("close");
    },

    // 编辑
    handleEdit() {
      this.$emit("edit", this.detailData);
    },
  },
};
</script>

<style lang="scss" scoped>
.serve-detail {
  .detail-content {
    overflow-y: auto;

    .info-card,
    .image-card,
    .approve-card {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;

      &:last-child {
        margin-bottom: 0;
      }

      .card-header {
        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          i {
            margin-right: 8px;
            color: #409eff;
          }
        }
      }
    }

    .user-info,
    .time-info {
      display: flex;
      align-items: center;

      i {
        margin-right: 5px;
        color: #909399;
      }
    }

    .content-text {
      line-height: 1.6;
      color: #606266;
      white-space: pre-wrap;
    }

    .image-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 15px;

      .gallery-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        cursor: pointer;
        border: 1px solid #ebeef5;

        &:hover {
          border-color: #409eff;
        }

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background: #f5f7fa;
          color: #909399;
          font-size: 30px;
        }
      }
    }
  }

  .action-buttons {
    margin-top: 20px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;

    .el-button {
      margin-left: 10px;
      padding: 10px 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .serve-detail {
    .detail-content {
      .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 10px;

        .gallery-image {
          width: 80px;
          height: 80px;
        }
      }
    }

    .action-buttons {
      text-align: center;

      .el-button {
        width: 100%;
        margin: 5px 0;
      }
    }
  }
}
</style>
