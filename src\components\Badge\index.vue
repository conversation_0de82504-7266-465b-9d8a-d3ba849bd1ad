<template>
  <el-dropdown class="badge-item" placement="bottom" @command="handleCommand">
    <span class="el-dropdown-link">
      <el-badge :hidden="undoNum === 0" :value="undoNum" :max="99">
        <i
          class="right-menu-item el-icon-bell"
          style="
            cursor: pointer;
            color: #303133;
            font-size: 18px;
            /* margin-top: 18px; */
          "
        />
      </el-badge>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="county">
        县市区待办：{{ countyNum }}
      </el-dropdown-item>
      <el-dropdown-item command="city">
        市本级待办：{{ cityNum }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import store from '@/store'

export default {
  data() {
    return {
      value: undefined,
      timerId: undefined,
    }
  },
  computed: {
    undoNum() {
      return this.$store.getters.undoNum
    },
    countyNum() {
      return this.$store.state.notice.countyNum
    },
    cityNum() {
      return this.$store.state.notice.cityNum
    },
  },
  created() {
    this.fetchStatistics()
    // this.timerId = setInterval(() => {
    //   this.fetchStatistics()
    // }, 30 * 1000)
  },
  beforeDestroy() {
    // clearInterval(this.timerId)
  },
  methods: {
    handleCommand(command) {
      if (command === 'county') {
        this.$router.push({
          path: '/industryApp/xzzfzhzx/direct/qxinstruction',
          query: {
            process: 6,
          },
        })
      } else if (command === 'city') {
        this.$router.push({
          path: '/industryApp/xzzfzhzx/direct/instruction',
          query: {
            process: 6,
          },
        })
      }
    },
    fetchStatistics() {
      // getStatistics().then((res) => {
      //   if (res && res.code === 200) {
      //     this.value = res.data
      //   }
      // })
      store.dispatch('UpdateUndo')
    },
    handleClick() {
      this.$router.push({
        path: '/industryApp/xzzfzhzx/direct/instruction',
        query: {
          process: 6,
        },
      })
    },
  },
}
</script>

<style>
.badge-item {
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.badge-item .el-badge__content.is-fixed {
  top: 10px;
}
</style>
