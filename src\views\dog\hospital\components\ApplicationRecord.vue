<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :inline="true" :model="filters" size="small">
      <el-form-item label="医院名称">
        <el-input v-model="filters.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="申请类型">
        <el-select v-model="filters.applyType" placeholder="请选择">
          <el-option label="代办资质申请" value="1"></el-option>
          <el-option label="年审" value="2"></el-option>
          <el-option label="犬牌领用" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-input v-model="filters.status" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" icon="el-icon-search" @click="getPageList">
            查询
          </el-button>
          <el-button icon="el-icon-refresh-right" @click="resetParameter">
            重置
          </el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="data" stripe>
      <el-table-column
        type="index"
        label="序号"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column prop="name" label="医院名称"></el-table-column>
      <el-table-column prop="applyType" label="申请类型">
        <template slot-scope="scope">
          <span>{{ applyTypeOption[scope.row.applyType] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="applyUser" label="申请人"></el-table-column>
      <el-table-column prop="mobile" label="联系方式"></el-table-column>
      <el-table-column prop="status" label="申请状态">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.applyType == '1' || scope.row.applyType == '2'"
            :type="getStatusType(scope.row.status)"
          >
            {{ statusOption[scope.row.status] }}
          </el-tag>
          <el-tag v-else :type="getTagStatusType(scope.row.status)">
            {{ tagStatusOption[scope.row.status] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="申请时间"></el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="getPageList"
    />

    <!-- 详情弹窗 -->
    <el-dialog title="申请详情" :visible.sync="dialogVisible" width="50%">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="医院名称">
          <span>{{ form.name }}</span>
        </el-form-item>
        <el-form-item label="申请类型">
          <span>{{ applyTypeOption[form.applyType] }}</span>
        </el-form-item>
        <el-form-item label="申请状态">
          <el-tag
            v-if="form.applyType == '1' || form.applyType == '2'"
            :type="getStatusType(form.status)"
          >
            {{ statusOption[form.status] }}
          </el-tag>
          <el-tag v-else :type="getTagStatusType(form.status)">
            {{ tagStatusOption[form.status] }}
          </el-tag>
        </el-form-item>
        <el-form-item label="申请时间">
          <span>{{ form.createDate }}</span>
        </el-form-item>
        <el-form-item label="申请人">
          <span>{{ form.applyUser }}</span>
        </el-form-item>
        <el-form-item label="联系方式">
          <span>{{ form.mobile }}</span>
        </el-form-item>
        <!-- <el-form-item label="所属地区">
          <span>{{ form.area }}</span>
        </el-form-item>
        <el-form-item label="详细地址">
          <span>{{ form.address }}</span>
        </el-form-item> -->
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { doGet } from '@/api/dog/index'

export default {
  name: 'ApplicationRecord',
  data() {
    return {
      // 搜索条件
      filters: {
        name: '',
        applyType: '1',
        status: '',
      },
      // 表格数据
      loading: false,
      data: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 弹窗
      dialogVisible: false,
      form: {},
      // 申请类型
      applyTypeOption: {
        1: '代办资质申请',
        2: '年审',
        3: '犬牌领用',
      },
      // 审核状态 资质和年审
      // 状态 1:审核中，2:已通过，3:未通过，4：已注销
      statusOption: {
        1: '审核中',
        2: '已通过',
        3: '未通过',
        4: '已注销',
      },
      // 审核状态 犬牌领用
      // 状态 1.待审核 2.拒绝  3.待提交制作 30.已提交制作 4.制作通过 5.制作驳回 6.制作中 7.运输中 8.待领取 9.已领取 10.已完结
      tagStatusOption: {
        1: '待审核',
        2: '拒绝',
        3: '待提交制作',
        30: '已提交制作',
        4: '制作通过',
        5: '制作驳回',
        6: '制作中',
        7: '运输中',
        8: '待领取',
        9: '已领取',
        10: '已完结',
      },
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    // 获取列表数据
    async getPageList() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          ...this.filters,
        }
        const res = await doGet('/hospital/record/getApplyList', params)
        this.data = res.list
        this.total = res.total
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },

    // 重置搜索条件
    resetParameter() {
      this.filters = {
        name: '',
        applyType: '1',
        status: '',
      }
      this.getPageList()
    },

    // 查看详情
    handleView(row) {
      this.form = { ...row }
      this.dialogVisible = true
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPageList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPageList()
    },

    // 获取资质和年审状态对应的tag类型
    getStatusType(status) {
      const typeMap = {
        1: 'warning', // 审核中
        2: 'success', // 已通过
        3: 'danger', // 未通过
        4: 'info', // 已注销
      }
      return typeMap[status] || ''
    },

    // 获取犬牌领用状态对应的tag类型
    getTagStatusType(status) {
      const typeMap = {
        1: 'warning', // 待审核
        2: 'danger', // 拒绝
        3: 'success', // 已提交制作
        4: 'warning', // 制作中
        5: 'danger', // 制作驳回
        6: 'info', // 运输中
      }
      return typeMap[status] || ''
    },
  },
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
