<template>
  <div class="app-container">
    <!--工具条-->
    <el-col :span="24" class="form-line" style="padding-bottom: 0px">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="身份证">
          <el-input
            v-model="filters.petIdCard"
            placeholder="请输入身份证信息"
          ></el-input>
        </el-form-item>
        <!--                    <el-form-item label="审核状态">-->
        <!--                        <el-select clearable v-model="filters.status" placeholder="请选择"-->
        <!--                                   style="width:100%">-->
        <!--                            <el-option-->
        <!--                                v-for="item in statuss"-->
        <!--                                :key="item.value"-->
        <!--                                :label="item.label"-->
        <!--                                :value="item.value">-->
        <!--                            </el-option>-->
        <!--                        </el-select>-->
        <!--                    </el-form-item>-->

        <el-form-item label="联系电话">
          <el-input
            v-model="filters.tel"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>
        <el-form-item label="宠物名称">
          <el-input
            v-model="filters.petName"
            placeholder="请输入宠物名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="饲主名称">
          <el-input
            v-model="filters.ownerName"
            placeholder="请输入饲主名称"
          ></el-input>
        </el-form-item>
        <div style="float: right; display: flex">
          <el-button
            type="primary"
            size="small"
            style="margin-right: 10px"
            plain
            @click="changeSearchType"
          >
            筛选
            <i v-if="searchType2 == 1" class="el-icon-arrow-down el-icon"></i>
            <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
          </el-button>

          <el-button-group>
            <el-button
              type="primary"
              size="small"
              icon="el-icon-search"
              @click="handleSearch"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              size="small"
              @click="resetParameter"
            >
              重置
            </el-button>
          </el-button-group>
        </div>
      </el-form>
    </el-col>
    <el-col v-if="searchType2 == 2" :span="24" class="form-line">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="犬牌号">
          <el-input
            v-model="filters.petNum"
            placeholder="请输入犬牌号"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="user.userType != '3'" label="所在地区">
          <el-select
            v-model="filters.petDept"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-col>
    <!--列表-->
    <template>
      <el-table
        v-loading="loading"
        :data="data"
        highlight-current-row
        style="width: 100%"
        stripe
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="petNum"
          label="犬牌号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petIdCard"
          width="185"
          label="身份证"
          sortable
        ></el-table-column>
        <el-table-column
          prop="ownerName"
          label="饲主姓名"
          sortable
        ></el-table-column>
        <el-table-column prop="tel" label="联系电话" sortable></el-table-column>
        <el-table-column
          prop="petName"
          label="宠物名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="raiseDate"
          label="饲养日期"
          sortable
        ></el-table-column>
        <el-table-column
          prop="immuneCancel.cancelName"
          label="注销人"
          sortable
        ></el-table-column>
        <el-table-column
          prop="immuneCancel.createDate"
          label="注销时间"
          sortable
        ></el-table-column>
        <el-table-column label="注销状态" sortable>
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row)" size="small">
              {{ formatStatus(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(1, scope.row.id, scope.row.petIdCard)"
            >
              查看
            </el-button>
            <el-button
              v-if="
                scope.row.immuneCancel == null ||
                scope.row.immuneCancel.status == '3'
              "
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(2, scope.row.id, scope.row.petIdCard)"
            >
              注销
            </el-button>
            <!--                        <el-button-->
            <!--                            v-if="user.userType=='3' && scope.row.immuneCancel !=null &&  scope.row.immuneCancel.status=='1'"-->
            <!--                            class="btn-text-pd0" type="text" @click="handleEdit(3,scope.row.id,scope.row.petIdCard)">  审核-->
            <!--                        </el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!--工具条-->
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      :title="title"
      :visible.sync="formVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-form ref="form" :model="form" label-width="150px">
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item v-if="uploadFiles.length > 0" label="宠物照片">
                  <img
                    v-for="(item, index) in uploadFiles"
                    :key="index"
                    :src="item.fileUrl"
                    style="width: 100px; height: 100px; margin-left: 5px"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="uploadFiles.length == 0" :span="24">
                <el-form-item label="宠物照片"></el-form-item>
              </el-col>
            </el-row>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="饲主姓名" prop="ownerName">
                <el-input
                  v-model="form.ownerName"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主姓名"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证" prop="petIdCard">
                <el-input
                  v-model="form.petIdCard"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主身份证号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="户籍地址" prop="ownerAddress">
                <el-input
                  v-model="form.ownerAddress"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入户籍地址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="tel">
                <el-input
                  v-model="form.tel"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宠物名字" prop="petName">
                <el-input
                  v-model="form.petName"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入犬只名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物性别" prop="petSex">
                <el-select
                  v-model="form.petSex"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petSexData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物品种" prop="petVarieties">
                <el-select
                  v-model="form.petVarieties"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petVarietiesArray"
                    :key="item.dictKey"
                    :label="item.name"
                    :value="item.dictKey"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="form.petVarieties === '107'" :span="12">
              <el-form-item label="其他品种" prop="otherVarieties">
                <el-input
                  v-model="form.otherVarieties"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入其他品种名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物毛色" prop="petHair">
                <el-select
                  v-model="form.petHair"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petHairData"
                    :key="item.dictKey"
                    :label="item.name"
                    :value="item.dictKey"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物年龄" prop="petAge">
                <el-input
                  v-model="form.petAge"
                  type="number"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入犬只年龄"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="饲养日期" prop="raiseDate">
                <el-date-picker
                  v-model="form.raiseDate"
                  style="width: 100%"
                  :readonly="disabled"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所在地区" prop="petDept">
                <div style="display: flex; align-items: center">
                  <div style="width: 110px">浙江省/金华市/</div>
                  <el-cascader
                    v-model="area"
                    :disabled="disabled"
                    :options="options"
                    :props="cateProps"
                  ></el-cascader>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="养宠地址" prop="petAddress">
                <el-input
                  v-model="form.petAddress"
                  type="textarea"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入详细地址"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <el-form
        ref="immuneCancel"
        :model="immuneCancel"
        :rules="formRules"
        label-width="150px"
      >
        <div style="padding-top: 10px">
          <el-row v-if="readonly" :gutter="20">
            <el-col :span="12">
              <el-form-item label="注销人" prop="cancelName">
                <el-input
                  v-model="immuneCancel.cancelName"
                  type="input"
                  :readonly="readonly"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注销时间" prop="createDate">
                <el-input
                  v-model="immuneCancel.createDate"
                  type="input"
                  :readonly="readonly"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="注销原因" prop="type">
                <el-select
                  v-model="immuneCancel.type"
                  :disabled="readonly"
                  style="width: 100%"
                  @change="change()"
                >
                  <el-option
                    v-for="item in types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="注销说明" prop="cancelReason">
                <el-input
                  v-model="immuneCancel.cancelReason"
                  type="textarea"
                  :readonly="readonly"
                  auto-complete="off"
                  placeholder="请输入注销说明"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!--                    <div style="padding-top: 10px" v-if="approvalForm">-->
        <!--                        <el-row :gutter="20">-->
        <!--                            <el-col :span="24">-->
        <!--                                <el-form-item label="审核状态" prop="cancelStatus">-->
        <!--                                    <el-select v-model="immuneCancel.cancelStatus" placeholder="请选择"-->
        <!--                                               style="width:100%" @change="change()">-->
        <!--                                        <el-option-->
        <!--                                            v-for="item in cancelStatuss"-->
        <!--                                            :key="item.value"-->
        <!--                                            :label="item.label"-->
        <!--                                            :value="item.value">-->
        <!--                                        </el-option>-->
        <!--                                    </el-select>-->
        <!--                                </el-form-item>-->
        <!--                            </el-col>-->
        <!--                        </el-row>-->
        <!--                        <el-row :gutter="20">-->
        <!--                            <el-col :span="24">-->
        <!--                                <el-form-item label="审核意见" prop="reason">-->
        <!--                                    <el-input type="textarea" v-model="immuneCancel.reason"-->
        <!--                                              auto-complete="off"-->
        <!--                                              placeholder="请输入审核意见"></el-input>-->
        <!--                                </el-form-item>-->
        <!--                            </el-col>-->
        <!--                        </el-row>-->
        <!--                    </div>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancelForm">取消</el-button>
        <el-button
          v-if="type == 2"
          size="medium"
          type="success"
          :loading="formLoading"
          @click.native="formSubmit('2')"
        >
          提交
        </el-button>
        <el-button
          v-if="type == 3"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="approvalSubmit"
        >
          审核
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
/* eslint-disable */
import { doPost, getDownLoadUrl } from '@/api/dog/index'

export default {
  data() {
    return {
      searchType2: 1,
      filters: {},
      user: {},
      form: {
        sysUser: {},
      },
      immuneCancel: {
        id: '',
        petId: '',
        cancelReason: '',
        cancelName: '',
        cancelStatus: '',
        createDate: '',
        reason: '',
        type: '',
      },
      formRules: {
        cancelReason: [
          { required: true, message: '请输入注销说明!', trigger: 'blur' },
        ],
        type: [{ required: true, message: '请选择注销原因!', trigger: 'blur' }],
        // cancelStatus: [
        //     {required: true, message: '请输入选择审核状态!', trigger: 'blur'}
        // ],
        // reason: [
        //     {required: true, message: '请输入审核意见!', trigger: 'blur'}
        // ],
      }, //表单验证规则
      type: '', //区分详情
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      title: '',
      formVisible: false,
      approvalForm: false,
      disabled: false,
      readonly: false,
      formLoading: false,
      headers: '',
      cateProps: {
        label: 'deptName',
        children: 'children',
        value: 'id',
      },
      data: [],
      petTypes: [],
      petVarietiesArray: [],
      petSexData: [
        { value: 1, label: '雄' },
        { value: 2, label: '雌' },
      ],
      types: [
        { value: 1, label: '走失注销' },
        { value: 2, label: '死亡注销' },
        { value: 3, label: '重复注册' },
        { value: 4, label: '其他' },
      ],
      petHairData: [],
      area: [],
      options: [], // 地区下拉
      statuss: [
        { label: '未注销', value: '' },
        { label: '待审核', value: '1' },
        { label: '已注销', value: '2' },
        { label: '审核不通过', value: '3' },
      ],
      cancelStatuss: [
        { label: '已注销', value: '2' },
        { label: '审核不通过', value: '3' },
      ],
      titleFileList: [],
      uploadFiles: [],
    }
  },
  methods: {
    //筛选展开
    changeSearchType() {
      if (this.searchType2 === 1) {
        this.searchType2 = 2
      } else {
        this.searchType2 = 1
      }
    },
    resetParameter() {
      this.filters = {}
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPgaeList()
    },
    handleSearch() {
      this.currentPage = 1
      this.getPgaeList()
    },

    // 获取列表（查询条件，欠缺调整）
    getPgaeList: function () {
      this.loading = true
      let para = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        petId: this.filters.petId,
        cancelStatus: this.filters.status,
        hospitalId: this.filters.hospitalId,
        petDept: this.filters.petDept,
        tel: this.filters.tel,
        petName: this.filters.petName,
        ownerName: this.filters.ownerName,
        petNum: this.filters.petNum,
        petIdCard: this.filters.petIdCard,
      }
      doPost('/immuneCancel/queryPageList', para)
        .then((res) => {
          this.data = res.list
          if (this.data !== null && this.data.length > 0) {
            for (var a in this.data) {
              this.data[a].index =
                (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
            }
          }
          this.total = res.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    //编辑
    handleEdit: function (type, id, petIdCard) {
      this.type = type
      this.disabled = true
      this.formVisible = true
      if (type === 1) {
        this.title = '查看'
        this.readonly = true
      } else if (type === 2) {
        this.title = '保存'
        this.readonly = false
        this.approvalForm = true
      } else if (type === 3) {
        this.title = '审核'
        this.readonly = true
        this.approvalForm = true
      }
      doPost('/immuneCancel/getByCardId', {
        id: id,
        petIdCard: petIdCard,
      }).then((res) => {
        this.form = res
        this.area = [this.form.petDept, this.form.street] //所在地区回显
        const imgArray = this.form.uploadFiles
        for (let i in imgArray) {
          if (
            imgArray[i].modelType == 'petImgZ' ||
            imgArray[i].modelType == 'petImgF'
          ) {
            this.uploadFiles.push({
              fileUrl: getDownLoadUrl(imgArray[i].fileUrl),
            })
          }
        }
        delete this.form.uploadFiles
        if (this.form.immuneCancel != null && type !== 2) {
          this.immuneCancel = this.form.immuneCancel
          this.immuneCancel.cancelStatus = ''
        }
      })
    },

    //提交
    formSubmit: function (status) {
      const param = {
        // id: this.immuneCancel.id,//主键ID
        petId: this.form.id,
        status: status, //1:已保存 2：待审核
        cancelReason: this.immuneCancel.cancelReason,
        type: this.immuneCancel.type,
        cancelId: this.user.id,
        cancelName: this.user.userName,
        brandNum: this.form.petNum,
      }
      this.$refs.immuneCancel.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = Object.assign({}, param)
            doPost('/immuneCancel/saveOrUpdate', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: '提交成功',
                type: 'success',
              })
              this.getPgaeList()
              this.cancelForm()
            })
          })
        }
      })
    },

    approvalSubmit: function () {
      let para = Object.assign(
        {},
        {
          id: this.immuneCancel.id,
          type: this.immuneCancel.type,
          status: this.immuneCancel.cancelStatus,
          reason: this.immuneCancel.reason,
          petId: this.form.id,
          cancelId: this.user.id,
          brandNum: this.form.petNum,
        }
      )
      this.$refs.immuneCancel.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            doPost('/immuneCancel/updateStatus', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: '提交成功',
                type: 'success',
              })
              this.getPgaeList()
              this.cancelForm()
            })
          })
        }
      })
    },

    //点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.approvalForm = false
      this.$refs['immuneCancel'].resetFields()
      this.$refs['form'].resetFields()
      this.immuneCancel.id = ''
    },
    change() {
      this.$forceUpdate()
    },
    formatStatus: function (row, column) {
      for (const i in this.statuss) {
        if (row.immuneCancel == null) {
          return '未注销'
        } else if (row.immuneCancel.status === this.statuss[i].value) {
          return this.statuss[i].label
        }
      }
    },
    getTypesData() {
      const str = []
      const para = { dictType: 'pet_type' }
      doPost('/sysDict/getAllList', para).then((res) => {
        for (var i in res) {
          this.petTypes.push(res[i])
          str.push(Number(res[i].dictKey))
        }
        this.filters.petTypes = str
        this.getPgaeList()
      })

      doPost('/sysDict/getAllList', { dictType: 'varieties_type' }).then(
        (res) => {
          for (var i in res) {
            this.petVarietiesArray.push(res[i])
            str.push(Number(res[i].dictKey))
          }
          this.filters.petVarietiesArray = str
        }
      )
      doPost('/sysDict/getAllList', { dictType: 'hair_type' }).then((res) => {
        this.petHairData = res
      })
      doPost('/dept/getSecondDeptTree', {}).then((res) => {
        this.options = res
      })
    },
    initHeaders: function () {
      var headers = {}
      this.user = JSON.parse(sessionStorage.getItem('user'))
      if (this.user) {
        // console.log('用户令牌=' + this.user.reqToken)
        headers.reqToken = this.user.reqToken
      }
      return headers
    },
    getStatusType(row) {
      if (!row.immuneCancel) return 'info'
      const statusMap = {
        1: 'warning', // 待审核
        2: 'success', // 已注销
        3: 'danger', // 审核不通过
      }
      return statusMap[row.immuneCancel.status] || 'info'
    },
  },
  mounted() {
    this.headers = this.initHeaders()
    if (this.user.userType === 2) {
      this.$set(this.filters, 'hospitalId', this.user.userQualifi.id) //医院ID
    }
    if (this.user.userType === 3) {
      // this.$set(this.filters, 'status', '2');
      this.$set(
        this.filters,
        'petDept',
        this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
          ? ''
          : this.user.deptId
      )
    }
    this.getTypesData()
  },
}
</script>

<style scoped></style>
