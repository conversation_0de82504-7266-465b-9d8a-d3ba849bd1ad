<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme === 'theme-dark'
          ? variables.menuBg
          : variables.menuLightBg,
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          settings.sideTheme === 'theme-dark'
            ? variables.menuBg
            : variables.menuLightBg
        "
        :text-color="
          settings.sideTheme === 'theme-dark'
            ? variables.menuText
            : 'rgba(0,0,0,.65)'
        "
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in routers"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'

export default {
  components: { SidebarItem, Logo },
  props: {
    routers: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRouters', 'sidebar']),
    activeMenu() {
      const currentRoute = this.$route
      const { meta, path } = currentRoute

      // 查找当前路由配置
      const routeConfig = this.$router.getRoutes().find((route) => route.path === path)
      const parentRouteName = routeConfig?.parent?.name

      // 处理自定义激活菜单路径
      if (meta.activeMenu) {
        // 如果父路由名称匹配自定义激活菜单
        if (parentRouteName === meta.activeMenu) {
          const parentRoute = routeConfig?.parent
          // 处理父路由重定向
          if (parentRoute?.redirect !== 'noRedirect') {
            // 转换重定向路径首字母为小写
            const redirectRoute = this.$router.getRoutes().find((route) => route.name === parentRoute?.redirect)

            return redirectRoute.path
          }
        }
        return meta.activeMenu
      }

      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
}
</script>
