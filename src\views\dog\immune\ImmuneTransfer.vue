<template>
  <div class="app-container">
    <!--工具条-->
    <el-col :span="24" class="form-line" style="padding-bottom: 0px">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="身份证">
          <el-input
            v-model="filters.name"
            placeholder="请输入身份证信息"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filters.status"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in statuss"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input
            v-model="filters.tel"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>
        <el-form-item label="宠物名称">
          <el-input
            v-model="filters.petName"
            placeholder="请输入宠物名称"
          ></el-input>
        </el-form-item>

        <div style="float: right; display: flex">
          <el-button
            type="primary"
            size="small"
            plain
            style="margin-right: 10px"
            @click="changeSearchType"
          >
            筛选
            <i v-if="searchType2 == 1" class="el-icon-arrow-down el-icon"></i>
            <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
          </el-button>

          <el-button-group>
            <el-button
              type="primary"
              size="small"
              icon="el-icon-search"
              @click="handleSearch"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              size="small"
              @click="resetParameter"
            >
              重置
            </el-button>
          </el-button-group>
        </div>
      </el-form>
    </el-col>
    <el-col v-if="searchType2 == 2" :span="24" class="form-line">
      <el-form size="small" :inline="true" :model="filters">
        <el-form-item label="饲主名称">
          <el-input
            v-model="filters.ownerName"
            placeholder="请输入饲主名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="犬牌号">
          <el-input
            v-model="filters.petNum"
            placeholder="请输入犬牌号"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="user.userType != '3'" label="所在地区">
          <el-select
            v-model="filters.petDept"
            clearable
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-col>
    <!--列表-->
    <template>
      <el-table
        v-loading="loading"
        border
        :data="data"
        highlight-current-row
        style="width: 100%"
        stripe
      >
        <el-table-column
          prop="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="petNum"
          label="犬牌号"
          sortable
        ></el-table-column>
        <el-table-column
          prop="petIdCard"
          width="185"
          label="身份证"
          sortable
        ></el-table-column>
        <el-table-column
          prop="ownerName"
          label="饲主姓名"
          sortable
        ></el-table-column>
        <el-table-column prop="tel" label="联系电话" sortable></el-table-column>
        <el-table-column
          prop="petName"
          label="宠物名称"
          sortable
        ></el-table-column>
        <el-table-column
          prop="raiseDate"
          label="饲养日期"
          sortable
        ></el-table-column>
        <el-table-column label="过户状态" sortable>
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row)"
              :effect="getStatusEffect(scope.row)"
            >
              {{ formatStatus(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <!--              <el-table-column prop="immuneRegister.hospital" label="免疫医院" sortable>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column prop="immuneRegister.injectionDate" label="注射日期" sortable>-->
        <!--              </el-table-column>-->
        <!--              <el-table-column prop="endDate" label="有效期至" sortable>-->
        <!--              </el-table-column>-->
        <el-table-column align="center" label="操作" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(1, scope.row.id, scope.row.petIdCard)"
            >
              查看
            </el-button>
            <el-button
              v-if="
                user.userType != '3' &&
                scope.row.immuneTransfer != null &&
                (scope.row.immuneTransfer.status == '1' ||
                  scope.row.immuneTransfer.status == '4')
              "
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(2, scope.row.id, scope.row.petIdCard)"
            >
              编辑
            </el-button>
            <el-button
              v-if="
                user.userType != '3' &&
                (scope.row.immuneTransfer == null ||
                  scope.row.immuneTransfer.status == '3')
              "
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(4, scope.row.id, scope.row.petIdCard)"
            >
              过户
            </el-button>
            <el-button
              v-if="
                user.userType == '3' &&
                scope.row.immuneTransfer != null &&
                scope.row.immuneTransfer.status == '2'
              "
              class="btn-text-pd0"
              type="text"
              @click="handleEdit(3, scope.row.id, scope.row.petIdCard)"
            >
              审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <!--工具条-->
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="padding: 15px 5px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      :title="title"
      :visible.sync="formVisible"
      :close-on-click-modal="false"
      :before-close="cancelForm"
    >
      <el-form ref="form" :model="form" label-width="150px">
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <div class="section-title">宠物信息</div>
            <el-divider></el-divider>
            <el-col :span="24">
              <el-form-item v-if="uploadFiles.length > 0" label="宠物照片">
                <img
                  v-for="(item, index) in uploadFiles"
                  :key="index"
                  :src="item.fileUrl"
                  style="width: 100px; height: 100px; margin-left: 5px"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="uploadFiles.length == 0" :span="24">
              <el-form-item label="宠物照片"></el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="犬牌号" prop="petNum">
                <el-select
                  v-model="form.petNum"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="item in petNumArray"
                    :key="item.brandNum"
                    :label="item.brandNum"
                    :value="item.brandNum"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物名字" prop="petName">
                <el-input
                  v-model="form.petName"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入犬只名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <!--<el-col :span="12">
                              <el-form-item label="宠物品种" prop="petType">
                                <el-select :disabled="disabled" v-model="form.petType" placeholder="请选择" style="width:100%">
                                  <el-option
                                      v-for="item in petTypes"
                                      :key="item.dictKey"
                                      :label="item.name"
                                      :value="item.dictKey">
                                  </el-option>
                                </el-select>
                              </el-form-item>
                            </el-col>-->
            <el-col :span="12">
              <el-form-item label="宠物性别" prop="petSex">
                <el-select
                  v-model="form.petSex"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petSexData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物品种" prop="petVarieties">
                <el-select
                  v-model="form.petVarieties"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petVarietiesArray"
                    :key="item.dictKey"
                    :label="item.name"
                    :value="item.dictKey"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="form.petVarieties === '107'" :span="12">
              <el-form-item label="其他品种" prop="otherVarieties">
                <el-input
                  v-model="form.otherVarieties"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入其他品种名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物毛色" prop="petHair">
                <el-select
                  v-model="form.petHair"
                  :disabled="disabled"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in petHairData"
                    :key="item.dictKey"
                    :label="item.name"
                    :value="item.dictKey"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="宠物年龄" prop="petAge">
                <el-input
                  v-model="form.petAge"
                  type="number"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入犬只年龄"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="饲养日期" prop="raiseDate">
                <el-date-picker
                  v-model="form.raiseDate"
                  style="width: 100%"
                  :readonly="disabled"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所在地区" prop="petDept">
                <div style="display: flex; align-items: center">
                  <div style="width: 110px">浙江省/金华市/</div>
                  <el-cascader
                    v-model="area"
                    :disabled="disabled"
                    :options="options"
                    :props="cateProps"
                  ></el-cascader>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="养宠地址" prop="petAddress">
                <el-input
                  v-model="form.petAddress"
                  type="textarea"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入详细地址"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="20">

            <el-col :span="12">
              <el-form-item label="饲主姓名" prop="ownerName">
                <el-input
                  v-model="form.ownerName"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主姓名"
                ></el-input>
              </el-col>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证" prop="petIdCard">
                <el-input
                  v-model="form.petIdCard"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主身份证号"
                                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="户籍地址" prop="ownerAddress">
                  <el-input
                    v-model="form.ownerAddress"
                    :readonly="disabled"
                    auto-complete="off"
                    placeholder="请输入户籍地址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="tel">
                <el-input
                  v-model="form.tel"
                  :readonly="disabled"
                  auto-complete="off"
                  placeholder="请输入饲主联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
        </div>
      </el-form>
      <el-form
        ref="immuneTransfer"
        :model="immuneTransfer"
        :rules="formRules"
        label-width="150px"
      >
        <div style="padding-top: 10px">
          <el-row :gutter="20">
            <div class="section-title">原饲主信息</div>
            <el-divider></el-divider>
            <el-col :span="12">
              <el-form-item label="原饲主身份证" prop="oldIdCard">
                <el-input
                  v-model="immuneTransfer.oldIdCard"
                  :readonly="disabled"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原饲主姓名" prop="oldName">
                <el-input
                  v-model="immuneTransfer.oldName"
                  :readonly="disabled"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="原饲主身份证地址" prop="oldAddress">
                <el-input
                  v-model="immuneTransfer.oldAddress"
                  :readonly="disabled"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原饲主手机号码" prop="oldTel">
                <el-input
                  v-model="immuneTransfer.oldTel"
                  :readonly="disabled"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="section-title">新饲主信息</div>
          <el-divider></el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="新饲主身份证" prop="newIdCard">
                <el-input
                  v-model="immuneTransfer.newIdCard"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="新饲主姓名" prop="newName">
                <el-input
                  v-model="immuneTransfer.newName"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="新饲主身份证地址" prop="newAddress">
                <el-input
                  v-model="immuneTransfer.newAddress"
                  :readonly="readonly"
                  auto-complete="off"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="新饲主手机号码" prop="newTel">
                <div style="display: flex; align-items: center">
                  <el-input
                    v-model="immuneTransfer.newTel"
                    :readonly="readonly"
                    auto-complete="off"
                    style="flex: 1"
                  ></el-input>
                  <el-button
                    v-if="type === 2 || type === 4"
                    type="primary"
                    size="small"
                    style="margin-left: 10px"
                    :disabled="
                      readonly ||
                      !immuneTransfer.newTel ||
                      immuneTransfer.newTel.length !== 11
                    "
                    @click="sendCaptcha"
                  >
                    {{ captchaButtonText }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col v-if="type === 2 || type === 4" :span="12">
              <el-form-item label="验证码" prop="captcha">
                <el-input
                  v-model="immuneTransfer.captcha"
                  :readonly="readonly"
                  auto-complete="off"
                  placeholder="请输入验证码"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="户口本照片" prop="residence">
                <el-upload
                  :class="[!readonly ? '' : 'hidden-Btn']"
                  list-type="picture-card"
                  :action="uploadUrl"
                  accept="image/*"
                  :on-change="handleResidenceChange"
                  :file-list="residenceImgList"
                  name="multipartFile"
                  :multiple="true"
                  :before-upload="beforeAvatarUpload"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div slot="file" slot-scope="{ file }">
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      style="border-radius: 6px; width: 148px; height: 148px"
                      alt=""
                      @click.self="showBigImage($event)"
                    />
                    <span v-if="!readonly" class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleResidenceRemove(file)"
                      >
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="房产证照片" prop="property">
                <el-upload
                  :class="[!readonly ? '' : 'hidden-Btn']"
                  list-type="picture-card"
                  :action="uploadUrl"
                  accept="image/*"
                  :on-change="handlePropertyChange"
                  :file-list="propertyImgList"
                  name="multipartFile"
                  :multiple="true"
                  :before-upload="beforeAvatarUpload"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div slot="file" slot-scope="{ file }">
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      style="border-radius: 6px; width: 148px; height: 148px"
                      alt=""
                      @click.self="showBigImage($event)"
                    />
                    <span v-if="!readonly" class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-delete"
                        @click="handlePropertyRemove(file)"
                      >
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div v-if="approvalForm && form.immuneTransfer" style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核状态" prop="transferStatus">
                <el-select
                  v-model="immuneTransfer.transferStatus"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="applyDisabled"
                >
                  <el-option
                    v-for="item in transferStatuss"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="审核意见" prop="reason">
                <el-input
                  v-model="immuneTransfer.reason"
                  type="textarea"
                  auto-complete="off"
                  placeholder="请输入审核意见"
                  :disabled="applyDisabled"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="cancelForm">取消</el-button>
        <el-button
          v-if="type == 2 || type == 4"
          size="medium"
          type="success"
          :loading="formLoading"
          @click.native="formSubmit('2')"
        >
          提交
        </el-button>
        <el-button
          v-if="type == 2 || type == 4"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="formSubmit('1')"
        >
          保存
        </el-button>
        <el-button
          v-if="type == 3"
          size="medium"
          type="primary"
          :loading="formLoading"
          @click.native="approvalSubmit"
        >
          审核
        </el-button>
      </div>
    </el-dialog>
    <div v-show="photoVisible" class="showPhoto" @click="closeClick">
      <img class="img" :src="bigImgUrl" alt="图片加载失败" />
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import { base, doGet, doPost, getDownLoadUrl } from '@/api/dog/index'

export default {
  data() {
    const residenceValidate = (rule, value, callback) => {
      if (this.residenceImgList.length == 0) {
        callback(new Error('请选择户口本照片！'))
      } else {
        callback()
      }
    }
    const propertyValidate = (rule, value, callback) => {
      if (this.propertyImgList.length == 0) {
        callback(new Error('请选择房产证/租赁合同照片！'))
      } else {
        callback()
      }
    }
    const captchaValidate = (rule, value, callback) => {
      if ((this.type === 2 || this.type === 4) && !value) {
        callback(new Error('请输入验证码！'))
      } else {
        callback()
      }
    }
    return {
      filters: {},
      type: '', //区分详情
      loading: false,
      data: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      form: {
        id: '',
        ownerName: '', //饲主姓名
        petIdCard: '', //身份证号
        ownerAddress: '', //户籍地址
        tel: '', //饲主联系电话
        petNum: '', //犬牌编号
        petName: '', //宠物名
        petType: '', //宠物类别(对应字典表pet_type)
        petSex: '', //宠物性别
        petVarieties: '', //品种(对应字典表varieties_type)
        petHair: '', //毛色(对应字典表hair_type)
        petAge: '', //年龄
        raiseDate: '', //饲养日期
        petAddress: '', //详细地址
        petDept: '', //所在地区
        isAgency: '', //是否代办：1是，2否
        agencyCom: '', //代办单位
        status: '', //审批状态：1待审批，2已通过，3已注销，4走失注销
        aboutMake: '', //预约方式
        hospitalId: '', //注射医院ID
        petId: '', //犬只ID
        hospital: '', //注射医生
        doctor: '', //注射医生
        vaccineBrand: '', //疫苗品牌
        vaccineBrandOld: '', //原来的疫苗品牌
        vaccineBatch: '', //疫苗批次
        injectionDate: '', //注射日期
        injectionEnddate: '', //到期日期
        aboutDate: '', //预约时间
        expresType: '', //领取方式
        expresAddress: '', //领取地址
        petImg: '',
        residence: '',
        property: '',
      },
      immuneTransfer: {
        id: '',
        petId: '',
        oldIdCard: '',
        oldAddress: '',
        oldName: '',
        oldTel: '',
        newIdCard: '',
        newAddress: '',
        newName: '',
        newTel: '',
        transferStatus: '',
        reason: '',
        img: '',
        status: '',
        captcha: '',
      },
      title: '',
      formVisible: false,
      approvalForm: false,
      formRules: {
        newIdCard: [
          { required: true, message: '请输入新饲主身份证!', trigger: 'blur' },
          {
            pattern:
              /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            message: '格式有误!',
          },
        ],
        newAddress: [
          {
            required: true,
            message: '请输入新饲主身份证地址!',
            trigger: 'blur',
          },
        ],
        newName: [
          { required: true, message: '请输入新饲主姓名!', trigger: 'blur' },
        ],
        newTel: [
          { required: true, message: '请输入新饲主电话!', trigger: 'blur' },
          {
            pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
            message: '手机号格式有误!',
          },
        ],
        captcha: [{ validator: captchaValidate, trigger: 'blur' }],
        transferStatus: [
          { required: true, message: '请选择审核状态!', trigger: 'blur' },
        ],
        residence: [
          { required: true, validator: residenceValidate, trigger: 'blur' },
        ],
        property: [
          { required: true, validator: propertyValidate, trigger: 'blur' },
        ],
      },
      disabled: false,
      readonly: false,
      applyDisabled: false,
      formLoading: false,
      residenceName: '',
      residencePath: '',
      propertyName: '',
      propertyPath: '',
      photoVisible: false,
      bigImgUrl: '',
      user: {},
      cateProps: {
        label: 'deptName',
        children: 'children',
        value: 'id',
      },
      petTypes: [],
      petVarietiesArray: [],
      petSexData: [
        { value: 1, label: '雄' },
        { value: 2, label: '雌' },
      ],
      petHairData: [],
      area: [],
      options: [], // 地区下拉
      statuss: [
        { label: '未过户', value: '0' },
        { label: '已保存', value: '1' },
        { label: '待审核', value: '2' },
        { label: '已过户', value: '3' },
        { label: '未通过', value: '4' },
      ],
      searchType2: 1,
      //上传附件
      uploadUrl: base + '/sysUploadFile/uploadFile',
      headers: '',
      uploadFiles: [],
      propertyImgList: [], //户口本照片集合
      residenceImgList: [], //租房合同照片集合
      petNumArray: [], // 犬牌下拉
      captchaButtonText: '发送验证码',
      countdown: 60,
      countdownTimer: null,
    }
  },
  computed: {
    transferStatuss() {
      if (this.user.userType == '3') {
        return [
          { label: '通过', value: '3' },
          { label: '未通过', value: '4' }
        ]
      } else {
        return [
          { label: '未过户', value: null },
          { label: '未审核', value: '1' },
          { label: '未审核', value: '2' },
          { label: '通过', value: '3' },
          { label: '未通过', value: '4' },
        ]
      }
    }
  },
  mounted() {
    this.getPetNum()
    this.headers = this.initHeaders()

    // 从URL获取petName参数并赋值
    const urlParams = new URLSearchParams(window.location.search)
    const petNameParam = urlParams.get('petName')
    if (petNameParam) {
      this.filters.petName = petNameParam
    }

    if (this.user.userType === 2) {
      this.$set(this.filters, 'hospitalId', this.user.userQualifi.id) //医院ID
    }
    if (this.user.userType === 3) {
      // this.$set(this.filters, 'status', '2');
      this.$set(
        this.filters,
        'petDept',
        this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
          ? ''
          : this.user.deptId
      )
    }
    this.getTypesData()
  },
  methods: {
    getPetNum() {
      var param = {
        brandCom: this.user?.userQualifi?.id || '',
        brandNum: this.rsissue?.dogBrand || '',
      }
      doPost('/petBrand/getAllList', param).then((res) => {
        this.petNumArray = res
      })
    },
    //筛选展开
    changeSearchType() {
      if (this.searchType2 === 1) {
        this.searchType2 = 2
      } else {
        this.searchType2 = 1
      }
    },
    resetParameter() {
      this.filters = {}
      if (this.user.userType === 2) {
        this.$set(this.filters, 'hospitalId', this.user.userQualifi.id) //医院ID
      }
      if (this.user.userType === 3) {
        // this.$set(this.filters, 'status', '2');
        this.$set(
          this.filters,
          'petDept',
          this.user.deptId == '753f3c429c5d462cb5e9fa1113461128'
            ? ''
            : this.user.deptId
        )
      }
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getPgaeList()
    },
    handleSearch() {
      this.currentPage = 1
      this.getPgaeList()
    },

    showBigImage(e) {
      //点击图片函数，点击后，把photoVisible设置成true
      debugger
      if (e != '' && this.readonly) {
        this.photoVisible = true
        this.bigImgUrl = e.currentTarget.src
      }
    },

    //房产证/租赁合同照片多上传
    handlePropertyChange(file, fileList) {
      const a = fileList.length - 1
      if (fileList[a].status === 'success') {
        this.propertyImgList.push({
          fileUrl: fileList[a].response.data,
          fileName: fileList[a].name,
          modelType: 'property',
          uid: file.uid,
          url: getDownLoadUrl(fileList[a].response.data),
        })
      }
    },
    //删除房产证/租赁合同照片
    handlePropertyRemove(file, fileList) {
      this.propertyImgList.forEach((item, index) => {
        if (item.uid == file.uid) {
          this.propertyImgList.splice(index, 1)
        }
      })
    },
    //户口本照片多上传
    handleResidenceChange(file, fileList) {
      const a = fileList.length - 1
      if (fileList[a].status === 'success') {
        this.residenceImgList.push({
          fileUrl: fileList[a].response.data,
          fileName: fileList[a].name,
          modelType: 'residence',
          uid: file.uid,
          url: getDownLoadUrl(fileList[a].response.data),
        })
      }
    },
    //删除户口本照片
    handleResidenceRemove(file, fileList) {
      this.residenceImgList.forEach((item, index) => {
        if (item.uid == file.uid) {
          this.residenceImgList.splice(index, 1)
        }
      })
    },
    // 限制上传格式
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 10
      const isPNG = file.type === 'image/png'
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是JPG格式或PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },

    closeClick() {
      this.photoVisible = false
    },

    // 获取列表（查询条件，欠缺调整）
    getPgaeList: function () {
      this.loading = true
      let para = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        petId: this.filters.petId,
        transferStatus: this.filters.status,
        hospitalId: this.filters.hospitalId,
        petDept: this.filters.petDept,
        tel: this.filters.tel,
        petName: this.filters.petName,
        ownerName: this.filters.ownerName,
        petNum: this.filters.petNum,
      }
      doPost('/immuneTransfer/queryPageList', para)
        .then((res) => {
          this.data = res.list
          if (this.data !== null && this.data.length > 0) {
            for (var a in this.data) {
              this.data[a].index =
                (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
            }
          }
          this.total = res.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    //提交
    formSubmit: function (status) {
      const imgList = [...this.residenceImgList, ...this.propertyImgList]
      this.immuneTransfer.petId = this.form.id
      this.immuneTransfer.status = status //1：保存 2： 提交审核
      this.immuneTransfer.img =
        imgList.length === 0 ? '' : JSON.stringify(imgList)
      // console.log(this.immuneTransfer)
      this.$refs.immuneTransfer.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = Object.assign({}, this.immuneTransfer)
            doPost('/immuneTransfer/saveOrUpdateH5', para).then((res) => {
              this.formLoading = false
              if (res == '2') {
                this.$message({
                  message: '过户新饲主名下已有犬只信息，不允许过户！',
                  type: 'error',
                })
                return
              } else {
                this.$message({
                  message: '提交成功',
                  type: 'success',
                })
              }

              this.getPgaeList()
              this.cancelForm()
            }).finally(() => {
              this.formLoading = false
            })
          })
        }
      })
    },

    approvalSubmit: function () {
      if (
        this.immuneTransfer.transferStatus == '4' &&
        this.immuneTransfer.reason === ''
      ) {
        this.$message({
          message: '请输入审核意见',
          type: 'error',
        })
        return
      }

      // 审核模式下手动设置验证规则
      const tempRules = {
        transferStatus: [
          { required: true, message: '请选择审核状态!', trigger: 'blur' },
        ],
      }

      // 临时保存原规则
      const originalRules = this.$refs.immuneTransfer.rules
      // 设置临时规则
      this.$refs.immuneTransfer.rules = tempRules

      this.$refs.immuneTransfer.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = Object.assign(
              {},
              {
                id: this.immuneTransfer.id,
                status: this.immuneTransfer.transferStatus,
                reason: this.immuneTransfer.reason,
                newIdCard: this.immuneTransfer.newIdCard,
                newAddress: this.immuneTransfer.newAddress,
                newName: this.immuneTransfer.newName,
                newTel: this.immuneTransfer.newTel,
                petId: this.form.id,
                img: this.form.immuneTransfer.img,
                tel: this.form.tel,
                realName: this.form.ownerName,
                petIdCard: this.form.petIdCard,
              }
            )
            doPost('/immuneTransfer/updateStatus', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: '提交成功',
                type: 'success',
              })
              this.getPgaeList()
              this.cancelForm()
            })
          })
        }
        // 恢复原规则
        this.$refs.immuneTransfer.rules = originalRules
      })
    },

    //编辑
    handleEdit: function (type, id, petIdCard) {
      this.type = type
      this.disabled = true
      this.formVisible = true
      if (type === 1) {
        this.title = '查看'
        this.readonly = true
        this.approvalForm = false // 默认不显示审核状态和意见
        this.applyDisabled = true
      } else if (type === 2) {
        this.title = '编辑'
        this.approvalForm = false
      } else if (type === 4) {
        this.title = '过户'
        this.approvalForm = false
      } else if (type === 3) {
        this.title = '审核'
        this.approvalForm = true
        this.applyDisabled = false
      }
      let para = { id: id, petIdCard: petIdCard }
      doPost('/immuneTransfer/getByCardId', para).then((res) => {
        this.form = res
        this.area = [this.form.petDept, this.form.street] //所在地区回显
        if (!this.form.immuneTransfer) {
          //如果过户信息没有
          this.immuneTransfer = this.$options.data().immuneTransfer
          this.approvalForm = false // 未过户状态，不显示审核字段
        } else if (type !== 4) {
          this.immuneTransfer = { ...this.immuneTransfer,...this.form.immuneTransfer }
          // 审核模式下，状态初始化为null
          if (type === 3) {
            this.immuneTransfer.transferStatus = null
          } else {
            this.immuneTransfer.transferStatus = this.form.immuneTransfer.status
          }
          // 只有查看模式且有过户记录时才显示审核信息
          if (type === 1 && this.form.immuneTransfer) {
            this.approvalForm = true
          }
          const imgList = JSON.parse(this.form.immuneTransfer.img)
          for (const i in imgList) {
            if (imgList[i].modelType == 'residence') {
              this.residenceImgList.push({
                uid: imgList[i].id,
                url: getDownLoadUrl(imgList[i].fileUrl),
                name: imgList[i].fileName,
                fileName: imgList[i].fileName,
                fileUrl: imgList[i].fileUrl,
                modelType: 'residence',
              })
            } else if (imgList[i].modelType == 'property') {
              this.propertyImgList.push({
                uid: imgList[i].id,
                url: getDownLoadUrl(imgList[i].fileUrl),
                name: imgList[i].fileName,
                fileName: imgList[i].fileName,
                fileUrl: imgList[i].fileUrl,
                modelType: 'property',
              })
            }
          }
        }
        if (type === 4) {
          //只有过户登记显示原饲主信息
          this.immuneTransfer.oldIdCard = this.form.petIdCard
          this.immuneTransfer.oldAddress = this.form.ownerAddress
          this.immuneTransfer.oldName = this.form.ownerName
          this.immuneTransfer.oldTel = this.form.tel
        }
        const imgArray = this.form.uploadFiles
        for (let i in imgArray) {
          if (
            imgArray[i].modelType == 'petImgZ' ||
            imgArray[i].modelType == 'petImgF'
          ) {
            this.uploadFiles.push({
              fileUrl: getDownLoadUrl(imgArray[i].fileUrl),
            })
          }
        }
        delete this.form.uploadFiles
      })
    },
    //点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.approvalForm = false
      this.readonly = false
      this.$refs['immuneTransfer'].resetFields()
      this.$refs['form'].resetFields()
      this.propertyImgList = [] //户口本照片集合
      this.residenceImgList = [] //租房合同照片集合
      this.uploadFiles = []
      this.immuneTransfer = this.$options.data().immuneTransfer
      clearInterval(this.countdownTimer)
      this.captchaButtonText = '发送验证码'
      this.countdown = 60
    },
    formatType: function (row, column) {
      for (var i in this.petTypes) {
        if (row.petType == this.petTypes[i].dictKey) {
          return this.petTypes[i].name
        }
      }
    },

    formatStatus: function (row, column) {
      for (let i in this.statuss) {
        if (row.immuneTransfer == null) {
          return '未过户'
        } else if (row.immuneTransfer.status === this.statuss[i].value) {
          return this.statuss[i].label
        }
      }
    },
    getTypesData() {
      var str = []
      var para = { dictType: 'pet_type' }
      doPost('/sysDict/getAllList', para).then((res) => {
        for (var i in res) {
          this.petTypes.push(res[i])
          str.push(Number(res[i].dictKey))
        }
        this.filters.petTypes = str
      })
      doPost('/sysDict/getAllList', { dictType: 'varieties_type' }).then(
        (res) => {
          this.petVarietiesArray = res
        }
      )
      doPost('/sysDict/getAllList', { dictType: 'hair_type' }).then((res) => {
        this.petHairData = res
      })
      doPost('/dept/getSecondDeptTree', {}).then((res) => {
        this.options = res
      })
      this.getPgaeList()
    },

    initHeaders: function () {
      var headers = {}
      this.user = JSON.parse(sessionStorage.getItem('user'))
      if (this.user) {
        // console.log('用户令牌=' + this.user.reqToken)
        headers.reqToken = this.user.reqToken
      }
      return headers
    },
    getStatusType(row) {
      if (!row.immuneTransfer) return 'info'
      const status = row.immuneTransfer.status
      const statusMap = {
        0: 'info', // 未过户
        1: 'warning', // 已保存
        2: 'primary', // 待审核
        3: 'success', // 已过户
        4: 'danger', // 未通过
      }
      return statusMap[status] || 'info'
    },

    getStatusEffect(row) {
      if (!row.immuneTransfer) return 'plain'
      const status = row.immuneTransfer.status
      return ['0', '1', '4'].includes(status) ? 'plain' : 'light'
    },
    sendCaptcha() {
      if (!this.immuneTransfer.newTel) {
        this.$message.error('请输入手机号码')
        return
      }
      if (!/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.immuneTransfer.newTel)) {
        this.$message.error('手机号格式不正确')
        return
      }

      this.captchaButtonText = `${this.countdown}秒后重试`
      this.countdownTimer = setInterval(() => {
        this.countdown--
        this.captchaButtonText = `${this.countdown}秒后重试`
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
          this.captchaButtonText = '发送验证码'
          this.countdown = 60
        }
      }, 1000)

      // 调用发送验证码接口
      doGet('/sendCaptcha', { tel: this.immuneTransfer.newTel })
        .then((res) => {
          this.$message.success('验证码发送成功')
        })
        .catch((error) => {
          clearInterval(this.countdownTimer)
          this.captchaButtonText = '发送验证码'
          this.countdown = 60
          this.$message.error('验证码发送失败')
        })
    },
  },
}
</script>

<style scoped>
:deep(.hidden-Btn .el-upload) {
  display: none;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: #409eff;
}

:deep(.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

:deep(.avatar) {
  width: 100%;
  height: 150px;
  display: block;
}

.deleteImg {
  font-size: 20px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
}

.showPhoto {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.showPhoto .img {
  display: block;
  margin: auto 0;
  max-width: 100%;
  text-align: center;
}
</style>
