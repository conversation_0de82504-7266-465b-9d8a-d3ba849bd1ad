<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="568助老助残直通车预约">
    </el-page-header>

    <!-- 预约信息表单容器 -->
    <div class="form-container">
      <!-- 预约信息表单 -->
      <el-form
        ref="form"
        :model="user"
        :rules="rules"
        label-width="120px"
        size="medium"
      >
        <!-- 车次信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="乘车班次" prop="trainNumber">
              <el-input
                v-model="user.trainNumber"
                placeholder="请填写您的乘车班次"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发车时间" prop="trainTime">
              <el-date-picker
                v-model="user.trainTime"
                type="datetime"
                placeholder="请选择发车时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 预约人信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="您的姓名" prop="name">
              <el-input
                v-model="user.name"
                placeholder="请输入姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input
                v-model="user.phone"
                placeholder="请填写手机号码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="残病类型" prop="diseaseTypeName">
              <el-input
                v-model="user.diseaseTypeName"
                placeholder="请输入残病类型"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 服务需求 -->
        <el-form-item label="服务需求" prop="types">
          <div v-if="!typeColumns.length" class="loading-text">加载中...</div>
          <el-checkbox-group v-else v-model="user.types">
            <el-checkbox
              v-for="(item, index) in typeColumns"
              :key="index"
              :label="item.value"
              class="checkbox-item"
            >
              {{ item.text }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 相关人员信息 -->
        <div v-if="user.types.includes('1')">
          <el-form-item label="相关人员">
            <el-checkbox v-model="user.isEmergency"
              >是否有陪同人员或应急联系人</el-checkbox
            >
          </el-form-item>

          <template v-if="user.isEmergency">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="人员姓名" prop="emergencyName">
                  <el-input
                    v-model="user.emergencyName"
                    placeholder="请输入陪同人员或应急联系人姓名"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="emergencyPhone">
                  <el-input
                    v-model="user.emergencyPhone"
                    placeholder="请填写手机号码"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-form-item label="上门地址" prop="address">
            <el-input
              v-model="user.address"
              placeholder="请填写上门地址"
              clearable
            />
          </el-form-item>
        </div>

        <!-- 需求描述 -->
        <el-form-item label="需求描述" prop="description">
          <el-input
            v-model="user.description"
            type="textarea"
            :rows="4"
            placeholder="请输入您的具体服务需求详情"
            maxlength="300"
            show-word-limit
          />
        </el-form-item>

        <!-- 协议确认 -->
        <el-form-item>
          <el-checkbox v-model="isAgree">
            您的预约应符合
            <el-button
              type="text"
              class="agreement-link"
              @click="showInstructions"
            >
              《568助老助残直通车服务说明》
            </el-button>
            的相关要求，点击提交预约即表示您已同意相关要求
          </el-checkbox>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            size="medium"
            @click="onSubmit"
            :loading="submitting"
          >
            提交预约
          </el-button>
          <el-button size="medium" @click="resetForm"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 服务说明弹窗 -->
    <el-dialog
      title="568助老助残直通车服务说明"
      :visible.sync="instructionsVisible"
      width="800px"
      :before-close="handleCloseInstructions"
    >
      <div class="instructions-content">
        <div class="section">
          <p>
            <span class="bold">简介：</span
            >"568助老助残直通车"旨在帮助出行不便的残疾人、老年人，通过提前预约，便可得到568志愿者点到点接站进站服务。
          </p>
        </div>

        <div class="section">
          <p><span class="bold">接受预约范围:</span></p>
          <p>地域范围：金华市区内。</p>
          <p>服务时间：早上8:30-下午17:00</p>
          <p>送达车站：铁路金华站。</p>
        </div>

        <div class="section">
          <p><span class="bold">一、服务预约：</span></p>
          <p class="p-l-10">(1)至少提前一天预约。</p>
          <p class="p-l-10">
            (2)需提供个人基本信息、身体情况、乘车信息、服务需求等。
          </p>
          <p class="p-l-10">(3)如需上门接送，还需提供详细地址。</p>
          <p class="p-l-10">(4)若没有随行陪同人员必须提供应急联系人电话。</p>
          <p class="p-l-10">(5)如有变化需提前2小时取消服务预约。</p>
          <p class="p-l-10">
            (6)旅客无故迟到，服务自动取消，次数超过2次将不再接受其预约申请。
          </p>
        </div>

        <div class="section">
          <p><span class="bold">二、服务安排：</span></p>
          <p class="p-l-10">根据需求安排各环节志愿者对接做好服务。</p>
          <p class="p-l-10">站外：金华出租雷锋车队</p>
          <p class="p-l-10">站区：金华市综合行政执法局站前管理中心568志愿队</p>
          <p class="p-l-10">站内：铁路金华站</p>
        </div>

        <div class="section">
          <p><span class="bold">三、服务过程：</span></p>
          <p class="p-l-10">
            根据预定时间地点做好点对点服务，各阶段对服务过程进行照片记录。
          </p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="instructionsVisible = false">关闭</el-button>
        <el-button type="primary" @click="agreeInstructions"
          >我已阅读并同意</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCase } from "@/api/568/index";
import { addAppointment } from "@/api/home";

export default {
  name: "ElderlyAssistance",
  data() {
    // 手机号验证器
    const phoneValidator = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入手机号码"));
      } else {
        const phonePattern =
          /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
        // 如果和原本手机号相同直接过，否则进入手机号正则判断
        const ph = this.asePhone ? this.asePhone(this.vuex_user_mobile) : "";
        if (ph === value.trim() || phonePattern.test(value)) {
          callback();
        } else {
          callback(new Error("手机号码格式不正确"));
        }
      }
    };

    return {
      user: {
        name: "",
        phone: "",
        trainNumber: "",
        trainTime: "",
        diseaseTypeName: "",
        description: "",
        types: [],
        isEmergency: false,
        emergencyName: "",
        emergencyPhone: "",
        address: "",
      },
      typeColumns: [],
      isAgree: false,
      submitting: false,
      instructionsVisible: false,
      rules: {
        trainNumber: [
          { required: true, message: "请填写乘车班次", trigger: "blur" },
        ],
        trainTime: [
          { required: true, message: "请选择发车时间", trigger: "change" },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        phone: [{ required: true, validator: phoneValidator, trigger: "blur" }],
        diseaseTypeName: [
          { required: true, message: "请输入残病类型", trigger: "blur" },
        ],
        types: [
          {
            required: true,
            message: "请选择至少一项服务需求",
            trigger: "change",
          },
        ],
        description: [
          { required: true, message: "请输入需求描述", trigger: "blur" },
        ],
        emergencyName: [
          { required: true, message: "请输入人员姓名", trigger: "blur" },
        ],
        emergencyPhone: [
          { required: true, validator: phoneValidator, trigger: "blur" },
        ],
        address: [
          { required: true, message: "请填写上门地址", trigger: "blur" },
        ],
      },
    };
  },
  async created() {
    await this.initUserInfo();
    await this.loadServiceTypes();
  },
  mounted() {
    // 埋点处理，计算页面加载完成时间
    if (this.pv_getCalcTime) {
      this.pv_getCalcTime("pv_time_t2");
    }
  },
  methods: {
    // 初始化用户信息
    async initUserInfo() {
      try {
        const name = this.aseName ? this.aseName(this.vuex_user_realName) : "";
        const phone = this.asePhone ? this.asePhone(this.vuex_user_mobile) : "";
        this.user = { ...this.user, name, phone };
      } catch (error) {
        console.warn("初始化用户信息失败:", error);
      }
    },

    // 加载服务类型
    async loadServiceTypes() {
      try {
        const res = await getCase("vol_detail_type");
        this.typeColumns = res.data.map((item) => ({
          text: item.dictLabel,
          value: item.dictValue,
        }));
      } catch (error) {
        console.error("加载服务类型失败:", error);
        this.$message.error("加载服务类型失败");
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
      this.user = {
        name: "",
        phone: "",
        trainNumber: "",
        trainTime: "",
        diseaseTypeName: "",
        description: "",
        types: [],
        isEmergency: false,
        emergencyName: "",
        emergencyPhone: "",
        address: "",
      };
      this.isAgree = false;
      this.initUserInfo();
    },

    // 提交表单
    async onSubmit() {
      try {
        // 验证协议确认
        if (!this.isAgree) {
          this.$message.warning(
            "请先阅读《568助老助残直通车服务说明》后再提交"
          );
          return;
        }

        // 验证表单
        await this.$refs.form.validate();

        // 验证服务需求
        if (!this.user.types.length) {
          this.$message.warning("请选择至少一项服务需求");
          return;
        }

        // 确认提交
        await this.$confirm("是否确认提交申请？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        this.submitting = true;

        // 准备提交数据
        const params = this.prepareSubmitData();

        // 提交申请
        await addAppointment(params);

        this.$message.success(
          '已提交审核，提交申请后可在"我的-审批事项"中查看进度'
        );

        // 返回上一页或跳转到指定页面
        this.$router.back();
      } catch (error) {
        if (error !== "cancel") {
          console.error("提交失败:", error);
          this.$message.error("网络异常，请重试！");
        }
      } finally {
        this.submitting = false;
      }
    },

    // 准备提交数据
    prepareSubmitData() {
      const name = this.aseName ? this.aseName(this.vuex_user_realName) : "";
      const phone = this.asePhone ? this.asePhone(this.vuex_user_mobile) : "";

      const params = {
        ...this.user,
        isEmergency: Number(this.user.isEmergency),
        types: this.user.types.join(","),
      };

      // 如果姓名和手机号与原始信息相同，使用原始信息
      if (this.user.name.trim() === name && this.vuex_user_realName) {
        params.name = this.vuex_user_realName;
      }
      if (this.user.phone.trim() === phone && this.vuex_user_mobile) {
        params.phone = this.vuex_user_mobile;
      }

      // 加密敏感信息
      if (this.user.emergencyName && this.encrypt) {
        params.emergencyName = this.encrypt(this.user.emergencyName);
      }
      if (this.user.emergencyPhone && this.encrypt) {
        params.emergencyPhone = this.encrypt(this.user.emergencyPhone);
      }

      return params;
    },

    // 显示服务说明弹窗
    showInstructions() {
      this.instructionsVisible = true;
    },

    // 关闭弹窗前的处理
    handleCloseInstructions(done) {
      done();
    },

    // 同意服务说明
    agreeInstructions() {
      this.isAgree = true;
      this.instructionsVisible = false;
      this.$message.success("已确认阅读服务说明");
    },

    // 返回上一页
    goBack() {
      this.$router.back();
    }
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  max-width: 1200px;
  margin: 20px auto 0;
  min-height: calc(100vh - 84px);
}

.form-container {
  max-width: 1200px;
  margin: 20px auto 0;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
}

.loading-text {
  color: #909399;
  font-size: 14px;
}

.checkbox-item {
  display: block;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.agreement-link {
  color: #409eff;
  padding: 0;
  font-size: inherit;

  &:hover {
    color: #66b1ff;
  }
}

// Element UI 样式覆盖
::v-deep .el-form {
  .el-form-item {
    margin-bottom: 22px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 4px;

    &:focus {
      border-color: #409eff;
    }
  }

  .el-checkbox {
    .el-checkbox__label {
      color: #606266;
      font-size: 14px;
    }
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 8px;
    }
  }

  .el-date-editor {
    width: 100%;
  }
}

// PageHeader 样式
::v-deep .el-page-header {
  padding: 0;
  margin-bottom: 20px;

  .el-page-header__content {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .form-container {
    margin: 10px auto 0;
    padding: 20px;
    border-radius: 4px;
  }

  ::v-deep .el-page-header {
    .el-page-header__content {
      font-size: 18px;
    }
  }

  ::v-deep .el-col {
    margin-bottom: 0;
  }

  ::v-deep .el-row {
    margin-left: 0 !important;
    margin-right: 0 !important;

    .el-col {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }
}

// 弹窗内容样式
.instructions-content {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;

  .section {
    margin-bottom: 16px;
  }

  .bold {
    font-weight: 600;
    color: #303133;
  }

  .p-l-10 {
    padding-left: 20px;
    margin: 8px 0;
  }

  p {
    margin: 8px 0;
    text-indent: 0;
  }
}

// Element UI 弹窗样式覆盖
::v-deep .el-dialog {
  .el-dialog__header {
    background: #f5f7fa;
    padding: 20px 20px 10px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
  }
}
</style>
