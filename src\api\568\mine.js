import request from "@/utils/request";

// 获取当前用户信息和志愿者数量
export function hourScore() {
  return request({
    url: "/business/vol/mine/index",
    method: "get",
  });
}

// 获取我的评分
export function getEvaluatesScore() {
  return request({
    url: "/business/vol/mine/evaluatesScore",
    method: "get",
  });
}

// 获取我的评价
export function getMineEvaluateList(params) {
  return request({
    url: "/business/vol/mine/mineList",
    method: "get",
    params,
  });
}

// 获取补卡申请列表（打卡记录列表）
export function getSignList(params) {
  return request({
    url: "/business/vol/sign/list",
    method: "get",
    params,
  });
}

// 查询补卡申请详情
export function getSign(id) {
  return request({
    url: "/business/vol/sign/" + id,
    method: "get",
  });
}

// 补卡
export function repairSign(data) {
  return request({
    url: "/business/vol/sign/repair",
    method: "POST",
    data,
  });
}

// 获取积分列表
export function getScoreList(params) {
  return request({
    url: "/business/vol/score/record/list",
    method: "get",
    params,
  });
}

// 获取积分奖品列表
export function getScoreGiftList(params) {
  return request({
    url: "/business/vol/gift/listWithImg",
    method: "get",
    params,
  });
}

// 积分兑换
export function exchangeGift(id) {
  return request({
    url: `/business/vol/gift/exchangeGift/${id}`,
    method: "POST",
  });
}

// 查询审批记录列表
export function listApprove(query) {
  return request({
    url: "/business/approve/list",
    method: "get",
    params: query,
  });
}

// 查询志愿者任务（报名）详情
export function getTaskUser(id) {
  return request({
    url: "/business/vol/taskUser/detail/" + id,
    method: "get",
  });
}
