import request from '@/utils/request'

// 查询监控设备列表
export function listDevice(query) {
  return request({
    url: '/airport/device/list',
    method: 'get',
    params: query
  })
}

// 查询监控设备详细
export function getDevice(deviceId) {
  return request({
    url: '/airport/device/' + deviceId,
    method: 'get'
  })
}

// 新增监控设备
export function addDevice(data) {
  return request({
    url: '/airport/device',
    method: 'post',
    data: data
  })
}

// 修改监控设备
export function updateDevice(data) {
  return request({
    url: '/airport/device',
    method: 'put',
    data: data
  })
}

// 删除监控设备
export function delDevice(deviceId) {
  return request({
    url: '/airport/device/' + deviceId,
    method: 'delete'
  })
}

// 根据设备类型查询设备列表
export function getDevicesByType(deviceType) {
  return request({
    url: '/airport/device/type/' + deviceType,
    method: 'get'
  })
}

// 根据状态查询设备列表
export function getDevicesByStatus(status) {
  return request({
    url: '/airport/device/status/' + status,
    method: 'get'
  })
}

// 获取设备状态统计
export function getDeviceStatistics() {
  return request({
    url: '/airport/device/statistics',
    method: 'get'
  })
}
