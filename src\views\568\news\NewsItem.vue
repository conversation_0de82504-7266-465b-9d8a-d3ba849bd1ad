<template>
  <div>
    <div class="trends_item" @click="handleOpen">
      <div class="trends_left">
        <div class="title">{{ newsItem.title?newsItem.title:newsItem.serveTypeName }}</div>
        <div class="trends_bottom_rows">
          <div class="date">发布人：{{ newsItem.createBy | name }}</div>
        </div>
        <div class="trends_bottom_rows" :class="vuex_uiStyle === 'elder' ? 'elder' : ''">
          <div class="date">{{ newsItem.createTime }}</div>
          <div class="thumb">{{ newsItem.thumbsNum }}人点赞</div>
        </div>
      </div>
      <van-image class="img" fit="cover" :src="newsItem.newsImg || newsImg">
        <!-- @click.stop="handlePreview(newsItem.newsImg)" -->
        <template v-slot:loading>
          <van-loading type="spinner" size="20" />
        </template>
      </van-image>
    </div>
  </div>
</template>

<script>
import newsImg from '@/assets/images/newsImg.jpg'

export default {
  name: 'NewsItem',
  components: {},
  props: {
    newsItem: {
      type: Object,
      required: true
    },
    // 动态索引
    newsIndex: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      newsImg: newsImg
    }
  },
  computed: {},
  watch: {
  },
  created() {

  },
  methods: {
    handleOpen() {
      const scrollTop = document.querySelector('.van-list').scrollTop
      this.$route.meta.scrollTop = scrollTop
      this.$router.push({name: 'NewsDetail', params: {newsId: this.newsItem.id, newsIndex: this.newsIndex}})
    },
  }
}
</script>

<style lang="scss" scoped>
.trends_item {
  display: flex;
  margin: 15px 15px 0;
  padding-bottom: 15px;
  // height: 186px;
  border-bottom: 1px solid #e0e0e0;
  .trends_left{
    flex: 3;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 75%;
    .title {
      font-size: 15px;
      color: #333;
      width: 100%;
      // 单号超出省略
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .trends_bottom_rows{
      display: flex;
      align-items:center;
      font-size: 12px;
      color: #808080;
      .date {
        margin-right: 15px;
      }
      &.elder{
        margin-right: 10px;
        align-items: flex-end;
        justify-content: space-between;
        .date {
          margin-right: 0;
          width: 80px;
        }
      }
    }
  }
  .img {
    flex: 1;
    // width: 100%;
    height: 56px;
    // margin: 11px 0;
    overflow: hidden;
    border-radius: 5px;
    object-fit: cover;
  }

}
</style>
