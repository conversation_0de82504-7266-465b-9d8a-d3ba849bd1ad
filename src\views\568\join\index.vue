<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="568志愿者申请"> </el-page-header>

    <!-- 主要内容区域 -->
    <div class="content-container">
      <!-- 申请表单卡片 -->
      <el-card class="form-card" shadow="none">
        <!-- 申请信息表单 -->
        <el-form
          ref="form"
          :model="user"
          :rules="formRules"
          label-width="120px"
          size="medium"
          @submit.native.prevent="onSubmit"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="您的姓名" prop="volUserName">
                <el-input
                  v-model="user.volUserName"
                  placeholder="请输入姓名"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号码" prop="volUserPhone">
                <el-input
                  v-model="user.volUserPhone"
                  placeholder="请填写手机号码"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="现在身份" prop="job">
                <el-select
                  v-model="user.job"
                  placeholder="请选择现在身份"
                  :disabled="disable"
                  style="width: 100%"
                  @change="onJobChange"
                >
                  <el-option
                    v-for="item in jobTypeList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictLabel"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 服务条款 -->
          <el-form-item prop="checked">
            <el-checkbox v-model="checked" :disabled="disable">
              已阅读并接受
              <el-button type="text" @click="showDialog"
                >《服务条款》</el-button
              >
            </el-checkbox>
          </el-form-item>

          <!-- 提交按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              :loading="aloading"
              :disabled="disable"
              @click="onSubmit"
              size="medium"
            >
              提交申请
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 审批进度步骤条 -->
      <Step v-if="businessId" :vol-approve="volApprove" />
    </div>

    <!-- 服务条款对话框 -->
    <el-dialog
      :visible.sync="clauseShow"
      title="服务条款"
      width="800px"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <div class="info-content">
        <div class="info-item">
          <h4 class="info-title">568简介</h4>
          <p class="info-text">
            568（我来帮）志愿服务队隶属于金华市综合行政执法局站前管理中心，推行"365天志愿服务不落幕"，建设1个志愿站，位于铁路金华站出站口，是金华市志愿服务站示范站点之一。服务队为铁路金华站来往旅客提供家服务、家温暖、家安全。2021年推出"568助老助残直通车"志愿服务项目，为行动不便残疾人、老年人、提供点到点精准服务。
          </p>
        </div>

        <div class="info-item">
          <h4 class="info-title">招募要求</h4>
          <p class="info-text">
            具备相应民事行为能力（未成年人需有监护人或家长陪同）和志愿服务能力，且应身心健康，建议60周岁以下，无不良行为和信用记录，热爱公益事业，遵守志愿者行为规范，熟悉金华火车站的热心市民。
          </p>
        </div>

        <div class="info-item">
          <h4 class="info-title">服务内容</h4>
          <p class="info-text">
            铁路金华站站前区域的咨询问询、交通引导、文明劝导、疫情防控、便民服务等10+X服务。
          </p>
        </div>

        <div class="info-item">
          <h4 class="info-title">服务时间段</h4>
          <p class="info-text">全天分三个时间段，</p>
          <p class="info-text">
            夏令时：上午8：30-11：30，中午11：30-14：30，下午14:30-17：30；
          </p>
          <p class="info-text">
            冬令时：上午8：30-11：30，中午11：30-14：00，下午14:00-17：00。
          </p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="showCancelButton" @click="$router.back()"
          >我再想想</el-button
        >
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { volApply, getUser } from "@/api/home";
import { getCase } from "@/api/568/index";
import Step from "@/components/step";
// import {pvMixin} from '@/util/pvMixin'

export default {
  name: "Join",
  components: {
    Step,
  },
  // mixins: [pvMixin],
  props: {},
  data() {
    return {
      user: {},
      // 条款弹窗显示与隐藏
      clauseShow: false,
      // 显示弹窗的取消按钮
      showCancelButton: true,
      // 服务条款选中状态
      checked: true,
      disable: false,
      // 审批详情
      volApprove: {},
      businessId: "",
      aloading: false,
      jobTypeList: [],
      jobType: "1",
      // 表单验证规则
      formRules: {
        volUserName: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        volUserPhone: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
        ],
        job: [{ required: true, message: "请选择现在身份", trigger: "change" }],
        checked: [
          { required: true, message: "请先勾选服务条款", trigger: "change" },
        ],
      },
    };
  },
  computed: {},
  watch: {},
  async created() {
    // 查字典
    const { data } = await getCase("vol_job_type");
    this.jobTypeList = data;

    this.user = {
      ...this.user,
      volUserId: this.vuex_user_vol_id,
      volUserName: this.aseName(this.vuex_user_realName),
      volUserPhone: this.asePhone(this.vuex_user_mobile),
      job: null,
    };

    this.businessId = this.$route.query.businessId;
    this.$nextTick(() => {
      if (this.businessId) {
        this.handleClick();
      } else {
        this.disable = false;
        this.clauseShow = false;
      }
    });
  },
  mounted() {
    // 埋点处理，计算页面加载完成时间
    // this.pv_getCalcTime('pv_time_t2')
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.back();
    },

    async handleClick() {
      this.clauseShow = false;
      this.disable = true;

      const data = await getUser(this.businessId);

      if (data.code != 200) {
        this.$message.error(data.msg);
        return;
      }
      this.user.job = data.data.job;
      this.volApprove = data.data.volApprove;
    },

    // 身份选择改变
    onJobChange(value) {
      const selectedItem = this.jobTypeList.find(
        (item) => item.dictLabel === value
      );
      if (selectedItem) {
        this.jobType = selectedItem.dictValue;
      }
    },

    // 同意服务条款
    onConfirm() {
      this.checked = true;
      this.clauseShow = false;
    },

    // 点击《服务条款》
    showDialog() {
      this.showCancelButton = false;
      this.clauseShow = true;
    },

    // 提交申请
    async onSubmit() {
      if (!this.checked) {
        this.$message.warning("请先勾选《服务条款》");
        return;
      }

      // 表单验证
      try {
        await this.$refs.form.validate();
      } catch (error) {
        return;
      }

      const params = {
        ...this.user,
        volUserName: this.vuex_user_realName,
        volUserPhone: this.vuex_user_mobile,
        jobType: this.jobType,
      };

      this.$confirm(
        '是否确认成为志愿者？(提交申请后可在"我的-审批事项"中查看进度)',
        "申请成为志愿者",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          this.aloading = true;
          const data = await volApply(params);

          if (data.code != 200) {
            this.$message.error(data.msg);
            this.aloading = false;
            return;
          }

          this.$message.success("已提交申请！");
          this.$router.replace("/index");
          this.aloading = false;
          this.user = {};
          this.jobType = "";
        })
        .catch(() => {
          this.aloading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  max-width: 1200px;
  margin: 20px auto 0;
  min-height: calc(100vh - 50px);
}

// 页面头部样式
::v-deep .el-page-header {
  padding: 0;
  margin-bottom: 30px;

  .el-page-header__content {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .el-page-header__left {
    .el-page-header__back {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 内容容器
.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

// 表单卡片
.form-card {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  border: none !important;

  .card-header {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    color: white;
    padding: 20px;

    .card-title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  ::v-deep .el-card__body {
    padding: 30px;
  }

  // 表单样式
  ::v-deep .el-form {
    .el-form-item {
      margin-bottom: 25px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-input__inner,
      .el-select .el-input__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        transition: all 0.3s;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      .el-checkbox {
        .el-checkbox__label {
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
}

// 服务条款对话框
::v-deep .el-dialog {
  border-radius: 8px;

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    color: white;
    padding: 20px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__close {
      color: white;
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.clause-content {
  .paragraph {
    margin-bottom: 15px;
    line-height: 1.8;
    color: #606266;
    text-align: justify;

    &:last-child {
      margin-bottom: 0;
    }

    .strong {
      font-weight: 600;
      color: #303133;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 15px;
  }

  .content-container {
    max-width: 100%;
  }

  .form-card {
    ::v-deep .el-card__body {
      padding: 20px;
    }

    ::v-deep .el-form {
      .el-row {
        .el-col {
          margin-bottom: 15px;
        }
      }
    }
  }

  // 移动端 PageHeader 样式
  ::v-deep .el-page-header {
    .el-page-header__content {
      font-size: 20px;
    }
  }

  // 移动端对话框
  ::v-deep .el-dialog {
    width: 90% !important;
    margin-top: 5vh !important;
  }
}

.info-content {
  .info-item {
    margin-bottom: 25px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 10px;
      padding-left: 12px;
      border-left: 4px solid #409eff;
    }

    .info-text {
      font-size: 14px;
      line-height: 1.8;
      color: #606266;
      text-align: justify;
      text-indent: 2em;
      margin: 0;
    }
  }
}
</style>
