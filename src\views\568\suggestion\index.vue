<template>
  <div class="suggestion-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="我的建议"> </el-page-header>
    <div class="flex justify-end mb-4">
      <el-button type="primary" size="small" @click="showAddDialog = true">
        <i class="el-icon-plus"></i>
        新增建议
      </el-button>
    </div>
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="建议内容">
          <el-input
            v-model="searchForm.description"
            placeholder="请输入建议内容"
            clearable
            size="small"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleSearch">
            搜索
          </el-button>
          <el-button size="small" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 建议列表 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="suggestionList"
        border
        style="width: 100%"
      >
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="description" label="建议内容" min-width="300" />
        <el-table-column label="图片" width="120">
          <template slot-scope="scope">
            <div
              v-if="scope.row.imgSrcArr && scope.row.imgSrcArr.length > 0"
              class="image-preview"
            >
              <el-image
                v-for="(img, index) in scope.row.imgSrcArr.slice(0, 3)"
                :key="index"
                :src="img"
                :preview-src-list="scope.row.imgSrcArr"
                class="table-image"
                fit="cover"
              />
              <span v-if="scope.row.imgSrcArr.length > 3" class="more-images">
                +{{ scope.row.imgSrcArr.length - 3 }}
              </span>
            </div>
            <span v-else>无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewDetail(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <!-- 新增建议弹窗 -->
    <el-dialog
      title="新增建议"
      :visible.sync="showAddDialog"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="suggestionForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="建议内容" prop="description">
          <el-input
            type="textarea"
            v-model="formData.description"
            placeholder="请输入您的建议内容"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="上传图片">
          <el-upload
            ref="upload"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :file-list="fileList"
            :on-success="handleUploadSuccess"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            list-type="picture-card"
            :limit="9"
            accept="image/*"
          >
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过2MB，最多9张
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="submitSuggestion"
          :loading="submitting"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOffsLists, addOffs } from "@/api/568/offs";
import { getToken } from "@/utils/auth";

export default {
  name: "Suggestion",
  data() {
    return {
      // 列表数据
      suggestionList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,

      // 搜索表单
      searchForm: {
        description: "",
      },

      // 弹窗相关
      showAddDialog: false,
      submitting: false,

      // 表单数据
      formData: {
        description: "",
        fileStr: "",
      },

      // 表单验证规则
      formRules: {
        description: [
          { required: true, message: "请输入建议内容", trigger: "blur" },
          { min: 10, message: "建议内容至少10个字符", trigger: "blur" },
        ],
      },

      // 文件上传
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  created() {
    this.loadSuggestionList();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载建议列表
    async loadSuggestionList() {
      try {
        this.loading = true;

        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          orderByColumn: "createTime",
          isAsc: "desc",
          ...this.searchForm,
        };

        const data = await getOffsLists(params);

        // 处理图片
        if (data.rows) {
          data.rows.forEach((item) => {
            if (item.imgs) {
              item.imgSrcArr = item.imgs.map((value) => {
                return `${process.env.VUE_APP_BASE_API}${value.filePath}`;
              });
            }
          });
          this.suggestionList = data.rows;
          this.total = data.total;
        }
      } catch (error) {
        console.error("获取建议列表失败:", error);
        this.$message.error("获取建议列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleSearch() {
      this.pageNum = 1;
      this.loadSuggestionList();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        description: "",
      };
      this.pageNum = 1;
      this.loadSuggestionList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.loadSuggestionList();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pageNum = val;
      this.loadSuggestionList();
    },

    // 查看详情
    viewDetail(row) {
      this.$message.info("查看详情功能待实现");
    },

    // 关闭弹窗
    handleDialogClose() {
      this.$refs.suggestionForm?.resetFields();
      this.formData = {
        description: "",
        fileStr: "",
      };
      this.fileList = [];
      this.showAddDialog = false;
    },

    // 文件上传成功
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.fileList = fileList;
        // 更新文件字符串
        this.updateFileStr();
      } else {
        this.$message.error(response.msg || "上传失败");
        // 移除失败的文件
        this.fileList = this.fileList.filter((item) => item.uid !== file.uid);
      }
    },

    // 移除文件
    handleRemove(file, fileList) {
      this.fileList = fileList;
      this.updateFileStr();
    },

    // 上传前检查
    beforeUpload(file) {
      const isImage = file.type.indexOf("image/") === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },

    // 更新文件字符串
    updateFileStr() {
      const fileUrls = this.fileList
        .filter((file) => file.response && file.response.code === 200)
        .map((file) => file.response.url);
      this.formData.fileStr = fileUrls.join(",");
    },

    // 提交建议
    async submitSuggestion() {
      try {
        await this.$refs.suggestionForm.validate();

        this.submitting = true;

        await addOffs(this.formData);

        this.$message.success("提交成功");
        this.handleDialogClose();
        this.loadSuggestionList();
      } catch (error) {
        if (error !== false) {
          // 不是表单验证错误
          console.error("提交失败:", error);
          this.$message.error("提交失败，请重试");
        }
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.suggestion-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 84px);

  .search-area {
    margin-bottom: 8px;
    background: #fff;

    .search-form {
      margin: 0;
    }
  }

  .table-container {
    .image-preview {
      display: flex;
      align-items: center;
      gap: 4px;

      .table-image {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        cursor: pointer;
      }

      .more-images {
        font-size: 12px;
        color: #909399;
        margin-left: 4px;
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

// 弹窗样式
::v-deep .el-dialog {
  .el-dialog__header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 30px 20px;
  }

  .el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
  }
}

// 表格样式优化
::v-deep .el-table {
  .el-table__header {
    th {
      background: #f8f9fa;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

// 按钮样式优化
::v-deep .el-button--primary {
  background: #409eff;
  border-color: #409eff;

  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
  }
}
</style>
