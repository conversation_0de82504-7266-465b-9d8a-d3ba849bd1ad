<template>
  <div class="app-container home">
    <div class="welcome-section">
      <h1 class="welcome-title">欢迎使用金华市城市运行管理服务平台</h1>
    </div>

    <div class="service-grid">
      <div class="service-card" @click="navigateTo('/dog')">
        <div class="card-icon">
          <svg-icon icon-class="peoples" class-name="icon-large" />
        </div>
        <div class="card-content">
          <h3 class="card-title">犬类管理</h3>
          <p class="card-description">犬只登记、年审、注销等管理服务</p>
        </div>
        <div class="card-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <div class="service-card" @click="navigateTo('/BrokeTheNews')">
        <div class="card-icon">
          <svg-icon icon-class="message" class-name="icon-large" />
        </div>
        <div class="card-content">
          <h3 class="card-title">我要爆料</h3>
          <p class="card-description">举报违法违规行为，维护社会秩序</p>
        </div>
        <div class="card-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <div class="service-card" @click="navigateTo('/568/index')">
        <div class="card-icon">
          <svg-icon icon-class="star" class-name="icon-large" />
        </div>
        <div class="card-content">
          <h3 class="card-title">568志愿</h3>
          <p class="card-description">志愿服务活动参与和管理平台</p>
        </div>
        <div class="card-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.9.0",
    };
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    },
    navigateTo(path) {
      this.$router.push(path);
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  padding: 20px;
  min-height: calc(100vh - 50px);
  background: linear-gradient(to bottom, #0984e3 100%, #74b9ff 0%);

  .welcome-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;

    .welcome-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .service-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #74b9ff, #0984e3);
    }

    .card-icon {
      margin-right: 20px;
      padding: 16px;
      background: linear-gradient(135deg, #74b9ff, #0984e3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-large {
        font-size: 24px;
        color: white;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin: 0 0 8px 0;
      }

      .card-description {
        font-size: 14px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .card-arrow {
      color: #999;
      font-size: 18px;
      transition: all 0.3s ease;
    }

    &:hover .card-arrow {
      color: #74b9ff;
      transform: translateX(4px);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .welcome-section {
      margin-bottom: 30px;

      .welcome-title {
        font-size: 24px;
      }

      .welcome-subtitle {
        font-size: 14px;
      }
    }

    .service-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .service-card {
      padding: 20px;

      .card-icon {
        margin-right: 16px;
        padding: 12px;

        .icon-large {
          font-size: 20px;
        }
      }

      .card-content {
        .card-title {
          font-size: 18px;
        }

        .card-description {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
