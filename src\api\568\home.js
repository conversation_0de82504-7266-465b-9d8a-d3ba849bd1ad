import request from "@/utils/request";

// 获取当前用户信息和志愿者数量
export function volCount() {
  return request({
    url: "/business/vol/home/<USER>",
    method: "get",
  });
}

// 志愿者申请
export function volApply(data) {
  return request({
    url: "/business/vol/apply/add",
    method: "POST",
    data: data,
  });
}

// 查询志愿者申请详情
export function getUser(userId) {
  return request({
    url: "/business/vol/apply/" + userId,
    method: "get",
  });
}

// 新增站前动态
export function addNews(data) {
  return request({
    url: "/business/vol/news/addByApprove",
    method: "post",
    data: data,
  });
}

// 修改站前动态
export function updateNews(data) {
  return request({
    url: "/business/vol/news/edit",
    method: "post",
    data: data,
  });
}

// 查询站前动态详细
export function getNews(id) {
  return request({
    url: "/business/vol/news/" + id,
    method: "get",
  });
}

// 站前动态分页列表数据
export function newsLists(params) {
  return request({
    url: "/business/vol/news/list",
    method: "get",
    params,
  });
}

// 站前动态详情页
export function getNewsDetail(newsId) {
  return request({
    url: `/business/vol/news/${newsId}`,
    method: "get",
  });
}

// 获取服务预约列表-志愿者、管理员
export function appointment(params) {
  const data = { ...params, orderByColumn: "createTime", isAsc: "desc" };
  return request({
    url: "/business/vol/appointment/list",
    method: "get",
    params: data,
  });
}

// 获取服务预约列表-游客
export function appointmentByUser(params) {
  const data = { ...params, orderByColumn: "createTime", isAsc: "desc" };
  return request({
    url: "/business/vol/appointment/listByVolUser",
    method: "get",
    params: data,
  });
}

// 新增服务预约
export function addAppointment(data) {
  return request({
    url: "/business/vol/appointment/add",
    method: "POST",
    data,
  });
}
// 新增服务记录
export function addAppointmentRecord(data) {
  return request({
    url: "/business/vol/appointment/addAppointment",
    method: "POST",
    data,
  });
}
// 根据id查询服务记录
export function getAppointmentRecord(id) {
  return request({
    url: `/business/vol/appointment/${id}`,
    method: "GET",
  });
}

// 更新服务记录 请求参数比新增多：id:记录id,status:9-更新完结，无法修改
export function editAppointmentRecord(data) {
  return request({
    url: "/business/vol/appointment/editAppointment",
    method: "POST",
    data,
  });
}

// 根据id查询单条服务预约
export function getAppointment(id) {
  return request({
    url: `/business/vol/appointment/${id}`,
    method: "GET",
  });
}

// 获取所有志愿者列表
export function getVolLists(params) {
  return request({
    url: "/business/vol/user/list",
    method: "GET",
    params,
  });
}

// 接受预约
export function editAppointment(data) {
  return request({
    url: "/business/vol/appointment/editAppointment",
    method: "POST",
    data,
  });
}

// 我的任务列表
export function getMyServiceLists(params) {
  return request({
    url: "/business/vol/mine/service",
    method: "GET",
    params,
  });
}

// 生成评价二维码
export function showQRCode(params) {
  return request({
    url: "/business/vol/user/showQRCode",
    method: "GET",
    params,
  });
}

// 获取当日签到记录
export function getSignRecord(params) {
  return request({
    url: "/business/vol/sign/todayRecord",
    method: "GET",
    params,
  });
}

// 志愿者签到
export function signOperation(params) {
  return request({
    url: "/business/vol/sign/operation",
    method: "POST",
    params,
  });
}

// 获取服务监督列表 status:'1'
// export function getTaskLists(params) {
//   return request({
//     url: '/business/vol/task/list',
//     method: 'GET',
//     params
//   })
// }

// 获取服务监督-评价/监督列表
export function getReviewLists(params) {
  return request({
    url: "/business/vol/taskUser/review/list",
    method: "GET",
    params,
  });
}

// 获取服务监督-审核列表
export function gettaskUserLists(params) {
  return request({
    url: "/business/vol/taskUser/list",
    method: "GET",
    params,
  });
}

// 获取加入我们-审核列表
export function getJoinApplyLists(params) {
  return request({
    url: "/business/vol/apply/list",
    method: "GET",
    params,
  });
}

// 获取服务监督-活动审核 / 加入我们审核
export function reviewApply(params) {
  return request({
    url: "/business/approve/zlbReviewApply",
    method: "POST",
    params,
  });
}

// 获取服务监督-提交评价
export function addEvaluate(data) {
  return request({
    url: "/business/vol/evaluate/add",
    method: "POST",
    data,
  });
}

// 获取服务监督-提交评价-更新接口
export function updateEvaluate(data) {
  return request({
    url: "/business/vol/evaluate/edit",
    method: "POST",
    data,
  });
}

// 获取服务监督-提交评价-获取
export function getEvaluate(data) {
  return request({
    url: "/business/vol/evaluate/list",
    method: "GET",
    params: data,
  });
}

// 我的页面-我的评价-评价详情
export function getEvaluateById(id) {
  return request({
    url: `/business/vol/evaluate/${id}`,
    method: "GET",
  });
}

// 获取服务监督-提交监督记录
export function addBreak(data) {
  return request({
    url: "/business/vol/violations/review/add",
    method: "POST",
    data,
  });
}

// 获取 浙里办-志愿任务打卡区域配置
export function getPunchArea(id) {
  return request({
    url: `/business/punchIn/config/detail/${id}`,
    method: "GET",
  });
}

// 助老助残接口
export function addAppointmentDetail(data) {
  return request({
    url: "/business/vol/appointment/detail/add",
    method: "POST",
    data,
  });
}

export function removeAppointmentDetail(id) {
  return request({
    url: "/business/vol/appointment/detail/remove/" + id,
    method: "POST",
  });
}

export function editAppointmentDetail(data) {
  return request({
    url: "/business/vol/appointment/editAppointment",
    method: "POST",
    data,
  });
}

// 查询当前用户是否点赞
export function isThumbs(params) {
  return request({
    url: "/business/vol/thumbs/list",
    method: "GET",
    params,
  });
}

// 新增点赞
export function addThumbs(data) {
  return request({
    url: "/business/vol/thumbs/add",
    method: "POST",
    data,
  });
}

// 更新点赞
export function updateThumbs(data) {
  return request({
    url: "/business/vol/thumbs/update",
    method: "POST",
    data,
  });
}
