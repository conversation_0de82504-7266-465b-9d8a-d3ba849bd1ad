<template>
  <div>
    <el-popover
      v-model="visible"
      class="avatar-container right-menu-item hover-effect"
      popper-class="user-menu"
      :visible-arrow="false"
      width="240"
    >
      <div class="user-menu-bg"></div>
      <div class="user-account-info">
        <img :src="avatar" class="user-menu-avatar" />
        <div class="user-dept-name">{{ $store.getters.info.deptName || ''}}</div>
        <div class="user-account-name">{{ $store.getters.info.nickName }}</div>
      </div>
      <div class="user-menu-divider--horizontal"></div>

      <el-dropdown-item @click.native="openProfile">个人中心</el-dropdown-item>

      <el-dropdown-item
        @click.native="
          setting = true
          visible = false
        "
      >
        <span>布局设置</span>
      </el-dropdown-item>
      <el-dropdown-item divided @click.native="logout">
        <span style="color: #ff7575">退出登录</span>
      </el-dropdown-item>

      <div slot="reference" class="avatar-wrapper">
        <img :src="avatar" class="user-avatar" />
        <!-- <i class="el-icon-caret-bottom" /> -->
        <div class="user-name">
          {{ $store.getters.info.nickName }}
        </div>
      </div>
    </el-popover>
    <el-dialog
      :visible.sync="showProfile"
      title="个人中心"
      width="60vw"
      :close-on-click-modal="false"
    >
      <Profile @success="success" @close="close" />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Profile from '@/views/system/user/profile/index.vue'

export default {
  name: 'UserMenu',
  components: {
    Profile,
  },
  data() {
    return {
      visible: false,
      showProfile: false,
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device', 'name']),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val,
        })
      },
    },
  },
  methods: {
    async logout() {
      this.visible = false
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          if (process.env.NODE_ENV === 'development') {
            this.$router.push('/login?user')
          } else {
            this.$router.push('/login')
          }
        })
      })
    },
    openProfile() {
      this.visible = false
      this.showProfile = true
    },
    success() {
      this.$message.success('修改成功')
      this.showProfile = false
    },
    close() {
      this.showProfile = false
    },
  },
}
</script>

<style lang="scss" scoped>
.user-menu-divider--horizontal {
  margin: 6px 0;
  display: block;
  height: 1px;
  width: 100%;
  background-color: #dcdfe6;
  position: relative;
}
.right-menu-item {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 18px;
  color: #5a5e66;
  vertical-align: text-bottom;

  &.hover-effect {
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
}
.avatar-container {
  margin-right: 30px;

  .avatar-wrapper {
    // margin-top: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .user-avatar {
      cursor: pointer;
      width: 32px;
      height: 32px;
      border-radius: 16px;
      margin: 4px 0;
    }

    .user-name {
      // display: inline;
      text-align: center;
      // height: 40px;
      width: auto;
      padding-left: 6px;
      color: #303133;
      font-size: 14px;
      vertical-align: text-bottom;
    }

    .el-icon-caret-bottom {
      cursor: pointer;
      position: absolute;
      right: -20px;
      top: 25px;
      font-size: 12px;
    }
  }
}
</style>
<style>
.user-menu {
  margin-top: -8px !important;
  padding: 0 0 8px !important;
}
/* .user-menu .el-dropdown-menu__item {
  padding-left: 36px;
} */
.user-menu-bg {
  width: 100%;
  height: 66px;
  background-color: #6bb8ff;
}
.user-account-info {
  padding-left: 32px;
  padding-right: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -32px;
}
.user-menu-avatar {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  line-height: 68px;
  font-size: 16px;
  vertical-align: middle;
}
.user-account-name {
  color: #333;
  font-size: 20px;
  margin-top: 6px;
}
.user-dept-name {
  color: #999;
  margin-top: 16px;
  font-size: 12px;
}
</style>
