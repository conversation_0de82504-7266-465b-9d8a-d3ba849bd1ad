import request from "@/utils/request";

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: "/system/dict/data/type/" + dictType,
    method: "get",
  });
}

// 问题登记
export function addAj(data) {
  return request({
    url: "/zhcg/event",
    method: "post",
    data,
  });
}

// 我要爆料
export function myRevelation(data) {
  return request({
    url: "/zhcg/event/myRevelation",
    method: "post",
    data,
  });
}

// 菜单
export function getYdMenu(params) {
  return request({
    url: "/zhcg/event/getYdMenu",
    method: "get",
    params,
  });
}

// 统计
export function getYdStatistics(type) {
  return request({
    url: `/zhcg/event/getYdStatistics/${type}`,
    method: "get",
  });
}

// 待办任务统计
export function getJdyToDoTask(params) {
  return request({
    url: `/zhcg/event/getJdyToDoTask`,
    method: "get",
    params,
  });
}

//带处置统计
export function getDeptCount(params) {
  return request({
    url: `/zhcg/event/getDeptCount`,
    method: "get",
    params,
  });
}

// 问题列表 isDraft: 1 草稿列表  prestatus 3：待核实 16:待核查
export function getProblemList(params) {
  return request({
    url: "/zhcg/event/list",
    method: "get",
    params,
  });
}

// 问题详情
export function getProblemDetail(id) {
  return request({
    url: `/zhcg/event/${id}`,
    method: "get",
  });
}

// 查询办理经过
export function getBljg(params) {
  return request({
    url: `/zhcg/operate/list`,
    method: "get",
    params,
  });
}

// 新增案件处理
export function addAjcl(data) {
  return request({
    url: "/zhcg/operate",
    method: "post",
    data,
  });
}

// 查询监督员已办任务统计
export function getjdyYbrwTJ(type) {
  return request({
    url: `/zhcg/event/getJdyHaveDoCount/${type}`,
    method: "get",
  });
}

// 查询专业部门已办任务统计
export function getzybmYbrwTj(type) {
  return request({
    url: `/zhcg/event/getZybmHaveDoCount/${type}`,
    method: "get",
  });
}

// 查询监督员已办任务列表
export function getjdyYbrwList(params, type) {
  return request({
    url: `/zhcg/event/getJdyHaveDoList/${type}`,
    method: "get",
    params,
  });
}

// 查询专业部门已办任务列表
export function getzybmYbrwList(params, type) {
  return request({
    url: `/zhcg/event/getZybmHaveDoList/${type}`,
    method: "get",
    params,
  });
}

// 查询惯用语列表
export function getPhraseList(params) {
  return request({
    url: `/zhcg/phrase/list`,
    method: "get",
    params,
  });
}

// 新增惯用语
export function addPhrase(data) {
  return request({
    url: `/zhcg/phrase`,
    method: "post",
    data,
  });
}

// 删除惯用语
export function deletePhrase(ids) {
  return request({
    url: `/zhcg/phrase/${ids}`,
    method: "delete",
  });
}

// 修改惯用语
export function updatePhrase(data) {
  return request({
    url: `/zhcg/phrase`,
    method: "put",
    data,
  });
}

// 查询专项整治任务表列表
export function getSpecialTaskList(params) {
  return request({
    url: `/zhcg/specialTask/list`,
    method: "get",
    params,
  });
}

// 查询专项整治任务详情
export function getSpecialTaskDetail(id) {
  return request({
    url: `/zhcg/specialTask/${id}`,
    method: "get",
  });
}

// 获取案卷档案接口
//任务类型type(String):  1:案件 2:专项整治 3:牛皮鲜 4:四位一体 5：监控抓拍 6：智能抓拍 7：大综合一体化案件 8：黄牛处置 9：日常巡查 10：违规处置 11：一般案件
//开始时间startTime  结束时间endTime  关键词搜索keyWord
export function getCaseList(params) {
  return request({
    url: `/mobile/statistics/caseList`,
    method: "get",
    params,
  });
}

export function getFileList(params) {
  return request({
    url: `/system/file/list`,
    method: "get",
    params,
  });
}

export function dictList(params) {
  return request({
    url: `/system/dict/data/list`,
    method: "get",
    params,
  });
}

//今日统计查询
export function getTodayStatistics(params) {
  return request({
    url: `/system/jrtj/list`,
    method: "get",
    params,
  });
}

// 上报记录
export function getNewsList(params) {
  return request({
    url: "/zhcg/reporter/list",
    method: "get",
    params,
  });
}

// 浙里办上传图片
export function ZlbUploadFile(data) {
  return request({
    url: "/sysUploadFile/uploadFileByBase64",
    method: "post",
    data,
  });
}
