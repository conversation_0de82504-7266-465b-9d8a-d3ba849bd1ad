import request from "@/utils/request";

// 获取书吧首页书籍列表
export function bookLists() {
  return request({
    url: "/business/vol/book/index",
    method: "GET",
  });
}

// 根据书籍id获取书籍详情
export function getDetails(volBookId) {
  return request({
    url: `/business/vol/book/${volBookId}`,
    method: "GET",
  });
}

// 借书送书
export function getRecord(data) {
  return request({
    url: "/business/vol/book/record/add",
    method: "POST",
    data,
  });
}

// 获取我的书籍列表
export function getMyBook() {
  return request({
    url: "/business/vol/book/mine/index",
    method: "GET",
  });
}

// 管理员获取借书所有记录
export function getAllBook(params) {
  return request({
    url: "/business/vol/book/record/list",
    method: "GET",
    params,
  });
}

// 获取借书还书审批列表
export function getApproveLists(params) {
  return request({
    url: "/business/vol/book/review/borrowList",
    method: "GET",
    params,
  });
}

// 借书同意、拒绝审批
export function agreeOrRefuse(params) {
  return request({
    url: "/business/vol/book/review/accept",
    method: "POST",
    params,
  });
}

// 还书
export function returningBook(params) {
  return request({
    url: "/business/vol/book/mine/returningBook",
    method: "POST",
    params,
  });
}

// 还书审批
export function returnBook(params) {
  return request({
    url: "/business/vol/book/review/restore",
    method: "POST",
    params,
  });
}

// 获取分类书籍列表
export function getClassifyBookLists(params) {
  return request({
    url: "/business/vol/book/list",
    method: "GET",
    params,
  });
}
