<template>
  <el-card>
    <section>
      <!--工具条-->
      <el-col :span="24" class="form-line" style="padding-bottom: 0px">
        <el-form size="small" :inline="true" :model="filters">
          <el-form-item label="犬牌编号">
            <el-input
              v-model="filters.petNum"
              placeholder="犬牌编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="饲主名称">
            <el-input
              v-model="filters.ownerName"
              placeholder="饲主名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="犬只名称">
            <el-input
              v-model="filters.petName"
              placeholder="犬只名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="犬牌状态">
            <el-select
              v-model="filters.isReissue"
              clearable
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div style="float: right">
            <el-form-item>
              <el-button type="primary" plain @click="changeSearchType">
                筛选
                <i
                  v-if="searchType2 == 1"
                  class="el-icon-arrow-down el-icon"
                ></i>
                <i v-if="searchType2 == 2" class="el-icon-arrow-up el-icon"></i>
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button-group>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="handleSearch"
                >
                  查询
                </el-button>
                <el-button
                  icon="el-icon-refresh-right"
                  @click="resetParameter"
                >
                  重置
                </el-button>
              </el-button-group>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
      <el-col v-if="searchType2 == 2" :span="24" class="form-line">
        <el-form size="small" :inline="true" :model="filters">
          <el-form-item label="犬只性别">
            <el-select
              v-model="filters.petSex"
              clearable
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in petSexs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="犬只年龄">
            <el-input
              v-model="filters.petAge"
              type="number"
              placeholder="犬只年龄"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-col>
      <!--列表-->
      <template>
        <el-table
          v-loading="loading"
          border
          :data="data"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            prop="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column
            label="犬牌编号"
            :formatter="formats"
            sortable
          ></el-table-column>
          <el-table-column
            prop="ownerName"
            label="饲主名称"
            sortable
          ></el-table-column>
          <el-table-column
            prop="petName"
            label="犬只名称"
            sortable
          ></el-table-column>
          <el-table-column
            prop="petSex"
            label="犬只性别"
            :formatter="formatPetSex"
            sortable
          ></el-table-column>
          <el-table-column
            prop="petAge"
            label="犬只年龄"
            :formatter="formatPetAge"
            sortable
          ></el-table-column>
          <el-table-column
            prop="isReissue"
            label="犬牌状态"
            :formatter="formatIsReissue"
            sortable
          ></el-table-column>
          <el-table-column
            align="center"
            label="操作"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                class="btn-text-pd0"
                type="text"
                @click="handleEdit(scope.row.id, 1)"
              >
                详情
              </el-button>
              <!-- 犬牌审核通过之后才可以补办-->
              <el-button
                v-if="
                  (!scope.row.isReissue || scope.row.isReissue == '2') &&
                  scope.row.petNum
                "
                class="btn-text-pd0"
                type="text"
                @click="handleEdit(scope.row.id, 2, scope.row.endDate)"
              >
                申请
              </el-button>
              <el-button
                v-if="scope.row.isReissue === 3"
                class="btn-text-pd0"
                type="text"
                @click="handleEdit(scope.row.id, 3)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!--工具条-->
      <el-pagination
        :current-page="filters.pageNum"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="filters.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        style="padding: 15px 5px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
      <el-dialog
        :title="title"
        :visible.sync="formVisible"
        :close-on-click-modal="false"
        :before-close="cancelForm"
      >
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="补办信息" name="first">
            <el-form
              ref="rsissue"
              :model="rsissue"
              :rules="formRules"
              label-width="150px"
            >
              <div style="padding-top: 10px">
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item v-if="fileList.length > 0" label="宠物照片">
                      <el-image
                        v-for="(item, index) in fileList"
                        :key="index"
                        :src="item.url"
                        :preview-src-list="fileList.map(img => img.url)"
                        class="w-[100px] h-[100px] mr-2"
                        fit="cover"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="饲主姓名" prop="ownerName">
                      <el-input
                        v-model="form.ownerName"
                        disabled
                        auto-complete="off"
                        placeholder="请输入饲主姓名"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="身份证" prop="petIdCard">
                      <el-input
                        v-model="form.petIdCard"
                        disabled
                        auto-complete="off"
                        placeholder="请输入饲主身份证号"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="户籍地址" prop="ownerAddress">
                      <el-input
                        v-model="form.ownerAddress"
                        disabled
                        auto-complete="off"
                        placeholder="请输入户籍地址"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="tel">
                      <el-input
                        v-model="form.tel"
                        disabled
                        auto-complete="off"
                        placeholder="请输入饲主联系电话"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="身份证照片（正面）" prop="posiCard">
                      <el-image
                        v-if="posiCardPath"
                        :src="form.posiCard"
                        :preview-src-list="[form.posiCard]"
                        fit="cover"
                        class="w-[200px] h-[100px] block"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="身份证照片（反面）" prop="sideCard">
                      <el-image
                        v-if="sideCardPath"
                        :src="form.sideCard"
                        :preview-src-list="[form.sideCard]"
                        fit="cover"
                        class="w-[200px] h-[100px] block"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="户口本照片" prop="residence">
                      <el-image
                        v-for="(item, index) in residenceImgList"
                        :key="index"
                        :src="item.url"
                        :preview-src-list="residenceImgList.map(img => img.url)"
                        class="el-upload-list__item-thumbnail rounded-md w-[148px] h-[148px] mr-2"
                        fit="cover"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="房产证/租赁合同照片" prop="property">
                      <el-image
                        v-for="(item, index) in propertyImgList"
                        :key="index"
                        :src="item.url"
                        :preview-src-list="propertyImgList.map(img => img.url)"
                        class="el-upload-list__item-thumbnail rounded-md w-[148px] h-[148px] mr-2"
                        fit="cover"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="宠物名字" prop="petName">
                      <el-input
                        v-model="form.petName"
                        disabled
                        auto-complete="off"
                        placeholder="请输入犬只名称"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="宠物性别" prop="petSex">
                      <el-select
                        v-model="form.petSex"
                        :disabled="disabled"
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in petSexs"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="宠物品种" prop="petVarieties">
                      <el-select
                        v-model="form.petVarieties"
                        :disabled="disabled"
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in petVarieties"
                          :key="item.dictKey"
                          :label="item.name"
                          :value="item.dictKey"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="form.petVarieties === '107'" :span="12">
                    <el-form-item label="其他品种" prop="otherVarieties">
                      <el-input
                        v-model="form.otherVarieties"
                        :disabled="disabled"
                        auto-complete="off"
                        placeholder="请输入其他品种名称"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="宠物毛色" prop="petHair">
                      <el-select
                        v-model="form.petHair"
                        :disabled="disabled"
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in petHairs"
                          :key="item.dictKey"
                          :label="item.name"
                          :value="item.dictKey"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="宠物年龄" prop="petAge">
                      <el-input
                        v-model="form.petAge"
                        type="number"
                        disabled
                        auto-complete="off"
                        placeholder="请输入犬只年龄"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="饲养日期" prop="raiseDate">
                      <el-date-picker
                        v-model="form.raiseDate"
                        style="width: 100%"
                        disabled
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期"
                      ></el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="养宠地址" prop="petDept">
                      <div style="display: flex; align-items: center">
                        <div style="width: 110px">浙江省/金华市/</div>
                        <el-cascader
                          :key="modalKey"
                          v-model="area"
                          style="flex: 1"
                          :disabled="disabled"
                          :options="options"
                          :props="cateProps"
                          clearable
                        ></el-cascader>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="" prop="petAddress">
                      <el-input
                        v-model="form.petAddress"
                        type="textarea"
                        disabled
                        auto-complete="off"
                        placeholder="请输入详细地址"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="旧犬牌号" prop="oldPetNum">
                      <el-input
                        v-model="form.petNum"
                        disabled
                        auto-complete="off"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="新犬牌号" prop="dogBrand">
                      <el-select
                        v-model="rsissue.dogBrand"
                        filterable
                        placeholder="请选择"
                        style="width: 100%"
                        clearable
                        :disabled="disabled || numFlag"
                      >
                        <el-option
                          v-for="item in petNumArray"
                          :key="item.brandNum"
                          :label="item.brandNum"
                          :value="item.brandNum"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="用途方式" prop="type">
                      <el-select
                        v-model="rsissue.type"
                        :disabled="disabled"
                        style="width: 100%"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in types"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col
                    v-if="rsissue.type === 2 || rsissue.type === 3"
                    :span="12"
                  >
                    <el-form-item :label="label">
                      <el-image
                        v-if="idlower"
                        :src="rsissue.fileUrl"
                        :preview-src-list="[rsissue.fileUrl]"
                        class="w-[200px] h-[100px]"
                        fit="cover"
                      />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="rsissue.sendType !== 0" :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="寄送方式" prop="sendType">
                      <el-select
                        v-model="rsissue.sendType"
                        :disabled="disabled"
                        style="width: 100%"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in sendTypes"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="rsissue.sendType === 1" :span="12">
                    <el-form-item label="费用到付" prop="isCollect">
                      <el-select
                        v-model="rsissue.isCollect"
                        :disabled="disabled"
                        style="width: 100%"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in isCollects"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="rsissue.sendType === 1" :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="收件人" prop="phone">
                      <el-input
                        v-model="rsissue.name"
                        :readonly="disabled"
                        auto-complete="off"
                        placeholder="请输入收件人"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系方式" prop="phone">
                      <el-input
                        v-model="rsissue.phone"
                        type="number"
                        :readonly="disabled"
                        auto-complete="off"
                        placeholder="请输入联系方式"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="rsissue.sendType !== 0" :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="收货地址" prop="area">
                      <el-input
                        v-model="rsissue.area"
                        :readonly="disabled"
                        auto-complete="off"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="详细地址" prop="address">
                      <el-input
                        v-model="rsissue.address"
                        :readonly="disabled"
                        auto-complete="off"
                        placeholder="请输入收货地址"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="rsissue.status === 3 || disabled" :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="审核意见" prop="reason">
                      <el-input
                        v-model="rsissue.reason"
                        readonly
                        auto-complete="off"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="备注" prop="content">
                      <el-input
                        v-model="rsissue.content"
                        type="textarea"
                        :readonly="disabled"
                        auto-complete="off"
                        placeholder="请输入备注"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </el-tab-pane>
          <el-tab-pane v-if="type == '1'" label="操作记录" name="two">
            <el-table
              v-loading="loading"
              border
              :data="recordList"
              highlight-current-row
              style="width: 100%"
            >
              <el-table-column
                prop="createName"
                label="操作人"
              ></el-table-column>
              <el-table-column
                prop="createDate"
                label="操作时间"
              ></el-table-column>
              <el-table-column
                prop="oldPetNum"
                label="旧犬牌号"
              ></el-table-column>
              <el-table-column prop="petNum" label="新犬牌号"></el-table-column>
              <el-table-column
                label="类型"
                :formatter="formatNode"
              ></el-table-column>
              <el-table-column
                label="状态"
                :formatter="formatRecordStatus"
              ></el-table-column>
              <el-table-column
                prop="remark"
                label="原因/审批意见"
              ></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <div slot="footer" class="dialog-footer">
          <el-button size="medium" @click.native="cancelForm">取消</el-button>
          <el-button
            v-if="type === 2 || type === 3"
            size="medium"
            type="primary"
            :loading="formLoading"
            @click.native="submitForm()"
          >
            提交申请
          </el-button>
        </div>
      </el-dialog>

      <div v-show="photoVisible" class="showPhoto" @click="closeClick">
        <img class="img" :src="bigImgUrl" alt="图片加载失败" />
      </div>
    </section>
  </el-card>
</template>
<script>
/* eslint-disable */
import { base, doPost, getDownLoadUrl } from '@/api/dog/index'

export default {
  data() {
    return {
      searchType2: 1,
      filters: {
        brandNum: '',
        brandCity: '',
        brandCom: '',
        isRecovery: '',
        isUse: '',
        status: '',
      },
      type: '',
      loading: false,
      data: [],
      total: 0,
      form: {
        id: '',
        sNum: '',
        eNum: '',
        brandNum: '',
        brandCity: '',
        brandCom: '',
        offDate: '',
        remark: '',
        offCom: '',
        isRecovery: 1,
        isUse: 1,
        uploadFiles: [],
      },
      rsissue: {
        petId: '', //犬只ID
        type: 1,
        sendType: 0,
        name: '',
        phone: '',
        area: '',
        address: '',
        isCollect: 0,
        reason: '',
        fileUrl: '',
        dogBrand: '',
        hospitalId: '',
      },
      formVisible: false,
      title: '',
      formRules: {
        type: [{ required: true, message: '请选择用途方式', trigger: 'blur' }],
        sendType: [
          { required: true, message: '请选择寄送方式', trigger: 'blur' },
        ],
        isCollect: [
          { required: true, message: '请选择费用到付', trigger: 'blur' },
        ],
        dogBrand: [
          { required: true, message: '请选择犬牌号', trigger: 'blur' },
        ],
      },
      disabled: false,
      numFlag: false,
      formLoading: false,
      user: {},
      brandCitys: [],
      petHairs: [],
      petVarieties: [],
      petSexs: [
        { value: '1', label: '雄' },
        { value: '2', label: '雌' },
      ],
      options: [], // 地区下拉
      petNumArray: [], // 犬牌下拉
      label: '',
      srcList: [],
      types: [
        { label: '观赏', value: 1 },
        { label: '导盲', value: 2 },
        { label: '辅助', value: 3 },
        { label: '其他', value: 4 },
      ],
      sendTypes: [
        { label: '快递', value: 1 },
        { label: '自取', value: 2 },
      ],
      isCollects: [
        { label: '是', value: 1 },
        { label: '否', value: 2 },
      ],
      statusList: [
        { label: '待审核', value: '1' },
        { label: '已通过', value: '2' },
        { label: '未通过', value: '3' },
      ],
      uploadUrl: base + '/sysUploadFile/uploadFile', // 上传附件
      idlower: false,
      fileList: [],
      cateProps: {
        label: 'deptName',
        children: 'children',
        value: 'id',
      },
      recordList: [],
      nodeList: [
        { dictKey: 4, name: '犬牌补办' },
        { dictKey: 5, name: '补办审核' },
        { dictKey: 7, name: '犬证激活' },
      ],
      recordStatusList: [
        { dictKey: 1, name: '已提交' },
        { dictKey: 2, name: '已通过' },
        { dictKey: 3, name: '未通过' },
        { dictKey: 4, name: '已激活' },
      ],
      activeName: 'first',
      propertyImgList: [], //户口本照片集合
      residenceImgList: [], //租房合同照片集合
      area: [],
      residencePath: '',
      propertyPath: '',
      posiCardPath: '',
      sideCardPath: '',
      petTypes: [], // 宠物类别
      modalKey: 0,
      photoVisible: false,
      bigImgUrl: '',
    }
  },
  methods: {
    showBigImage(e) {
      //点击图片函数，点击后，把photoVisible设置成true
      if (e != '') {
        this.photoVisible = true
        this.bigImgUrl = e.currentTarget.src
      }
    },
    closeClick() {
      this.photoVisible = false
    },
    //筛选展开
    changeSearchType() {
      if (this.searchType2 === 1) {
        this.searchType2 = 2
      } else {
        this.searchType2 = 1
      }
    },
    resetParameter() {
      this.filters = {}
      this.getPgaeList()
    },
    // 分页
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.getPgaeList()
    },
    handleCurrentChange(val) {
      this.filters.pageNum = val
      this.getPgaeList()
    },
    handleSearch() {
      this.filters.pageNum = 1
      this.getPgaeList()
    },
    // 获取列表
    getPgaeList: function () {
      this.loading = true
      this.filters.agencyCom = this.user.userQualifi
        ? this.user.userQualifi.id
        : ''
      doPost('/petCertificates/getPageList', this.filters).then((res) => {
        this.data = res.list
        if (this.data !== null && this.data.length > 0) {
          for (var a in this.data) {
            this.data[a].index =
              (res.pageNum - 1) * res.pageSize + parseInt(a) + 1
          }
        }
        this.total = res.total
      })
      this.loading = false
    },
    getRecord(id) {
      doPost('/petRecord/getList', { petId: id }).then((res) => {
        if (res.length > 0) {
          this.recordList = res.filter((item) => {
            return item.node === 4 || item.node === 5 || item.node === 7
          })
        } else {
          this.recordList = []
        }
      })
    },
    //编辑
    handleEdit: function (id, type, endDate) {
      var that = this
      if (type === 1) {
        that.title = '申请详情'
        that.disabled = true

        that.getRsissueByPet(id)
        that.getRecord(id)
      } else if (type === 2) {
        let oDate1 = new Date(endDate)
        let oDate2 = new Date()
        if (oDate1.getTime() < oDate2.getTime()) {
          this.$message({
            message: '免疫已经过期，不可申请补办犬牌！',
            type: 'warning',
          })
          return
        }
        that.disabled = false
        that.title = '犬牌补办申请'
        that.getPetNum()
      } else if (type === 3) {
        that.disabled = false
        that.title = '犬牌补办编辑'
        that.getRsissueByPet(id)
      }
      that.type = type
      that.formVisible = true
      let para = {
        id: id,
      }
      that.modalKey++
      doPost('/petCertificates/getById', para).then((res) => {
        that.form = res
        that.form = res
        that.area = [that.form.petDept, that.form.street]
        const fileList = that.form.uploadFiles
        for (const i in fileList) {
          if (
            fileList[i].modelType == 'petImgZ' ||
            fileList[i].modelType == 'petImgF'
          ) {
            that.fileList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'residence') {
            //户口本照片集合
            that.residenceImgList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'property') {
            //房产证/租赁合同照片集合
            that.propertyImgList.push({
              url: getDownLoadUrl(fileList[i].fileUrl),
            })
          } else if (fileList[i].modelType == 'posiCard') {
            that.posiCardPath = fileList[i].fileUrl
            that.$set(
              that.form,
              'posiCard',
              getDownLoadUrl(fileList[i].fileUrl)
            )
          } else if (fileList[i].modelType == 'sideCard') {
            that.sideCardPath = fileList[i].fileUrl
            that.$set(
              that.form,
              'sideCard',
              getDownLoadUrl(fileList[i].fileUrl)
            )
          }
        }
        delete that.form.uploadFiles
      })
    },
    //点击取消(处理规则校验)
    cancelForm() {
      this.formVisible = false
      this.numFlag = false
      this.activeName = 'first'
      this.fileList = []
      this.propertyImgList = [] //户口本照片集合
      this.residenceImgList = [] //租房合同照片集合
      this.disabled = false
      this.rsissue = this.$options.data().rsissue
      this.form = this.$options.data().form
      this.$refs['rsissue'].resetFields()
    },
    //提交
    submitForm: function () {
      this.form.isReissue = 1
      console.log(this.rsissue.area)
      this.$refs.rsissue.validate((valid) => {
        if (valid) {
          this.$confirm('确认提交吗', '提示', {
            confirmButtonText: '继续提交',
            cancelButtonText: '取消',
          }).then(() => {
            this.formLoading = true
            let para = {
              id: this.rsissue.id,
              petId: this.form.id,
              userId: this.user.id,
              qualifiId: this.user.userQualifi ? this.user.userQualifi.id : '',
              brandNum: this.form.petNum,
              dogBrand: this.rsissue.dogBrand,
              type: this.rsissue.type,
              sendType: this.rsissue.sendType,
              content: this.rsissue.content,
              name: this.rsissue.name,
              phone: this.rsissue.phone,
              address: this.rsissue.address,
              isCollect: this.rsissue.isCollect,
              fileUrl: this.rsissue.fileUrl,
              status: 1,
              area: this.rsissue.area ? this.rsissue.area.join(',') : '',
            }
            console.log(para)
            doPost('/reissue/saveOrUpdate', para).then((res) => {
              this.formLoading = false
              this.$message({
                message: '提交成功',
                type: 'success',
              })
              this.$refs['rsissue'].resetFields()
              this.formVisible = false
              this.getPgaeList()
            })
          })
        }
      })
    },
    getPetNum() {
      console.log('getPetNum');
      if (this.user.userType == 2) {
        var param = {
          brandCom: this.user?.userQualifi?.id || '',
          brandNum: this.rsissue.dogBrand,
        }
        doPost('/petBrand/getAllList', param).then((res) => {
          this.petNumArray = res
        })
      }
    },

    formatPetSex: function (row, column) {
      if (row.petSex === 1) {
        return '雄'
      } else if (row.petSex === 2) {
        return '雌'
      }
    },
    formatPetAge: function (row, column) {
      return row.petAge + '岁'
    },
    formatIsReissue: function (row, column) {
      if (row.isReissue === 1) {
        return '待审核'
      } else if (row.isReissue === 2) {
        return '已通过'
      } else if (row.isReissue === 3) {
        return '未通过'
      } else {
        return ''
      }
    },
    formats: function (row, column) {
      if (
        row.petNum !== null &&
        row.petNum !== undefined &&
        row.petNum !== ''
      ) {
        return row.petNum
      }
      return '暂无犬牌'
    },

    //列表显示
    formatNode: function (row, column) {
      let str = ''
      this.nodeList.forEach(function (item) {
        if (item.dictKey == row.node) {
          str = item.name
        }
      })
      return str
    },
    formatRecordStatus: function (row, column) {
      let str = ''
      this.recordStatusList.forEach(function (item) {
        if (item.dictKey == row.status) {
          str = item.name
        }
      })
      return str
    },

    getHairType() {
      doPost('/sysDict/getAllList', { dictType: 'varieties_type' }).then(
        (res) => {
          this.petVarieties = res
        }
      )
      doPost('/dept/getSecondDeptTree', {}).then((res) => {
        this.options = res
      })
      doPost('/sysDict/getAllList', { dictType: 'hair_type' }).then((res) => {
        this.petHairs = res
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 10

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是JPG格式或PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
      }
      return (isJPG || isPNG) && isLt2M
    },
    // 照片保存
    handleSuccess(file, fileList) {
      this.rsissue.fileUrl = getDownLoadUrl(file.result)
      this.rsissue.doorwayName = file.name
      this.idlower = file.result
    },
    getRsissueByPet(petId) {
      var that = this
      let para = {
        petId: petId,
      }
      doPost('/reissue/getByPetId', para).then((res) => {
        if (res) {
          if (this.type === 1) {
            that.rsissue = res
            that.idlower = that.rsissue.fileUrl
          } else {
            that.rsissue.id = res.id
            that.rsissue.dogBrand = res.dogBrand
            that.rsissue.type = res.type
            that.idlower = res.fileUrl
          }
          if (that.type === 3) {
            if (this.rsissue.dogBrand) {
              this.numFlag = true
            }
            that.getPetNum()
          }
        }
      })
    },
  },
  mounted() {
    var user = JSON.parse(sessionStorage.getItem('user'))
    this.user = user
    this.getPgaeList()
    this.getHairType()
  },
}
</script>

<style scoped>
.img-box img {
  display: block;
  width: 100%;
  height: auto;
}

:deep(.avatar-uploader .el-upload) {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 100px;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: #409eff;
}

:deep(.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

:deep(.avatar) {
  width: 200px;
  height: 100px;
  display: block;
}

.showPhoto {
  position: fixed;
  top: 10%;
  width: 80%;
  height: 80%;
  z-index: 99999;
  display: flex;
}

.showPhoto .img {
  display: block;
  margin: auto 0;
  max-width: 100%;
  max-height: 100%;
  text-align: center;
}
</style>
