const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 创建 scripts 目录（如果不存在）
const scriptsDir = path.dirname(__filename);
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir, { recursive: true });
}

// 配置
const distPath = path.resolve(__dirname, '../dist');
const outputPath = path.resolve(__dirname, '../dist.zip');

// 检查 dist 目录是否存在
if (!fs.existsSync(distPath)) {
  console.error('错误: dist 目录不存在，请先运行构建命令');
  process.exit(1);
}

// 创建压缩包
console.log('开始创建压缩包...');

const output = fs.createWriteStream(outputPath);
const archive = archiver('zip', {
  zlib: { level: 9 } // 设置压缩级别
});

// 监听错误事件
output.on('close', function() {
  console.log(`压缩包创建成功: ${outputPath}`);
  console.log(`总大小: ${archive.pointer()} bytes`);
});

archive.on('error', function(err) {
  console.error('压缩过程中出现错误:', err);
  process.exit(1);
});

// 连接输出流
archive.pipe(output);

// 添加 dist 目录中的所有文件
archive.directory(distPath, false);

// 完成压缩
archive.finalize();
