<template>
  <div class="add-serve-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="serve-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务者名称" prop="volUserName">
            <el-input
              v-model="form.volUserName"
              placeholder="请输入服务者名称"
              :disabled="disable"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务时间" prop="happenTime">
            <el-date-picker
              v-model="form.happenTime"
              type="datetime"
              placeholder="选择服务时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 100%"
              :disabled="disable"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务类型" prop="serveType">
            <el-select
              v-model="form.serveType"
              placeholder="请选择服务类型"
              style="width: 100%"
              :disabled="disable"
              @change="handleServeTypeChange"
            >
              <el-option
                v-for="item in serveTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务概述" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入服务概述"
              :disabled="disable"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="服务内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入详细服务描述..."
          maxlength="500"
          show-word-limit
          :disabled="disable"
        />
      </el-form-item>

      <!-- 图片上传 -->
      <el-form-item label="服务图片">
        <el-upload
          ref="upload"
          :file-list="fileList"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          :disabled="disable"
          list-type="picture-card"
          :limit="9"
          accept="image/*"
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">
            只能上传jpg/png文件，且不超过2MB，最多9张图片
          </div>
        </el-upload>
      </el-form-item>

      <!-- 审批进度步骤条 -->
      <el-form-item v-if="businessId" label="审批进度">
        <Step :vol-approve="volApprove" />
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '提交' }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { addNews, getNews } from '@/api/568/home'
import { uploadPic, downloadFile,getCase } from '@/api/568/index'
import dayjs from 'dayjs'
import Step from '@/components/step'
import { getToken } from '@/utils/auth'

export default {
  name: 'AddServeForm',
  components: {
    Step
  },
  props: {
    formData: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 表单数据
      form: {
        volUserName: '',
        happenTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        serveType: '',
        title: '',
        content: ''
      },
      // 表单验证规则
      rules: {
        volUserName: [
          { required: true, message: '请输入服务者名称', trigger: 'blur' }
        ],
        happenTime: [
          { required: true, message: '请选择服务时间', trigger: 'change' }
        ],
        serveType: [
          { required: true, message: '请选择服务类型', trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入服务概述', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入服务内容', trigger: 'blur' }
        ]
      },
      // 服务类型选项
      serveTypeOptions: [],
      // 图片文件列表
      fileList: [],
      // 上传相关
      uploadAction: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      uploadData: {},
      // 控制状态
      disable: false,
      submitLoading: false,
      // 编辑相关
      id: '',
      businessId: '',
      volApprove: {},
      dataId: 0,
      deleteIds: []
    }
  },
  computed: {},
  watch: {
    formData: {
      handler(newVal) {
        if (newVal) {
          this.initFormData();
        }
      },
      immediate: true
    }
  },
  async created() {
    // 加载服务类型字典
    await this.loadServeTypes();

    // 初始化表单数据
    this.initFormData();
  },
  methods: {
    // 加载服务类型字典
    async loadServeTypes() {
      try {
        const { data } = await getCase('vol_appointment_type');
        this.serveTypeOptions = data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
      } catch (error) {
        console.error('加载服务类型失败:', error);
      }
    },

    // 初始化表单数据
    initFormData() {
      if (this.isEdit && this.formData) {
        // 编辑模式，填充表单数据
        this.form = {
          volUserName: this.formData.volUserName || '',
          happenTime: this.formData.happenTime || '',
          serveType: this.formData.serveType || '',
          title: this.formData.title || '',
          content: this.formData.content || ''
        };
        this.id = this.formData.id;
        this.businessId = this.formData.businessId;
        this.disable = true;

        // 加载图片
        if (this.id) {
          this.downloadFiles();
        }
      } else {
        // 新建模式，设置默认值
        this.form.volUserName = this.$store.getters.name || '';
        this.disable = false;
      }
    },

    // 服务类型改变
    handleServeTypeChange(value) {
      // 可以在这里添加额外的逻辑
    },
    // 新增/提交服务记录
    onSubmit() {
      this.$dialog.confirm({
        title: '提交服务记录',
        message: '是否确认提交服务记录？(提交后可在“我的-审批事项”中查看进度)',
        closeOnClickOverlay: true
      }).then(async() => {
        if (this.fileList.length == 0) return this.$toast('图片不能为空！')
        this.aloading = true

        this.$toast.loading({ message: '加载中...', duration: 0, forbidClick: true })

        // if (!this.id) {
        // 新增服务记录

        // 上传除图片外的其他信息
        if (this.dataId == 0) {
          // type 1-服务记录 2-我要赞美 3-站前动态
          // status:1-新建，2-审批中，9-同意
          const params = {...this.form, serveType: this.serveType, volUserId: this.vuex_user_vol_id, volUserName: this.vuex_user_realName, status: 2, type: 1}
          const data = await addNews(params).catch(() => {
            this.$toast.fail({
              message: '网络异常，请重试！'
            })
            this.aloading = false
          })

          if (data.code != 200) return
          this.dataId = data.data.id
        }

        // 上传图片
        if (this.fileList.length) {
          let fd = new FormData()
          this.fileList.forEach(v => {
            fd.append('files', v.file)
          })
          fd.append('businessId', this.dataId)
          fd.append('tableName', 'vol_news')
          fd.append('status', 2)

          const uploadPicData = await uploadPic(fd).catch(() => {
            this.$toast.fail({
              message: '图片上传失败，请重新提交'
            })
            this.aloading = false
          })

          if (!uploadPicData || uploadPicData.code != 200) return
        }

        this.$toast.success('添加成功！')
        this.form = {}
        this.serveType = ''
        this.fileList = []
        // } else {
        //   // 更新服务记录

        //   // type 1-服务记录 2-我要赞美 3-站前动态
        //   // status:1-新建，2-审批中，9-同意
        //   const param = {...this.form, serveType: this.serveType, volUserId: this.volUserId, volUserName: this.userName, status: 2, type: 1}
        //   // 上传除图片外的其他信息
        //   const editData = await updateNews(param).catch(() => {
        //     this.$toast.fail({
        //       message: '网络异常，请重试！'
        //     })
        //   })

        //   if (editData.code != 200) return

        //   // 更新图片...
        //   // 删除数据库图片
        //   if (this.deleteIds.length) {
        //     let deletePicData = await deleteFile(this.deleteIds.join()).catch(() => {
        //       this.$toast.fail({
        //         message: '图片删除失败，请重新提交'
        //       })
        //     })

        //     if (!deletePicData || deletePicData.code != 200) return
        //     this.deleteIds = []
        //   }
        //   const uploadFileList = this.fileList.filter(item => item.file)
        //   if (uploadFileList.length) {
        //     // 上传图片到数据库
        //     const fd = new FormData()
        //     uploadFileList.forEach(item => {
        //       if (item.file) fd.append('files', item.file)
        //     })
        //     console.log(editData.data.id)
        //     fd.append('businessId', editData.data.id)
        //     fd.append('tableName', 'vol_news')
        //     fd.append('status', 2)

        //     const uploadPicData = await uploadPic(fd).catch(() => {
        //       this.$toast.fail({
        //         message: '图片上传失败，请重新提交'
        //       })
        //     })

        //     if (!uploadPicData || uploadPicData.code != 200) return
        //   }
        //   this.$toast.success('更新成功！')
        //   this.form = {}
        //   this.serveType = ''
        //   this.fileList = []
        // }
        this.$bus.$emit('reLoadserveList')
        this.$router.back()
        this.aloading = false
      }).catch(() => this.aloading = false)

    },
    // 根据id查询服务记录
    async loadAppointmentRecord() {
      let id = this.id
      if (this.businessId) id = this.businessId
      const data = await getNews(id).catch(() => {
        this.$toast.fail({
          message: '网络异常，请重试！',
          onClose: () => {
            this.$router.back()
          }
        })
      })

      if (data.code != 200) return

      this.serveItem = data.data
      this.volApprove = data.data.volApprove

      this.volUserId = this.serveItem.volUserId
      this.userName = this.serveItem.volUserName
      this.form.volUserName = this.aseName(this.serveItem.volUserName)
      this.form.happenTime = this.serveItem.happenTime
      this.serveType = this.serveItem.serveType
      const columnObj = this.columns.find(v => v.value == this.serveType)
      this.form.serveTypeName = columnObj.text
      this.form.content = this.serveItem.content
      this.form.title = this.serveItem.title
    },
    // 获取图片
    async downloadFiles() {
      let id = this.id
      if (this.businessId) id = this.businessId
      const data = await downloadFile({
        businessId: id,
        tableName: 'vol_news'
      }).catch(() => {
        this.$toast.fail({
          message: '网络异常，请重试！'
        })
      })
      if (!data || data.code != 200) return

      this.fileList = data.rows.map(item => {
        return  {url: `${process.env.VUE_APP_BASE_API}${item.filePath}?fileId=${item.fileId}`}
      })
    },
    // 删除图片的回调
    deleteImg(val) {
      return new Promise((resolve, reject) => {
        this.$dialog.confirm({
          title: '删除图片',
          message: '是否确认删除该张图片？'
        })
          .then(() => {
            // on confirm
            if (val.url) {
              this.fileList = this.fileList.filter(item => {
                return item != val
              })
              this.deleteIds.push(val.url.split('?fileId=')[1])
            }
            resolve()
          })
          .catch(reject)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add_offs_container {
  background: #f6f6f8;
  min-height: 100vh;
  padding-bottom: 57px;
  .van-form {
    // margin-bottom: 50px;
    .cell {
      height: 44px;
    }
    .van-cell {
      padding: 12px 17px 12px 15px;
    }
    .message {
      display: flex;
      flex-direction: column;
      margin-top: 11px;
      padding: 8px 15px 7px;
      // height: 148px;
      ::v-deep.van-field__error-message {
        position: absolute;
        top: 22px;
        font-size: 15px;
      }
      ::v-deep .van-field__word-limit {
        margin-top: 16px;
        color: #9b9b9b;
      }
    }
    ::v-deep .van-field__label {
      color: #666;
      font-size: 15px;
    }
    ::v-deep .van-field__control {
      font-size: 15px;
      color: #333;
    }
    // 上传图片
    .updata {
      margin-bottom: 10px;
      width: 100%;
      // height: 100px;
      background: #fff;
      padding: 0 15px;
      ::v-deep .van-uploader__preview {
        width: 75px;
        height: 75px;
        margin-right: 15px;
        margin-bottom: 15px;
        &:nth-child(4n) {
          margin-right: 0;
        }
        .van-uploader__preview-image {
          width: 100%;
          height: 100%;
        }
      }
      ::v-deep .van-uploader__upload {
        width: 75px;
        height: 75px;
        margin-right: 0;
      }
    }
    .submit {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 2005;
      background: #fff;
      height: 57px;
      padding: 7px 15px;
      box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.08);
      .van-button {
        height: 43px;
        font-size: 16px;
      }
    }
  }
}
</style>
