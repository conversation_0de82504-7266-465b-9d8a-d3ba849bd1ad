<template>
  <div class="vol-container">
    <!-- 页面标题 -->
    <el-page-header @back="goBack" content="在线志愿者" class="py-[20px]"> </el-page-header>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入志愿者姓名或职位"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="4">
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="handleRefresh"
          >
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 志愿者表格 -->
    <div class="table-section">
      <el-table
        v-loading="volLoading"
        :data="filteredVolLists"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" label="排名" width="80" align="center">
          <template slot-scope="scope">
            <span class="rank-number">{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="头像" width="100" align="center">
          <template slot-scope="scope">
            <el-avatar
              :size="50"
              :src="scope.row.profile"
              :alt="scope.row.realName"
              @error="handleAvatarError"
            >
              <img :src="avaterSrc" alt="默认头像" />
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column
          label="姓名"
          prop="realName"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">
            <span class="volunteer-name">{{ scope.row.realName | name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="职位"
          prop="post"
          min-width="200"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag type="info" size="medium">{{
              scope.row.post || "暂无职位"
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="用户ID"
          prop="userId"
          width="120"
          align="center"
        />

        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-section">
      <el-pagination
        :current-page="volPageNum"
        :page-size="volPageSize"
        :total="volTotal"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getVolLists } from "@/api/568/home";
import avater from "@/assets/images/honor_avater.png";

export default {
  name: "Vol",
  components: {},
  filters: {
    name(value) {
      if (!value) return "暂无姓名";
      // 如果姓名长度大于2，中间用*代替
      if (value.length > 2) {
        return (
          value.charAt(0) +
          "*".repeat(value.length - 2) +
          value.charAt(value.length - 1)
        );
      }
      return value;
    },
  },
  props: {},
  data() {
    return {
      // 分页参数
      volLoading: false,
      volPageNum: 1,
      volPageSize: 10,
      volTotal: 0,
      // 默认头像
      avaterSrc: avater,
      volLists: [],
      // 搜索关键词
      searchKeyword: "",
      // 原始数据备份
      originalVolLists: [],
    };
  },
  computed: {
    // 过滤后的志愿者列表
    filteredVolLists() {
      if (!this.searchKeyword) {
        return this.volLists;
      }
      return this.volLists.filter((item) => {
        const name = item.realName || "";
        const post = item.post || "";
        const keyword = this.searchKeyword.toLowerCase();
        return (
          name.toLowerCase().includes(keyword) ||
          post.toLowerCase().includes(keyword)
        );
      });
    },
  },
  watch: {},
  mounted() {
    this.loadVol();
  },
  created() {},
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 获取志愿榜列表数据
    async loadVol() {
      this.volLoading = true;
      try {
        const data = await getVolLists({
          pageNum: this.volPageNum,
          pageSize: this.volPageSize,
          status: 1,
        });

        // 处理头像路径
        data.rows.forEach((item) => {
          let reg = /^\/.*/;
          item.profile =
            item.profile && reg.test(item.profile)
              ? `${process.env.VUE_APP_BASE_API}${item.profile}`
              : item.headpicture
              ? item.headpicture
              : this.avaterSrc;
        });

        this.volLists = data.rows;
        this.originalVolLists = [...data.rows]; // 备份原始数据
        this.volTotal = data.total;
      } catch (error) {
        console.error("获取志愿者列表失败:", error);
        this.$message.error("网络异常，请重试！");
      } finally {
        this.volLoading = false;
      }
    },

    // 处理分页大小变化
    handleSizeChange(newSize) {
      this.volPageSize = newSize;
      this.volPageNum = 1;
      this.loadVol();
    },

    // 处理当前页变化
    handleCurrentChange(newPage) {
      this.volPageNum = newPage;
      this.loadVol();
    },

    // 处理搜索
    handleSearch() {
      // 搜索功能通过computed属性filteredVolLists实现
      // 这里可以添加防抖逻辑
    },

    // 刷新数据
    handleRefresh() {
      this.searchKeyword = "";
      this.volPageNum = 1;
      this.loadVol();
      this.$message.success("刷新成功");
    },

    // 查看详情
    handleViewDetail(row) {
      this.$message.info(`查看志愿者 ${row.realName} 的详细信息`);
      // 这里可以跳转到详情页面或打开详情弹窗
      // this.$router.push({ path: '/volunteer/detail', query: { id: row.userId } });
    },

    // 头像加载失败处理
    handleAvatarError() {
      return true; // 使用默认头像
    },
  },
};
</script>

<style lang="scss" scoped>
.vol-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 10px 0;
      font-size: 28px;
      font-weight: 600;
    }

    p {
      margin: 0;
      font-size: 16px;
      opacity: 0.9;
    }
  }

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .table-section {
    background: white;

    .rank-number {
      font-weight: bold;
      color: #409eff;
      font-size: 16px;
    }

    .volunteer-name {
      font-weight: 600;
      color: #303133;
    }

    // 排名前三的特殊样式
    ::v-deep .el-table__row:nth-child(1) .rank-number {
      color: #ffd700;
      font-size: 18px;
    }

    ::v-deep .el-table__row:nth-child(2) .rank-number {
      color: #c0c0c0;
      font-size: 17px;
    }

    ::v-deep .el-table__row:nth-child(3) .rank-number {
      color: #cd7f32;
      font-size: 16px;
    }
  }

  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// Element UI 表格样式优化
::v-deep .el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        font-weight: 600;
        font-size: 14px;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vol-container {
    padding: 10px;

    .page-header {
      padding: 20px;

      h2 {
        font-size: 24px;
      }
    }

    .search-section {
      padding: 15px;
    }
  }
}
</style>
