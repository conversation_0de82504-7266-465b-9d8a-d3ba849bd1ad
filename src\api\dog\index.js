import request from '@/utils/request'
import Qs from 'qs'

// 本地环境
export const base = process.env.VUE_APP_BASE_API
// export const websocketUrl = 'ws://localhost:8686/websocket/'

// export const base = 'http://pet.chinau8.com/api'
// export const websocketUrl = 'ws://pet.chinau8.com/api/websocket/'

// export const base = 'https://www.jinhuadog.com/api/dog'

export const websocketUrl = 'wss://www.jinhuadog.com/api/dog/websocket/'
// axios.defaults.withCredentials = true

export function getDownLoadUrl(path) {
  return base + '/sysUploadFile/downloadLocalFile?path=' + encodeURI(path)
}

function initToken(params) {
  var user = JSON.parse(sessionStorage.getItem('user'))
  if (user) {
    params.token = user.token
  }
}

// post请求调用
export function doPost(url, params) {
  if (params === undefined) {
    params = {}
  }
  initToken(params)
  return request
    .post(url, Qs.stringify(params), {
      headers: initHeaders(),
    })
    .then((res) => res.data)
}

// get请求调用
export function doGet(url, params) {
  if (params === undefined) {
    params = {}
  }
  initToken(params)
  console.log('doGet', params)
  return request
    .get(url, {
      params,
      headers: initHeaders(),
    })
    .then((res) => res.data)
}

// post请求下载附件
export function download(url, params, fileName) {
  if (params === undefined) {
    params = {}
  }
  var user = JSON.parse(sessionStorage.getItem('user'))
  if (user) {
    params.token = user.token
  }
  return request({
    url: url,
    method: 'post',
    data: Qs.stringify(params),
    responseType: 'blob',
    headers: initHeaders(),
  }).then((res) => {
    // console.log(res.data)
    const blob = new Blob([res.data], { type: 'charset=UTF-8' })
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    downloadElement.href = href
    downloadElement.download = fileName
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
    window.URL.revokeObjectURL(href)
  })
}

function initHeaders() {
  var headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
  return headers
}

/**
 * 响应拦截器  验证后端返回是否登录超期  超期后跳转首页
 */
// axios.interceptors.response.use((response) => {
//   console.log('相应拦截器===》')
//   console.log(response.data)
//   if (response.data.code && response.data.code === 201) {
//     // 后端session超期  跳转首页重新登录
//     console.log('用户登录信息失效，请重新登录')
//     window.location.href = '/login?toLogin=1'
//   }
//   return response
// })

export function formatTime(format, date) {
  // format为返回的时间格式，例如yyyy-MM-dd HH:mm:ss,其中yyyy等时间格式限定，不能更改，'-'等符号可以改动，如yyyy/MM/dd
  var now = new Date(date)

  var year = now.getFullYear() // 年
  var month = now.getMonth() + 1 // 月
  var day = now.getDate() // 日

  var hh = now.getHours() // 时
  var mm = now.getMinutes() // 分
  var ss = now.getSeconds()

  var clock = ''

  if (format.indexOf('yyyy') >= 0) {
    clock += year
    if (format.length > 6) {
      clock += format.substring(4, 5)
    }
  }

  if (format.indexOf('MM') >= 0) {
    if (month < 10) {
      clock += '0'
    }
    clock += month
    if (format.length > 9) {
      clock += format.substring(7, 8)
    }
  }

  if (format.indexOf('dd') >= 0) {
    if (day < 10) {
      clock += '0'
    }
    clock += day
    if (format.length > 12) {
      clock += format.substring(10, 11)
    }
  }

  if (format.indexOf('HH') >= 0) {
    if (hh < 10) {
      clock += '0'
    }
    clock += hh
    if (format.length > 15) {
      clock += format.substring(13, 14)
    }
  }

  if (format.indexOf('mm') >= 0) {
    if (mm < 10) {
      clock += '0'
    }
    clock += mm
    if (format.length > 18) {
      clock += format.substring(16, 17)
    }
  }

  if (format.indexOf('ss') >= 0) {
    if (ss < 10) {
      clock += '0'
    }
    clock += ss
  }

  return clock
}

export function formatTimeMonth(format, date, monthNum) {
  // format为返回的时间格式，例如yyyy-MM-dd HH:mm:ss,其中yyyy等时间格式限定，不能更改，'-'等符号可以改动，如yyyy/MM/dd
  var now = new Date(date)

  var year = now.getFullYear() // 年
  var month = now.getMonth() + 1 + monthNum // 月
  var day = now.getDate() // 日

  var hh = now.getHours() // 时
  var mm = now.getMinutes() // 分
  var ss = now.getSeconds()

  var clock = ''

  if (format.indexOf('yyyy') >= 0) {
    clock += year
    if (format.length > 6) {
      clock += format.substring(4, 5)
    }
  }

  if (format.indexOf('MM') >= 0) {
    if (month < 10) {
      clock += '0'
    }
    clock += month
    if (format.length > 9) {
      clock += format.substring(7, 8)
    }
  }

  if (format.indexOf('dd') >= 0) {
    if (day < 10) {
      clock += '0'
    }
    clock += day
    if (format.length > 12) {
      clock += format.substring(10, 11)
    }
  }

  if (format.indexOf('HH') >= 0) {
    if (hh < 10) {
      clock += '0'
    }
    clock += hh
    if (format.length > 15) {
      clock += format.substring(13, 14)
    }
  }

  if (format.indexOf('mm') >= 0) {
    if (mm < 10) {
      clock += '0'
    }
    clock += mm
    if (format.length > 18) {
      clock += format.substring(16, 17)
    }
  }

  if (format.indexOf('ss') >= 0) {
    if (ss < 10) {
      clock += '0'
    }
    clock += ss
  }

  return clock
}

export function formatTimeCount(
  format,
  date,
  yearNum,
  monthNum,
  dayNum,
  hourNum,
  minutesNum,
  secondsNum
) {
  // format为返回的时间格式，例如yyyy-MM-dd HH:mm:ss,其中yyyy等时间格式限定，不能更改，'-'等符号可以改动，如yyyy/MM/dd
  var now = new Date(date)

  var year = now.getFullYear() + Number(yearNum) // 年
  var month = now.getMonth() + 1 + Number(monthNum) // 月
  var day = now.getDate() + Number(dayNum) // 日

  var hh = now.getHours() + Number(hourNum) // 时
  var mm = now.getMinutes() + Number(minutesNum) // 分
  var ss = now.getSeconds() + Number(secondsNum)

  var clock = ''

  if (format.indexOf('yyyy') >= 0) {
    clock += year
    if (format.length > 6) {
      clock += format.substring(4, 5)
    }
  }

  if (format.indexOf('MM') >= 0) {
    if (month < 10) {
      clock += '0'
    }
    clock += month
    if (format.length > 9) {
      clock += format.substring(7, 8)
    }
  }

  if (format.indexOf('dd') >= 0) {
    if (day < 10) {
      clock += '0'
    }
    clock += day
    if (format.length > 12) {
      clock += format.substring(10, 11)
    }
  }

  if (format.indexOf('HH') >= 0) {
    if (hh < 10) {
      clock += '0'
    }
    clock += hh
    if (format.length > 15) {
      clock += format.substring(13, 14)
    }
  }

  if (format.indexOf('mm') >= 0) {
    if (mm < 10) {
      clock += '0'
    }
    clock += mm
    if (format.length > 18) {
      clock += format.substring(16, 17)
    }
  }

  if (format.indexOf('ss') >= 0) {
    if (ss < 10) {
      clock += '0'
    }
    clock += ss
  }

  return clock
}
