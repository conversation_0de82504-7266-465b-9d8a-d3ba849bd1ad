<template>
  <div class="news-container">
    <!-- 页面头部 -->
    <el-page-header @back="goBack" content="服务动态" class="py-[20px]">
    </el-page-header>

    <!-- 搜索和筛选区域 -->
    <!-- <el-card class="search-card" shadow="never">
      <div slot="header" class="card-header">
        <span class="card-title">动态筛选</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索动态标题或内容"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="selectedStatus"
            placeholder="选择状态"
            clearable
            @change="handleStatusChange"
          >
            <el-option label="全部" value="" />
            <el-option label="已发布" value="9" />
            <el-option label="审批中" value="2" />
            <el-option label="新建" value="1" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="sortOrder"
            placeholder="排序方式"
            @change="handleSortChange"
          >
            <el-option label="最新发布" value="desc" />
            <el-option label="最早发布" value="asc" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" icon="el-icon-refresh" @click="handleRefresh">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card> -->

    <!-- 动态列表 -->
    <div v-loading="loading" class="news-content">
      <!-- 列表视图 -->
      <div class="list-view">
        <el-table
          :data="newsLists"
          style="width: 100%"
          @row-click="handleRowClick"
        >
          <el-table-column label="标题" min-width="200">
            <template slot-scope="scope">
              <div class="title-cell">
                <img
                  :src="scope.row.newsImg || defaultNewsImg"
                  :alt="scope.row.title"
                  class="table-image"
                  @error="handleImageError"
                />
                <span class="title-text">{{ scope.row.title || scope.row.serveTypeName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="志愿者" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.createBy | name }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="发布时间" min-width="180" />
          <el-table-column prop="thumbsNum" label="点赞数" min-width="100">
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-view"
                @click.stop="handleNewsClick(scope.row, scope.$index)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-show="newsTotal > 0"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="newsTotal"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && !newsLists.length"
        description="暂无服务动态"
        :image-size="120"
      >
        <el-button type="primary" @click="handleRefresh">刷新数据</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { newsLists } from "@/api/568/home";
import defaultNewsImg from "@/assets/images/newsImg.jpg";

export default {
  name: "NewsIndex",
  data() {
    return {
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      newsTotal: 0,

      // 数据
      newsLists: [],
      loading: false,

      // 默认图片
      defaultNewsImg: defaultNewsImg,
    };
  },

  created() {
    this.loadNewsLists();
  },

  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 加载动态列表
    async loadNewsLists() {
      try {
        this.loading = true;

        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          status: 9, // 已发布状态
          approveType: 0, // 审批通过
        };

        const data = await newsLists(params);

        if (data.code !== 200) {
          this.$message.error("获取动态列表失败");
          return;
        }

        // 处理数据
        const listData = data.rows.map(item => {
          if (item.imgs && item.imgs[0] && item.imgs[0].filePath) {
            item.newsImg = `${process.env.VUE_APP_BASE_API}${item.imgs[0].filePath}`;
          }
          return item;
        });

        this.newsLists = listData;
        this.newsTotal = data.total;

      } catch (error) {
        console.error("获取动态列表失败:", error);
        this.$message.error("网络异常，请重试！");
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    handleRefresh() {
      this.pageNum = 1;
      this.loadNewsLists();
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pageSize = size;
      this.pageNum = 1;
      this.loadNewsLists();
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pageNum = page;
      this.loadNewsLists();
    },

    // 点击动态项
    handleNewsClick(item, index) {
      // 将完整的item数据存储到sessionStorage，以便详情页面使用
      sessionStorage.setItem('currentNewsItem', JSON.stringify(item));

      this.$router.push({
        path: `/568/news-detail/${item.id}`,
        query: {
          newsIndex: index,
        },
      });
    },

    // 表格行点击
    handleRowClick(row) {
      const index = this.newsLists.findIndex(item => item.id === row.id);
      this.handleNewsClick(row, index);
    },

    // 图片加载错误处理
    handleImageError(event) {
      event.target.src = this.defaultNewsImg;
    },
  },
};
</script>

<style lang="scss" scoped>
.news-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 50px);



  // 动态内容区域
  .news-content {
    min-height: 400px;

    // 列表视图
    .list-view {
      ::v-deep .el-table {
        .title-cell {
          display: flex;
          align-items: center;
          gap: 10px;

          .table-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            background-color: #f5f7fa;
            flex-shrink: 0;
          }

          .title-text {
            flex: 1;
            font-weight: 500;
            color: #303133;
          }
        }

        .el-table__row {
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }

    // 分页样式
    .pagination-wrapper {
      margin-top: 30px;
      text-align: center;

      ::v-deep .el-pagination {
        .el-pager li {
          border-radius: 4px;
          margin: 0 2px;
        }

        .btn-prev,
        .btn-next {
          border-radius: 4px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-container {
    padding: 10px;
  }
}
</style>
