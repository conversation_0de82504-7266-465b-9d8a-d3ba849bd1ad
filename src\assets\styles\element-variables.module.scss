/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #1890ff;
$--color-success: #13ce66;
$--color-warning: #ffba00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;

//修改字体大小
$--font-size-base: 14px !default;
$--font-size-small: 15px !default;
$--font-size-large: 20px !default;
$--font-line-height-primary: 26px !default;
$--checkbox-font-size: 15px !default;
$--radio-font-size: 15px !default;
$--select-input-font-size: 15px !default;
$--select-group-font-size: 13px !default;
$--alert-title-font-size: 14px !default;
$--alert-description-font-size: 13px !default;
$--alert-close-font-size: 13px !default;
$--alert-close-customed-font-size: 14px !default;
$--msgbox-error-font-size: 13px !default;
$--notification-title-font-size: 14px !default;
$--input-medium-font-size: 15px !default;
$--input-small-font-size: 14px !default;
$--input-mini-font-size: 13px !default;
$--button-font-size: 15px !default;
$--button-medium-font-size: 15px !default;
$--button-small-font-size: 13px !default;
$--button-mini-font-size: 13px !default;
$--dialog-font-size: 15px !default;
$--pagination-font-size: 14px !default;
$--popover-title-font-size: 14px !default;
$--tooltip-font-size: 13px !default;
$--tag-font-size: 13px !default;
$--badge-font-size: 13px !default;
$--carousel-arrow-font-size: 13px !default;
/* Size
-------------------------- */
$--size-base: 14px !default;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid#dfe6ec;

/* menu
-----------------------*/
$--menu-item-color: #303133;

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import '~element-ui/packages/theme-chalk/src/index';

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
