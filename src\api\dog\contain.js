import request from '@/utils/request'

/**
 * 审核
 */
export function updateStatus(data) {
  return request({
    url: '/takeIn/updateStatus',
    method: 'post',
    data: data,
  })
}

/**
 * 领回登记
 */
export function takeInRegister(data) {
  return request({
    url: '/takeIn/register',
    method: 'post',
    data: data,
  })
}

/**
 * 短信提醒
 */
export function sendMobile(id) {
  return request({
    url: `/takeIn/sendMobile/${id}`,
    method: 'get',
  })
}

/**
 * 认领申请
 */
export function updateApply(data) {
  return request({
    url: '/reclaimRecord/updateApply',
    method: 'post',
    data: data,
  })
}

/**
 * 认领登记
 */
export function updateRegist(data) {
  return request({
    url: '/reclaimRecord/updateRegist',
    method: 'post',
    data: data,
  })
}

/**
 * 需要被无害化处理列表
 */
export function getNeedHarmList(params) {
  return request({
    url: '/harmTrea/getNeedHarmListV2',
    method: 'get',
    params: params,
  })
}

/**
 * 无害化处理
 */
export function harmlessProcess(data) {
  return request({
    url: '/harmTrea/saveOrUpdate',
    method: 'post',
    data: data,
  })
}

/**
 * 收容签收
 */
export function takeInSign(params) {
  return request({
    url: '/takeIn/sign',
    method: 'post',
    params: params,
  })
}

/**
 * 批量处理
 */
export function batchHandle(data) {
  return request({
    url: '/harmTrea/batchHandle',
    method: 'post',
    data: data,
  })
}
