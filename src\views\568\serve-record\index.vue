<template>
  <div class="serve-record-container">
    <el-page-header @back="goBack" content="服务记录管理" class="py-[20px]">
    </el-page-header>
    <!-- 页面标题和操作栏 -->

    <div class="flex justify-end mb-4" v-if="!volUserId">
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新建服务
      </el-button>
    </div>

    <!-- 搜索筛选区域 -->
    <!-- <el-card class="filter-card" shadow="never">
      <el-form :model="queryParams" :inline="true"size="small" class="filter-form">
        <el-form-item label="服务者">
          <el-input
            v-model="queryParams.volUserName"
            placeholder="请输入服务者姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select
            v-model="queryParams.serveType"
            placeholder="请选择服务类型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in serveTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务时间">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card> -->

    <!-- 数据表格 -->

    <el-table
      v-loading="loading"
      :data="servicingLists"
      stripe
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column
        prop="serveTypeName"
        label="服务类型"
        min-width="120"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag :type="getServeTypeTagType(scope.row.serveType)">
            {{ scope.row.serveTypeName }}服务
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="volUserName"
        label="服务者"
        min-width="120"
        align="center"
      >
        <template slot-scope="scope">
          <div class="user-info">
            <i
              class="el-icon-user-solid"
              style="margin-right: 5px; color: #909399"
            ></i>
            {{ scope.row.volUserName | name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="title"
        label="服务概述"
        min-width="200"
        show-overflow-tooltip
      />

      <el-table-column
        prop="happenTime"
        label="服务时间"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <i class="el-icon-time" style="margin-right: 5px; color: #909399"></i>
          {{ scope.row.happenTime }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            type="text"
            size="small"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑服务弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <add-serve-form
        v-if="dialogVisible"
        ref="addServeForm"
        :form-data="currentRow"
        :is-edit="isEdit"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      title="服务记录详情"
      :visible.sync="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <serve-detail
        v-if="detailDialogVisible"
        :serve-id="currentServeId"
        @close="detailDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { newsLists } from "@/api/568/home";
import { getCase } from "@/api/568/index";
import AddServeForm from "./AddServeForm.vue";
import ServeDetail from "./ServeDetail.vue";

export default {
  name: "ServeRecord",
  components: {
    AddServeForm,
    ServeDetail,
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 服务记录列表
      servicingLists: [],
      // 分页信息
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        volUserName: "",
        serveType: "",
        dateRange: [],
        type: 1, // 1-服务记录
        status: 9, // 9-同意
        approveType: 0, // 0-通过
      },
      // 服务类型选项
      serveTypeOptions: [],
      // 弹窗控制
      dialogVisible: false,
      detailDialogVisible: false,
      isEdit: false,
      currentRow: null,
      currentServeId: null,
      // 志愿者ID（从路由参数获取）
      volUserId: null,
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? "编辑服务记录" : "新建服务记录";
    },
  },
  watch: {},
  async created() {
    // 荣誉榜传进来的志愿者id
    this.volUserId = this.$route.query.volUserId;

    // 加载服务类型字典
    await this.loadServeTypes();

    // 加载服务记录列表
    this.getList();

    // 监听刷新事件
    this.$bus.$off("reLoadserveList");
    this.$bus.$on("reLoadserveList", () => {
      this.getList();
    });
  },
  activated() {
    // 荣誉榜传进来的志愿者id
    this.volUserId = this.$route.query.volUserId;
    this.getList();
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    // 加载服务类型字典
    async loadServeTypes() {
      try {
        const { data } = await getCase("vol_appointment_type");
        this.serveTypeOptions = data.map((item) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }));
      } catch (error) {
        console.error("加载服务类型失败:", error);
      }
    },

    // 获取服务记录列表
    async getList() {
      try {
        this.loading = true;

        let params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          type: this.queryParams.type,
          status: this.queryParams.status,
          approveType: this.queryParams.approveType,
        };

        // 添加搜索条件
        if (this.queryParams.volUserName) {
          params.volUserName = this.queryParams.volUserName;
        }
        if (this.queryParams.serveType) {
          params.serveType = this.queryParams.serveType;
        }
        if (
          this.queryParams.dateRange &&
          this.queryParams.dateRange.length === 2
        ) {
          params.startTime = this.queryParams.dateRange[0];
          params.endTime = this.queryParams.dateRange[1];
        }

        // 如果有志愿者ID参数，添加到查询条件
        if (this.volUserId) {
          params.volUserId = this.volUserId;
        }

        const result = await newsLists(params);

        if (result && result.rows) {
          this.servicingLists = result.rows;
          this.total = result.total || 0;
        } else {
          this.servicingLists = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("获取服务记录列表失败:", error);
        this.$message.error("获取服务记录列表失败，请重试！");
        this.servicingLists = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        volUserName: "",
        serveType: "",
        dateRange: [],
        type: 1,
        status: 9,
        approveType: 0,
      };
      this.getList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },

    // 新建服务
    handleCreate() {
      this.isEdit = false;
      this.currentRow = null;
      this.dialogVisible = true;
    },

    // 编辑服务
    handleEdit(row) {
      this.isEdit = true;
      this.currentRow = { ...row };
      this.dialogVisible = true;
    },

    // 查看详情
    handleDetail(row) {
      this.currentServeId = row.id;
      this.detailDialogVisible = true;
    },

    // 弹窗关闭
    handleDialogClose() {
      this.dialogVisible = false;
      this.currentRow = null;
      this.isEdit = false;
    },

    // 表单提交成功
    handleFormSuccess() {
      this.dialogVisible = false;
      this.getList();
      this.$message.success(this.isEdit ? "编辑成功！" : "新建成功！");
    },

    // 获取服务类型标签类型
    getServeTypeTagType(serveType) {
      const typeMap = {
        1: "primary",
        2: "success",
        3: "warning",
        4: "danger",
        5: "info",
      };
      return typeMap[serveType] || "primary";
    },
  },
};
</script>

<style lang="scss" scoped>
.serve-record-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 50px);

  .header-card {
    margin-bottom: 20px;
    border: none;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #303133;
        }

        .page-subtitle {
          margin: 0;
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;
    border: none;

    .filter-form {
      margin-bottom: 0;

      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .table-card {
    border: none;

    .el-table {
      .user-info {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .el-button--text {
        padding: 0;
        margin: 0 5px;

        &:hover {
          color: #409eff;
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

// Element UI 弹窗样式覆盖
::v-deep .el-dialog {
  .el-dialog__header {
    background: #f5f7fa;
    padding: 20px 20px 10px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .serve-record-container {
    padding: 10px;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .filter-form {
      .el-form-item {
        display: block;
        margin-bottom: 15px;

        .el-input,
        .el-select,
        .el-date-picker {
          width: 100% !important;
        }
      }
    }

    .el-table {
      font-size: 12px;

      .el-table__cell {
        padding: 8px 0;
      }
    }

    .pagination-container {
      text-align: center;

      .el-pagination {
        justify-content: center;
      }
    }
  }
}
</style>
