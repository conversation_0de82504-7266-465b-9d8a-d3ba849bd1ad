import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    component: () => import('@/views/layout'),
    children: [
      { // 首页
        name: 'Home',
        path: '', // 默认子路由
        component: () => import('@/views/home'),
        meta: { title: '568我来帮' }
      },
      { // 志愿地图
        name: 'Map',
        path: '/map',
        component: () => import('@/views/map'),
        meta: { title: '我要参加' }
      },
      { // 我的
        name: 'Mine',
        path: '/mine',
        component: () => import('@/views/mine'),
        meta: { title: '我的' }
      }
    ]
  },
  { // 服务预约列表
    name: 'Appointment',
    path: '/appointment',
    component: () => import('@/views/appointment'),
    meta: { title: '服务预约列表' }
  },
  { // 新增助老助残
    name: 'AddAppointment',
    path: '/add-appointment',
    component: () => import('@/views/add-appointment'),
    meta: { title: '新增助老助残' }
  },
  { // 服务预约-接受预约
    name: 'AppointmentDetail',
    path: '/appointment-detail/:id',
    component: () => import('@/views/appointment-detail'),
    props: true,
    meta: { title: '接受预约' }
  },
  { // 服务预约-接受预约-选择服务者
    name: 'ChooseServers',
    path: '/choose-servers',
    component: () => import('@/views/choose-servers'),
    meta: { title: '选择服务者' }
  },
  { // 流动书吧
    name: 'Book',
    path: '/book',
    component: () => import('@/views/book'),
    meta: { title: '流动书吧' }
  },
  { // 分类书籍列表
    name: 'ClassifyBook',
    path: '/classify-book',
    component: () => import('@/views/classify-book'),
    meta: { cancalTitle: true, title: '分类书籍列表' }
  },
  { // 借书送书
    name: 'Record',
    path: '/record/:volBookId',
    component: () => import('@/views/record'),
    props: true,
    meta: { cancalTitle: true, title: '借书送书' }
  },
  { // 书籍详情页
    name: 'BookDetail',
    path: '/book-detail/:volBookId',
    component: () => import('@/views/book-detail'),
    props: true,
    meta: { title: '书籍详情' }
  },
  { // 我的举报
    name: 'Offs',
    path: '/offs',
    component: () => import('@/views/offs'),
    meta: { title: '我的建议' }
  },
  { // 我的建议
    name: 'AddOffs',
    path: '/add-offs',
    component: () => import('@/views/add-offs'),
    meta: { title: '我的建议' }
  },
  { // 荣誉榜
    name: 'Honor',
    path: '/honor',
    component: () => import('@/views/honor'),
    meta: { title: '荣誉榜' }
  },
  { // 在线志愿者
    name: 'Vol',
    path: '/vol',
    component: () => import('@/views/vol'),
    meta: { title: '在线志愿者' }
  },
  { // 服务条款
    name: 'Clause',
    path: '/clause',
    component: () => import('@/views/clause'),
    meta: { title: '服务条款' }
  },
  { // 加入我们
    name: 'Join',
    path: '/join',
    component: () => import('@/views/join'),
    meta: { title: '加入我们' }
  },
  { // 加入我们 审核
    name: 'JoinApprove',
    path: '/join-approve',
    component: () => import('@/views/join-approve'),
    meta: { title: '加入我们审核' }
  },
  { // 我要赞美
    name: 'Praise',
    path: '/praise',
    component: () => import('@/views/praise'),
    meta: { title: '我要赞美' }
  },
  { // 补卡列表
    name: 'RepairSign',
    path: '/repair-sign',
    component: () => import('@/views/repair-sign'),
    meta: { title: '打卡列表' }
  },
  { // 补卡
    name: 'AddRepairSign',
    path: '/add-repair-sign/:id',
    component: () => import('@/views/add-repair-sign'),
    props: true,
    meta: { title: '补卡' }
  },
  { // 服务记录列表页
    name: 'Serve',
    path: '/serve',
    component: () => import('@/views/serve'),
    meta: { title: '服务记录' }
  },
  { // 添加服务记录
    name: 'AddServe',
    path: '/add-serve',
    component: () => import('@/views/add-serve'),
    meta: { title: '新增服务记录' }
  },
  { // 我的服务详情页
    name: 'ServeDetail',
    path: '/serve-detail/:taskId',
    component: () => import('@/views/serve-detail'),
    props: true,
    meta: { title: '服务详情' }
  },
  { // 站前动态详情页
    name: 'NewsDetail',
    path: '/news-detail/:newsId',
    component: () => import('@/views/news-detail'),
    props: true,
    meta: { title: '动态详情' }
  },
  { // 服务监督-审核
    name: 'SuperviseReview',
    path: '/supervise-review/:taskId',
    component: () => import('@/views/supervise-review'),
    props: true,
    meta: { title: '服务监督-审核' }
  },
  { // 服务监督-评价
    name: 'SuperviseEvaluate',
    path: '/supervise-evaluate/:taskId',
    component: () => import('@/views/supervise-evaluate'),
    props: true,
    meta: { title: '服务监督-评价' }
  },
  { // 服务监督-监督
    name: 'SuperviseBreak',
    path: '/supervise-break/:taskId',
    component: () => import('@/views/supervise-break'),
    props: true,
    meta: { title: '服务监督-监督' }
  },
  { // 服务监督-志愿者评价
    name: 'VolEvaluate',
    path: '/vol-evaluate/:volUserId',
    component: () => import('@/views/vol-evaluate'),
    props: true,
    meta: { title: '志愿者评价' }
  },
  { // 服务监督-志愿者监督记录
    name: 'VolBreak',
    path: '/vol-break/:volUserId',
    component: () => import('@/views/vol-break'),
    props: true,
    meta: { title: '志愿者监督记录' }
  },
  { // 更改用户头像
    name: 'Profile',
    path: '/profile',
    component: () => import('@/views/profile'),
    meta: { title: '头像' }
  },
  { // 我的页面-我的任务
    name: 'MineService',
    path: '/mine-service',
    component: () => import('@/views/mine-service'),
    meta: { title: '我的任务' }
  },
  { // 我的页面-我的评价
    name: 'MineEvaluate',
    path: '/mine-evaluate',
    component: () => import('@/views/mine-evaluate'),
    meta: { title: '我的评价' }
  },
  { // 我的页面-我的评价-评价详情
    name: 'MineEvaluateDetail',
    path: '/mine-evaluate-detail/:id',
    component: () => import('@/views/mine-evaluate-detail'),
    props: true,
    meta: { title: '评价详情' }
  },
  { // 我的页面-积分兑换
    name: 'MineScore',
    path: '/mine-score',
    component: () => import('@/views/mine-score'),
    meta: { title: '积分好礼' }
  },
  { // 我的页面-审批事项
    name: 'ReviewApply',
    path: '/review-apply',
    component: () => import('@/views/review-apply'),
    meta: { title: '审批事项' }
  },
  { // 任务报名-审批进度
    name: 'TaskEnter',
    path: '/task-enter',
    component: () => import('@/views/task-enter'),
    meta: { title: '任务报名-审批进度' }
  },
  { // 助老助残-流程管理
    name: 'AppointmentFlow',
    path: '/appointment-flow/:id',
    component: () => import('@/views/appointment-flow'),
    meta: { title: '助老助残-流程管理' }
  },
  { // 助老助残-审批进度
    name: 'AppointmentApprove',
    path: '/appointment-approve/:id',
    component: () => import('@/views/appointment-approve'),
    meta: { title: '助老助残-审批进度' }
  },
  { // 助老助残-审批进度
    name: 'AppointmentInstructions',
    path: '/appointment-instructions',
    component: () => import('@/views/appointment-instructions'),
    meta: { title: '服务说明' }
  }
]

const router = new VueRouter({
  routes
})

export default router
