import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import ChildLayout from "@/layout/ChildLayout";
import DogLayout from "@/layout/DogLayout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  // 犬类管理
  {
    path: "/dog",
    component: DogLayout,
    redirect: "/dog/index",
    name: "Dog",
    meta: { title: "犬类管理", icon: "agent" },
    children: [
      {
        path: "index",
        component: () => import("@/views/dog/index"),
        name: "DogIndex",
        meta: { title: "犬类管理首页", icon: "dashboard" },
      },
      {
        path: "register",
        component: () => import("@/components/ParentView"),
        redirect: "/dog/register/immune",
        name: "DogRegister",
        meta: { title: "犬只登记", icon: "chart" },
        children: [
          {
            path: "immune",
            component: () => import("@/views/dog/immune/ImmuneRegister"),
            name: "Immune",
            meta: { title: "犬只登记申请", icon: "record" },
          },
          {
            path: "record",
            component: () => import("@/views/dog/immune/AnnualReview"),
            name: "Record",
            meta: { title: "犬只登记年审", icon: "immune" },
          },
          {
            path: "cancel",
            component: () => import("@/views/dog/immune/ImmuneCancel"),
            name: "Cancel",
            meta: { title: "犬只登记注销", icon: "cancal" },
          },
          {
            path: "transfer",
            component: () => import("@/views/dog/immune/ImmuneTransfer"),
            name: "Transfer",
            meta: { title: "犬只登记过户", icon: "transfer" },
          },
          {
            path: "supplement",
            component: () => import("@/views/dog/immune/BrandReissueStatus"),
            name: "Supplement",
            meta: { title: "犬牌补办", icon: "supplement" },
          },
          {
            path: "brandReissue",
            component: () => import("@/views/dog/immune/BrandReissue"),
            name: "BrandReissue",
            meta: { title: "犬牌补办医院", icon: "#" },
          },
        ],
      },
      {
        path: "hospital",
        component: () => import("@/components/ParentView"),
        redirect: "hospital/qualification",
        name: "Hospital",
        meta: { title: "医院管理", icon: "example" },
        children: [
          {
            path: "qualification",
            component: () =>
              import("@/views/dog/hospital/components/HospitalQualification"),
            name: "Qualification",
            meta: { title: "医院代办资质审核", icon: "qualification" },
          },
          {
            path: "agent",
            component: () =>
              import("@/views/dog/hospital/components/HospitalAgent"),
            name: "Agent",
            meta: { title: "医院代办资质年审核", icon: "agent" },
          },
          {
            path: "annualReview",
            component: () =>
              import("@/views/dog/hospital/components/AnnualReviewAdd"),
            name: "AnnualReview",
            meta: { title: "年审提醒通知", icon: "annualReview" },
          },
          {
            path: "hospitalRecord",
            component: () =>
              import("@/views/dog/hospital/components/ApplicationRecord"),
            name: "HospitalRecord",
            meta: { title: "申请记录", icon: "hospitalRecord" },
          },
        ],
      },
    ],
  },

  // 原有的报告页面保留
  {
    path: "/report",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "index",
        component: () => import("@/views/report/index"),
        name: "Report",
        meta: { title: "我要爆料", icon: "message" },
      },
      // 我要爆料
      {
        path: "/BrokeTheNews",
        component: () => import("@/views/report/BrokeTheNews/index"),
        name: "BrokeTheNews",
        hidden: true,
        meta: { title: "我要爆料" },
      },
      {
        path: "/newsHistory",
        component: () =>
          import("@/views/report/BrokeTheNews/childPage/newsHistory"),
        name: "newsHistory",
        hidden: true,
        meta: { title: "我的上报记录" },
      },
      {
        path: "/newsDetail",
        component: () =>
          import("@/views/report/BrokeTheNews/childPage/newsDetail"),
        name: "newsDetail",
        hidden: true,
        meta: { title: "详情" },
      },
    ],
  },
  // 568志愿
  {
    path: "/zqgl",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "index",
        component: () => import("@/views/volunteer/index"),
        name: "Zqgl",
        meta: { title: "568志愿", icon: "star" },
      },
    ],
  },
  // 568志愿服务平台
  {
    path: "/568",
    component: Layout,
    redirect: "/568/index",
    name: "Volunteer568",
    meta: { title: "568志愿服务", icon: "star" },
    children: [
      {
        path: "index",
        component: () => import("@/views/568/index.vue"),
        name: "Volunteer568Index",
        meta: { title: "568志愿服务首页", icon: "star" },
      },
      {
        path: "news",
        component: () => import("@/views/568/news/index.vue"),
        name: "News",
        meta: { title: "568服务动态", icon: "star" },
      },
      {
        path: "news-detail/:newsId",
        component: () => import("@/views/568/news/NewsDetail.vue"),
        name: "NewsDetail",
        hidden: true,
        meta: { title: "动态详情" },
      },
      {
        path: "news-detail/:newsId",
        component: () => import("@/views/568/news/NewsDetail.vue"),
        name: "NewsDetail",
        hidden: true,
        meta: { title: "动态详情" },
      },
      {
        path: "elderly-assistance",
        component: () => import("@/views/568/elderly-assistance/index.vue"),
        name: "ElderlyAssistance",
        meta: { title: "助老助残", icon: "peoples" },
      },
      {
        path: "mobile-library",
        component: () => import("@/views/568/mobile-library/index.vue"),
        name: "MobileLibrary",
        meta: { title: "流动书吧", icon: "education" },
      },
      {
        path: "borrow-book",
        component: () => import("@/views/568/mobile-library/MineBorrow.vue"),
        name: "BorrowBook",
        meta: { title: "我的借阅", icon: "education" },
      },
      {
        path: "praise",
        component: () => import("@/views/568/praise/index.vue"),
        name: "Praise",
        meta: { title: "我要赞美", icon: "like" },
      },
      {
        path: "suggestion",
        component: () => import("@/views/568/suggestion/index.vue"),
        name: "Suggestion",
        meta: { title: "我的建议", icon: "message" },
      },
      {
        path: "join-us",
        component: () => import("@/views/568/join-us/index.vue"),
        name: "JoinUs",
        meta: { title: "加入我们", icon: "user" },
      },
      {
        path: "join",
        component: () => import("@/views/568/join/index.vue"),
        name: "Join",
        meta: { title: "申请志愿", icon: "user" },
      },
      {
        path: "check-in",
        component: () => import("@/views/568/check-in/index.vue"),
        name: "CheckIn",
        meta: { title: "签到签退", icon: "time" },
      },
      {
        path: "serve-record",
        component: () => import("@/views/568/serve-record/index.vue"),
        name: "ServeRecord",
        meta: { title: "服务记录", icon: "documentation" },
      },
      {
        path: "honor-board",
        component: () => import("@/views/568/honor-board/index.vue"),
        name: "HonorBoard",
        meta: { title: "荣誉榜", icon: "star" },
      },
      {
        // 在线志愿者
        name: "Vol",
        path: "/vol",
        component: () => import("@/views/568/vol/index.vue"),
        meta: { title: "在线志愿者" },
      },
      {
        path: "mine",
        component: () => import("@/views/568/mine/index.vue"),
        name: "Mine",
        hidden: true,
        meta: { title: "我的" },
      },
      {
        path: "my-tasks",
        component: () => import("@/views/568/mine/my-tasks/index.vue"),
        name: "MyTasks",
        hidden: true,
        meta: { title: "我的任务" },
      },
      {
        path: "my-evaluations",
        component: () => import("@/views/568/mine/my-evaluations/index.vue"),
        name: "MyEvaluations",
        hidden: true,
        meta: { title: "我的评价" },
      },
      {
        path: "repair-sign",
        component: () => import("@/views/568/mine/repair-sign/index.vue"),
        name: "RepairSign",
        hidden: true,
        meta: { title: "补卡申请" },
      },
      {
        path: "review-apply",
        component: () => import("@/views/568/mine/review-apply/index.vue"),
        name: "ReviewApply",
        hidden: true,
        meta: { title: "审批事项" },
      },
      {
        path: "score-gifts",
        component: () => import("@/views/568/mine/score-gifts/index.vue"),
        name: "ScoreGifts",
        hidden: true,
        meta: { title: "积分好礼" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch((err) => err);
};

export default new Router({
  base: process.env.VUE_APP_YGF_BASE_URL,
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
